import { Link, useLocation } from 'react-router-dom';
import { useVideos } from '@/context/VideoContext';
import { cn } from '@/lib/utils';
import { Home, TrendingUp, Bookmark, History, Settings, Video, Flag } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';

export default function Sidebar() {
  const { categories } = useVideos();
  const { isCreator } = useAuth();
  const { t } = useLanguage();
  const location = useLocation();
  const currentPath = location.pathname;

  // Map of category icons
  const categoryIcons: Record<string, string> = {
    Education: '🎓',
    Business: '💼',
    Technology: '💻',
    Cooking: '🍳',
    Travel: '✈️',
    Music: '🎵',
    Sports: '⚽',
    Gaming: '🎮',
    Health: '🩺',
    Science: '🔬'
  };

  return (
    <div className="w-64 h-[calc(100vh-60px)] flex-shrink-0 hidden md:flex md:flex-col fixed top-[60px] left-0 bg-background">
      {/* Fixed part - Main Navigation */}
      <div className="w-full py-4 px-2 bg-background flex-shrink-0">
        <div className="space-y-1">
          <Link
            to="/"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
              currentPath === "/"
                ? "bg-lingstream-accent/10 text-lingstream-accent"
                : "hover:bg-lingstream-hover"
            )}
          >
            <Home className="h-4 w-4" />
            <span>{t('nav.home')}</span>
          </Link>

          <Link
            to="/trending"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
              currentPath === "/trending"
                ? "bg-lingstream-accent/10 text-lingstream-accent"
                : "hover:bg-lingstream-hover"
            )}
          >
            <TrendingUp className="h-4 w-4" />
            <span>{t('nav.trending')}</span>
          </Link>

          <Link
            to="/report-issue"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors",
              currentPath === "/report-issue"
                ? "bg-lingstream-accent/10 text-lingstream-accent"
                : "hover:bg-lingstream-hover"
            )}
          >
            <Flag className="h-4 w-4" />
            <span>{t('common.report_issue')}</span>
          </Link>
        </div>
      </div>

      {/* Scrollable part - Categories */}
      <div className="flex-1 overflow-y-auto py-4 px-2 bg-background">
        <div>
          <h3 className="px-3 text-xs uppercase text-lingstream-muted font-medium mb-2">
            {t('home.categories')}
          </h3>
          <div className="space-y-1">
            {categories.map((category) => (
              <Link
                key={category}
                to={`/?category=${category.toLowerCase()}`}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-md text-sm transition-colors w-full",
                  location.search === `?category=${category.toLowerCase()}`
                    ? "bg-lingstream-accent/10 text-lingstream-accent"
                    : "hover:bg-lingstream-hover"
                )}
              >
                <span className="text-lg flex-shrink-0">{categoryIcons[category] || '📺'}</span>
                <span className="truncate flex-1">{t(`category.${category.toLowerCase()}`)}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
