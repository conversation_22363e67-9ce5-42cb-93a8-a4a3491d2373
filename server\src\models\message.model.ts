import mongoose, { Schem<PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Message interface extending the base entity
 */
export interface IMessage extends IBaseEntity {
  /**
   * ID of the conversation this message belongs to
   */
  conversationId: string;

  /**
   * ID of the user or creator who sent this message
   */
  senderId: string;

  /**
   * Sender type - identifies whether the message is from a user or creator
   * Used for UI display and permission checks
   */
  senderType: 'user' | 'creator' | 'system';

  /**
   * Content of the message
   */
  content: string;

  /**
   * Original content before translation (if applicable)
   */
  originalContent?: string;

  /**
   * Original language code (if translated)
   */
  originalLanguage?: string;

  /**
   * Format of the message content
   */
  contentType: 'text' | 'voice' | 'image' | 'video' | 'file';

  /**
   * URL to media file (for voice, image, video, file)
   */
  mediaUrl?: string;

  /**
   * MIME type of the media file
   */
  mediaMimeType?: string;

  /**
   * Whether the message has been read by the recipient
   */
  isRead: boolean;

  /**
   * When the message was read by the recipient
   */
  readAt?: Date;

  /**
   * Whether the message has been delivered to the recipient
   */
  isDelivered: boolean;

  /**
   * When the message was delivered to the recipient
   */
  deliveredAt?: Date;

  /**
   * Additional metadata about the message
   */
  metadata: {
    /**
     * Client-generated ID for optimistic updates
     */
    clientId?: string;

    /**
     * Tags applied to this message
     */
    tags?: string[];

    /**
     * Whether this message contains an answer to a question
     */
    isAnswer?: boolean;

    /**
     * Geographic location data (for location messages)
     */
    location?: {
      latitude: number;
      longitude: number;
      address?: string;
    };
  };
}

/**
 * Message schema definition
 */
const MessageSchema = new Schema<IMessage>(
  {
    conversationId: {
      type: String,
      required: true,
      ref: 'Conversation',
      index: true,
    },
    senderId: {
      type: String,
      required: true,
      ref: 'User',
      index: true,
    },
    senderType: {
      type: String,
      enum: ['user', 'creator', 'system'],
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    originalContent: {
      type: String,
    },
    originalLanguage: {
      type: String,
    },
    contentType: {
      type: String,
      enum: ['text', 'voice', 'image', 'video', 'file'],
      default: 'text',
    },
    mediaUrl: {
      type: String,
    },
    mediaMimeType: {
      type: String,
    },
    isRead: {
      type: Boolean,
      default: false,
      index: true,
    },
    readAt: {
      type: Date,
    },
    isDelivered: {
      type: Boolean,
      default: false,
      index: true,
    },
    deliveredAt: {
      type: Date,
    },
    metadata: {
      clientId: {
        type: String,
        index: true,
      },
      tags: {
        type: [String],
        default: [],
      },
      isAnswer: {
        type: Boolean,
        default: false,
      },
      location: {
        latitude: Number,
        longitude: Number,
        address: String,
      },
    },
  },
  { timestamps: true }
);

// Merge with base schema
MessageSchema.add(BaseSchema);

// Add compound indexes for common queries
MessageSchema.index({ conversationId: 1, createdAt: 1 });
MessageSchema.index({ conversationId: 1, isRead: 1 });
MessageSchema.index({ senderId: 1, conversationId: 1 });
MessageSchema.index({ 'metadata.clientId': 1 }, { sparse: true });

// Create and export the Message model
const MessageModel = mongoose.model<IMessage>('Message', MessageSchema);
export default MessageModel;
