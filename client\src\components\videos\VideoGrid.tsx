import React from 'react';
import VideoCard from './VideoCard';
import { Skeleton } from '@/components/ui/skeleton';

interface Video {
  id: string;
  title: string;
  thumbnailUrl: string;
  duration: number;
  createdAt: string;
  stats: {
    views: number;
  };
  channel?: {
    id: string;
    name: string;
    displayName: string;
    avatar?: string;
    isVerified: boolean;
  };
}

interface VideoGridProps {
  videos: Video[];
  isLoading?: boolean;
  emptyMessage?: string;
  hideChannelInfo?: boolean;
  columns?: 'default' | 'compact' | 'wide';
}

const VideoGrid: React.FC<VideoGridProps> = ({
  videos,
  isLoading = false,
  emptyMessage = 'No videos found',
  hideChannelInfo = false,
  columns = 'default',
}) => {
  // Determine grid columns based on the columns prop
  const gridClasses = {
    default: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    compact: 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
    wide: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  }[columns];

  if (isLoading) {
    return (
      <div className={`grid ${gridClasses} gap-4`}>
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="space-y-3">
            <Skeleton className="w-full aspect-video rounded-md" />
            <div className="flex gap-3">
              {!hideChannelInfo && (
                <Skeleton className="h-9 w-9 rounded-full flex-shrink-0" />
              )}
              <div className="flex-1 space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`grid ${gridClasses} gap-4`}>
      {videos.map(video => (
        <VideoCard
          key={video.id}
          video={video}
          hideChannelInfo={hideChannelInfo}
        />
      ))}
    </div>
  );
};

export default VideoGrid;
