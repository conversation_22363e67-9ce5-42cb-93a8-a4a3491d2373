import { FastifyRequest, FastifyReply } from 'fastify';
import { logger } from '../utils/logger';

/**
 * Performance monitoring middleware
 * Logs request duration and other performance metrics
 */
export async function performanceMonitor(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  // Record start time
  const startTime = process.hrtime();
  
  // Continue processing the request
  reply.raw.on('finish', () => {
    // Calculate request duration
    const hrTime = process.hrtime(startTime);
    const duration = hrTime[0] * 1000 + hrTime[1] / 1000000; // Convert to milliseconds
    
    // Log performance metrics
    if (duration > 1000) {
      // Log slow requests as warnings
      logger.warn(`Slow request: ${request.method} ${request.url} took ${duration.toFixed(2)}ms`, {
        method: request.method,
        url: request.url,
        statusCode: reply.statusCode,
        duration,
      });
    } else {
      // Log normal requests as debug
      logger.debug(`Request: ${request.method} ${request.url} took ${duration.toFixed(2)}ms`, {
        method: request.method,
        url: request.url,
        statusCode: reply.statusCode,
        duration,
      });
    }
    
    // Add performance header to response
    reply.header('X-Response-Time', `${duration.toFixed(2)}ms`);
  });
}
