/**
 * Utility functions for video-related operations
 */

/**
 * Extract Engaxe video ID from a URL or direct ID
 * @param url Engaxe URL or video ID
 * @returns Video ID or null if not a valid Engaxe URL or ID
 */
export function extractEngaxeVideoId(url: string): string | null {
  if (!url) return null;

  console.log(`Attempting to extract video ID from input: ${url}`);

  // If it's just a video ID (no slashes, dots, or protocol)
  if (!url.includes('/') && !url.includes('.') && !url.includes(':')) {
    // Check for standard Engaxe ID format (6-7 alphanumeric characters)
    if (/^[a-zA-Z0-9]{6,7}$/.test(url)) {
      console.log(`Input appears to be a standard Engaxe ID: ${url}`);
      return url;
    }
    // We only accept 6-7 character Engaxe IDs now
    else {
      console.log(`Input is not a valid 6-7 character Engaxe ID: ${url}`);
      return null;
    }
  }

  // Engaxe URL patterns - support multiple formats, but only match 6-7 character IDs
  const patterns = [
    // Format: engaxe.com/videos/[id] - only match 6-7 character IDs
    /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/videos\/([a-zA-Z0-9]{6,7})/i,
    // Format: engaxe.com/v/[id] - only match 6-7 character IDs
    /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([a-zA-Z0-9]{6,7})/i,
    // Format: engaxe.com/watch/[id] - only match 6-7 character IDs
    /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/watch\/([a-zA-Z0-9]{6,7})/i,
    // Format: engaxe.com/embed/[id] - only match 6-7 character IDs
    /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/embed\/([a-zA-Z0-9]{6,7})/i
  ];

  // Try each pattern
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      console.log(`Successfully extracted video ID: ${match[1]} using pattern: ${pattern}`);
      return match[1];
    }
  }

  // If no pattern matches, try a simple extraction as a fallback, but only accept 6-7 character IDs
  try {
    // Try to parse as URL first
    const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
    const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

    if (pathParts.length > 0) {
      const lastPart = pathParts[pathParts.length - 1];
      if (lastPart && /^[a-zA-Z0-9]{6,7}$/.test(lastPart) && !lastPart.includes('.') && !lastPart.includes('?')) {
        console.log(`Using URL path extraction, got valid Engaxe ID: ${lastPart}`);
        return lastPart;
      }
    }
  } catch (error) {
    // If URL parsing fails, try simple string splitting
    const parts = url.split('/');
    const lastPart = parts[parts.length - 1];
    if (lastPart && /^[a-zA-Z0-9]{6,7}$/.test(lastPart) && !lastPart.includes('.') && !lastPart.includes('?')) {
      console.log(`Using fallback string extraction, got valid Engaxe ID: ${lastPart}`);
      return lastPart;
    }
  }

  console.log('Failed to extract video ID from input');
  return null;
}

/**
 * Determine if the URL is an Engaxe video URL
 * @param url Video URL or ID
 * @returns 'engaxe' if it's an Engaxe URL or ID, null otherwise
 */
export function getVideoPlatform(url: string): 'engaxe' | null {
  if (!url) return null;

  console.log(`Checking platform for URL: ${url}`);

  // If it's just a video ID (no slashes, dots, or protocol)
  if (!url.includes('/') && !url.includes('.') && !url.includes(':')) {
    // Check for standard Engaxe ID format (6-7 alphanumeric characters)
    if (/^[a-zA-Z0-9]{6,7}$/.test(url)) {
      console.log('Input appears to be a standard Engaxe ID, treating as Engaxe');
      return 'engaxe';
    }
    // We only accept 6-7 character Engaxe IDs now
    else {
      console.log('Input is not a valid 6-7 character Engaxe ID');
      return null;
    }
  }

  try {
    // Check if it's an Engaxe URL
    if (url.includes('engaxe.com')) {
      // Check if it's a video URL (contains /v/, /videos/, /watch/, or /embed/)
      if (
        url.includes('/v/') ||
        url.includes('/videos/') ||
        url.includes('/watch/') ||
        url.includes('/embed/')
      ) {
        console.log('URL identified as Engaxe video URL');
        return 'engaxe';
      }

      // For development purposes, be more lenient
      // If it's an engaxe.com URL and has a path with at least one segment after the domain
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

      if (pathParts.length > 0) {
        console.log('URL identified as potential Engaxe video URL (lenient mode)');
        return 'engaxe';
      }
    }
  } catch (error) {
    console.warn('Error parsing URL:', error);
    // If URL parsing fails but it looks like a valid 6-7 character Engaxe ID
    if (/^[a-zA-Z0-9]{6,7}$/.test(url)) {
      console.log('Input appears to be a valid Engaxe ID after URL parsing failed');
      return 'engaxe';
    }
  }

  console.log('URL not identified as an Engaxe video URL');
  return null;
}

/**
 * Extract video ID from an Engaxe URL
 * @param url Video URL
 * @returns Object with platform and videoId, or null if not an Engaxe URL
 */
export function extractVideoInfo(url: string): { platform: 'engaxe', videoId: string } | null {
  if (!url) {
    console.log('No URL provided to extractVideoInfo');
    return null;
  }

  console.log(`Extracting video info from URL: ${url}`);

  const platform = getVideoPlatform(url);
  if (!platform) {
    console.log('No platform detected for URL');
    return null;
  }

  console.log(`Platform detected: ${platform}`);
  const videoId = extractEngaxeVideoId(url);

  if (!videoId) {
    console.log('Failed to extract video ID');
    return null;
  }

  console.log(`Successfully extracted video info: platform=${platform}, videoId=${videoId}`);
  return { platform, videoId };
}

/**
 * Get flag emoji for a language code
 * @param code Language code (e.g., 'en', 'es', 'fr')
 * @returns Flag emoji for the language
 */
export function getLanguageFlag(code: string): string {
  if (!code) return '🌐';

  // Map language codes to flag emojis
  const flagMap: Record<string, string> = {
    'en': '🇺🇸', // English - USA
    'hi': '🇮🇳', // Hindi - India
    'es': '🇪🇸', // Spanish - Spain
    'fr': '🇫🇷', // French - France
    'de': '🇩🇪', // German - Germany
    'ja': '🇯🇵', // Japanese - Japan
    'zh': '🇨🇳', // Chinese - China
    'ru': '🇷🇺', // Russian - Russia
    'ar': '🇸🇦', // Arabic - Saudi Arabia
    'pt': '🇵🇹', // Portuguese - Portugal
    'it': '🇮🇹', // Italian - Italy
    'nl': '🇳🇱', // Dutch - Netherlands
    'ko': '🇰🇷', // Korean - South Korea
    'tr': '🇹🇷', // Turkish - Turkey
    'pl': '🇵🇱', // Polish - Poland
    'uk': '🇺🇦', // Ukrainian - Ukraine
    'vi': '🇻🇳', // Vietnamese - Vietnam
    'th': '🇹🇭', // Thai - Thailand
    'id': '🇮🇩', // Indonesian - Indonesia
    'ms': '🇲🇾', // Malay - Malaysia
    'bn': '🇧🇩', // Bengali - Bangladesh
    'ta': '🇮🇳', // Tamil - India
    'te': '🇮🇳', // Telugu - India
    'mr': '🇮🇳', // Marathi - India
    'ur': '🇵🇰', // Urdu - Pakistan
    'fa': '🇮🇷', // Persian - Iran
    'he': '🇮🇱', // Hebrew - Israel
    'sv': '🇸🇪', // Swedish - Sweden
    'da': '🇩🇰', // Danish - Denmark
    'fi': '🇫🇮', // Finnish - Finland
    'no': '🇳🇴', // Norwegian - Norway
    'cs': '🇨🇿', // Czech - Czech Republic
    'hu': '🇭🇺', // Hungarian - Hungary
    'ro': '🇷🇴', // Romanian - Romania
    'el': '🇬🇷', // Greek - Greece
    'bg': '🇧🇬', // Bulgarian - Bulgaria
    'hr': '🇭🇷', // Croatian - Croatia
    'sk': '🇸🇰', // Slovak - Slovakia
    'lt': '🇱🇹', // Lithuanian - Lithuania
    'lv': '🇱🇻', // Latvian - Latvia
    'et': '🇪🇪', // Estonian - Estonia
    'sl': '🇸🇮', // Slovenian - Slovenia
  };

  return flagMap[code.toLowerCase()] || '🌐';
}
