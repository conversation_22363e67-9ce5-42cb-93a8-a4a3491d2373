import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { userSearchController } from '../controllers/user-search.controller';
import { authenticate } from '../middleware/auth';
import { ErrorResponseSchema } from '../middleware/swagger';

/**
 * User search routes
 */
export default async function userSearchRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Apply authentication middleware to all routes
  fastify.addHook('preHandler', authenticate);

  // Search users
  fastify.get('/search', {
    schema: {
      tags: ['Users'],
      description: 'Search users by query',
      querystring: Type.Object({
        query: Type.String({ minLength: 2 }),
        page: Type.Optional(Type.Number({ minimum: 1 })),
        limit: Type.Optional(Type.Number({ minimum: 1, maximum: 50 })),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Array(
            Type.Object({
              id: Type.String(),
              username: Type.String(),
              displayName: Type.Optional(Type.String()),
              firstName: Type.Optional(Type.String()),
              lastName: Type.Optional(Type.String()),
              avatar: Type.Optional(Type.String()),
              presence: Type.Object({
                isOnline: Type.Boolean(),
                lastActiveAt: Type.String({ format: 'date-time' }),
              }),
            })
          ),
          pagination: Type.Object({
            total: Type.Number(),
            page: Type.Number(),
            limit: Type.Number(),
            pages: Type.Number(),
          }),
        }),
        400: ErrorResponseSchema,
        401: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, userSearchController.searchUsers);

  // Get recent chat users
  fastify.get('/recent', {
    schema: {
      tags: ['Users'],
      description: 'Get recent chat users',
      querystring: Type.Object({
        limit: Type.Optional(Type.Number({ minimum: 1, maximum: 50 })),
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Array(
            Type.Object({
              id: Type.String(),
              username: Type.String(),
              displayName: Type.Optional(Type.String()),
              firstName: Type.Optional(Type.String()),
              lastName: Type.Optional(Type.String()),
              avatar: Type.Optional(Type.String()),
              presence: Type.Object({
                isOnline: Type.Boolean(),
                lastActiveAt: Type.String({ format: 'date-time' }),
              }),
              conversationId: Type.String(),
              lastMessageAt: Type.String({ format: 'date-time' }),
            })
          ),
        }),
        401: ErrorResponseSchema,
        500: ErrorResponseSchema,
      },
    },
  }, userSearchController.getRecentChatUsers);
}
