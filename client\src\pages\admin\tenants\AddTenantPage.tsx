import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Home, ArrowLeft, Save, X } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { useTheme } from '@/context/ThemeContext';

export default function AddTenantPage() {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    // Basic Tenant Information
    tenantName: '',
    displayName: '',
    tenantId: '',
    description: '',

    // Contact Information
    primaryContactName: '',
    primaryContactEmail: '',
    primaryContactPhone: '',
    billingContactName: '',
    billingContactEmail: '',
    billingContactPhone: '',
    technicalContactName: '',
    technicalContactEmail: '',
    technicalContactPhone: '',

    // Billing & Subscription
    billingPlan: '',
    billingCycle: 'monthly',
    paymentMethod: 'credit_card',
    billingAddress: '',
    taxId: '',
    initialContractDate: '',
    renewalDate: '',

    // Tenant Configuration
    defaultLanguage: 'en',
    defaultTimezone: '',
    dateFormat: 'MM/DD/YYYY',
    currency: 'USD',
    userLimit: '',
    storageLimit: '',
    enableBlog: true,
    enableMembership: true,
    customSmtpEnabled: false,
    smtpHost: '',
    smtpPort: '',
    smtpUsername: '',
    smtpPassword: '',
    smtpEncryption: 'tls',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear error when field is edited
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.tenantName.trim()) {
      newErrors.tenantName = 'Tenant name is required';
    }

    if (!formData.tenantId.trim()) {
      newErrors.tenantId = 'Tenant ID/Subdomain is required';
    } else if (!/^[a-z0-9-]+$/.test(formData.tenantId)) {
      newErrors.tenantId = 'Tenant ID can only contain lowercase letters, numbers, and hyphens';
    }

    if (!formData.primaryContactName.trim()) {
      newErrors.primaryContactName = 'Primary contact name is required';
    }

    if (!formData.primaryContactEmail.trim()) {
      newErrors.primaryContactEmail = 'Primary contact email is required';
    } else if (!/^\S+@\S+\.\S+$/.test(formData.primaryContactEmail)) {
      newErrors.primaryContactEmail = 'Please enter a valid email address';
    }

    if (!formData.billingPlan.trim()) {
      newErrors.billingPlan = 'Billing plan is required';
    }

    if (!formData.initialContractDate.trim()) {
      newErrors.initialContractDate = 'Initial contract date is required';
    }

    if (!formData.defaultTimezone.trim()) {
      newErrors.defaultTimezone = 'Default timezone is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      // In a real app, this would save the tenant data to the backend
      console.log('Form data submitted:', formData);
      alert('Tenant created successfully!');
      navigate('/admin/tenants');
    } else {
      // Scroll to the first error
      const firstErrorField = Object.keys(errors)[0];
      const element = document.querySelector(`[name="${firstErrorField}"]`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  const handleCancel = () => {
    navigate('/admin/tenants');
  };

  return (
    <div className="flex h-screen bg-[#f5f7fb] overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-5xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Add New Tenant</h1>
              <div className="flex space-x-2">
                <button
                  onClick={handleCancel}
                  className="bg-gray-100 text-gray-700 rounded-md px-4 py-2 flex items-center hover:bg-gray-200 transition-colors"
                >
                  <X size={16} className="mr-2" />
                  Cancel
                </button>
                <button
                  onClick={handleSubmit}
                  className="bg-[#38bdf8] text-white rounded-md px-4 py-2 flex items-center hover:bg-blue-600 transition-colors"
                >
                  <Save size={16} className="mr-2" />
                  Save Tenant
                </button>
              </div>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-gray-600 hover:text-gray-900 flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <Link to="/admin/tenants" className="text-gray-600 hover:text-gray-900 flex items-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Tenant Management
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <span className="text-[#38bdf8]">Add New Tenant</span>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-medium">Basic Tenant Information</h2>
                  <p className="text-sm text-gray-500 mt-1">Enter the basic information about the tenant organization.</p>
                </div>

                <div className="p-6 space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tenant Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        name="tenantName"
                        value={formData.tenantName}
                        onChange={handleChange}
                        className={`w-full border ${errors.tenantName ? 'border-red-500' : 'border-gray-300'} rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                        placeholder="Legal/organization name"
                      />
                      {errors.tenantName && (
                        <p className="mt-1 text-sm text-red-500">{errors.tenantName}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">The official legal name of the organization.</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tenant Display Name
                      </label>
                      <input
                        type="text"
                        name="displayName"
                        value={formData.displayName}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Name to show in UI (if different)"
                      />
                      <p className="mt-1 text-xs text-gray-500">The name that will be displayed to users (leave blank to use Tenant Name).</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tenant ID/Subdomain <span className="text-red-500">*</span>
                      </label>
                      <div className="flex items-center">
                        <input
                          type="text"
                          name="tenantId"
                          value={formData.tenantId}
                          onChange={handleChange}
                          className={`w-full border ${errors.tenantId ? 'border-red-500' : 'border-gray-300'} rounded-l-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                          placeholder="unique-identifier"
                        />
                        <span className="bg-gray-100 text-gray-600 px-4 py-2 border border-l-0 border-gray-300 rounded-r-md">
                          .legalaid.com
                        </span>
                      </div>
                      {errors.tenantId && (
                        <p className="mt-1 text-sm text-red-500">{errors.tenantId}</p>
                      )}
                      <p className="mt-1 text-xs text-gray-500">Used for subdomains and as a unique identifier. Use lowercase letters, numbers, and hyphens only.</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tenant Description
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      rows={3}
                      className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Brief description of the tenant"
                    ></textarea>
                    <p className="mt-1 text-xs text-gray-500">A short description of the tenant organization and its purpose.</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-medium">Contact Information</h2>
                  <p className="text-sm text-gray-500 mt-1">Provide contact details for the tenant organization.</p>
                </div>

                <div className="p-6">
                  <div className="mb-8">
                    <h3 className="text-md font-medium mb-4 pb-2 border-b border-gray-200">Primary Contact</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Name <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="text"
                          name="primaryContactName"
                          value={formData.primaryContactName}
                          onChange={handleChange}
                          className={`w-full border ${errors.primaryContactName ? 'border-red-500' : 'border-gray-300'} rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                          placeholder="Full name"
                        />
                        {errors.primaryContactName && (
                          <p className="mt-1 text-sm text-red-500">{errors.primaryContactName}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email <span className="text-red-500">*</span>
                        </label>
                        <input
                          type="email"
                          name="primaryContactEmail"
                          value={formData.primaryContactEmail}
                          onChange={handleChange}
                          className={`w-full border ${errors.primaryContactEmail ? 'border-red-500' : 'border-gray-300'} rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                          placeholder="<EMAIL>"
                        />
                        {errors.primaryContactEmail && (
                          <p className="mt-1 text-sm text-red-500">{errors.primaryContactEmail}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone
                        </label>
                        <input
                          type="tel"
                          name="primaryContactPhone"
                          value={formData.primaryContactPhone}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="+****************"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-8">
                    <h3 className="text-md font-medium mb-4 pb-2 border-b border-gray-200">Billing Contact</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Name
                        </label>
                        <input
                          type="text"
                          name="billingContactName"
                          value={formData.billingContactName}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Full name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email
                        </label>
                        <input
                          type="email"
                          name="billingContactEmail"
                          value={formData.billingContactEmail}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone
                        </label>
                        <input
                          type="tel"
                          name="billingContactPhone"
                          value={formData.billingContactPhone}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="+****************"
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-md font-medium mb-4 pb-2 border-b border-gray-200">Technical Contact</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Name
                        </label>
                        <input
                          type="text"
                          name="technicalContactName"
                          value={formData.technicalContactName}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Full name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email
                        </label>
                        <input
                          type="email"
                          name="technicalContactEmail"
                          value={formData.technicalContactEmail}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="<EMAIL>"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone
                        </label>
                        <input
                          type="tel"
                          name="technicalContactPhone"
                          value={formData.technicalContactPhone}
                          onChange={handleChange}
                          className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="+****************"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-medium">Billing & Subscription</h2>
                  <p className="text-sm text-gray-500 mt-1">Set up billing and subscription details for the tenant.</p>
                </div>

                <div className="p-6 space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Billing Plan <span className="text-red-500">*</span>
                      </label>
                      <select
                        name="billingPlan"
                        value={formData.billingPlan}
                        onChange={handleChange}
                        className={`w-full border ${errors.billingPlan ? 'border-red-500' : 'border-gray-300'} rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      >
                        <option value="" disabled>Select a plan</option>
                        <option value="basic">Basic Plan</option>
                        <option value="professional">Professional Plan</option>
                        <option value="enterprise">Enterprise Plan</option>
                        <option value="custom">Custom Plan</option>
                      </select>
                      {errors.billingPlan && (
                        <p className="mt-1 text-sm text-red-500">{errors.billingPlan}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Billing Cycle
                      </label>
                      <select
                        name="billingCycle"
                        value={formData.billingCycle}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="monthly">Monthly</option>
                        <option value="quarterly">Quarterly</option>
                        <option value="annual">Annual</option>
                        <option value="biennial">Biennial</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Payment Method
                      </label>
                      <select
                        name="paymentMethod"
                        value={formData.paymentMethod}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="credit_card">Credit Card</option>
                        <option value="bank_transfer">Bank Transfer</option>
                        <option value="invoice">Invoice</option>
                        <option value="paypal">PayPal</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Tax ID/VAT Number
                      </label>
                      <input
                        type="text"
                        name="taxId"
                        value={formData.taxId}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Tax ID or VAT number for invoicing"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Billing Address
                    </label>
                    <textarea
                      name="billingAddress"
                      value={formData.billingAddress}
                      onChange={handleChange}
                      rows={3}
                      className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Complete billing address"
                    ></textarea>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Initial Contract Date <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        name="initialContractDate"
                        value={formData.initialContractDate}
                        onChange={handleChange}
                        className={`w-full border ${errors.initialContractDate ? 'border-red-500' : 'border-gray-300'} rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      />
                      {errors.initialContractDate && (
                        <p className="mt-1 text-sm text-red-500">{errors.initialContractDate}</p>
                      )}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Renewal Date
                      </label>
                      <input
                        type="date"
                        name="renewalDate"
                        value={formData.renewalDate}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="mt-1 text-xs text-gray-500">Leave blank if same as contract end date</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow overflow-hidden mb-6">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-medium">Tenant Configuration</h2>
                  <p className="text-sm text-gray-500 mt-1">Configure localization, limits, and features for this tenant.</p>
                </div>

                <div className="p-6 space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Default Language
                      </label>
                      <select
                        name="defaultLanguage"
                        value={formData.defaultLanguage}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="it">Italian</option>
                        <option value="pt">Portuguese</option>
                        <option value="ru">Russian</option>
                        <option value="zh">Chinese</option>
                        <option value="ja">Japanese</option>
                        <option value="ar">Arabic</option>
                        <option value="hi">Hindi</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Default Timezone <span className="text-red-500">*</span>
                      </label>
                      <select
                        name="defaultTimezone"
                        value={formData.defaultTimezone}
                        onChange={handleChange}
                        className={`w-full border ${errors.defaultTimezone ? 'border-red-500' : 'border-gray-300'} rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      >
                        <option value="" disabled>Select a timezone</option>
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">Eastern Time (ET)</option>
                        <option value="America/Chicago">Central Time (CT)</option>
                        <option value="America/Denver">Mountain Time (MT)</option>
                        <option value="America/Los_Angeles">Pacific Time (PT)</option>
                        <option value="Europe/London">London (GMT)</option>
                        <option value="Europe/Paris">Central European Time (CET)</option>
                        <option value="Asia/Tokyo">Japan Standard Time (JST)</option>
                        <option value="Asia/Shanghai">China Standard Time (CST)</option>
                        <option value="Australia/Sydney">Australian Eastern Time (AET)</option>
                      </select>
                      {errors.defaultTimezone && (
                        <p className="mt-1 text-sm text-red-500">{errors.defaultTimezone}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date Format
                      </label>
                      <select
                        name="dateFormat"
                        value={formData.dateFormat}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                        <option value="DD.MM.YYYY">DD.MM.YYYY</option>
                        <option value="MMMM D, YYYY">MMMM D, YYYY</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Currency
                      </label>
                      <select
                        name="currency"
                        value={formData.currency}
                        onChange={handleChange}
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="USD">US Dollar (USD)</option>
                        <option value="EUR">Euro (EUR)</option>
                        <option value="GBP">British Pound (GBP)</option>
                        <option value="CAD">Canadian Dollar (CAD)</option>
                        <option value="AUD">Australian Dollar (AUD)</option>
                        <option value="JPY">Japanese Yen (JPY)</option>
                        <option value="INR">Indian Rupee (INR)</option>
                        <option value="CNY">Chinese Yuan (CNY)</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        User Limit
                      </label>
                      <input
                        type="number"
                        name="userLimit"
                        value={formData.userLimit}
                        onChange={handleChange}
                        min="0"
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Leave blank for unlimited"
                      />
                      <p className="mt-1 text-xs text-gray-500">Maximum number of users allowed</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Storage Limit (GB)
                      </label>
                      <input
                        type="number"
                        name="storageLimit"
                        value={formData.storageLimit}
                        onChange={handleChange}
                        min="0"
                        className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Leave blank for unlimited"
                      />
                      <p className="mt-1 text-xs text-gray-500">Maximum storage space in gigabytes</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-md font-medium mb-4">Feature Flags</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="enableMembership"
                          name="enableMembership"
                          checked={formData.enableMembership}
                          onChange={(e) => setFormData({...formData, enableMembership: e.target.checked})}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="enableMembership" className="ml-2 block text-sm text-gray-700">
                          Enable Membership Features
                        </label>
                      </div>

                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="enableBlog"
                          name="enableBlog"
                          checked={formData.enableBlog}
                          onChange={(e) => setFormData({...formData, enableBlog: e.target.checked})}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="enableBlog" className="ml-2 block text-sm text-gray-700">
                          Enable Blog & Content
                        </label>
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center mb-4">
                      <input
                        type="checkbox"
                        id="customSmtpEnabled"
                        name="customSmtpEnabled"
                        checked={formData.customSmtpEnabled}
                        onChange={(e) => setFormData({...formData, customSmtpEnabled: e.target.checked})}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="customSmtpEnabled" className="ml-2 block text-sm font-medium text-gray-700">
                        Use Custom SMTP Settings
                      </label>
                    </div>

                    {formData.customSmtpEnabled && (
                      <div className="pl-6 border-l-2 border-gray-200 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              SMTP Host
                            </label>
                            <input
                              type="text"
                              name="smtpHost"
                              value={formData.smtpHost}
                              onChange={handleChange}
                              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="smtp.example.com"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              SMTP Port
                            </label>
                            <input
                              type="text"
                              name="smtpPort"
                              value={formData.smtpPort}
                              onChange={handleChange}
                              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="587"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              SMTP Username
                            </label>
                            <input
                              type="text"
                              name="smtpUsername"
                              value={formData.smtpUsername}
                              onChange={handleChange}
                              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="<EMAIL>"
                            />
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              SMTP Password
                            </label>
                            <input
                              type="password"
                              name="smtpPassword"
                              value={formData.smtpPassword}
                              onChange={handleChange}
                              className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="••••••••"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Encryption
                          </label>
                          <select
                            name="smtpEncryption"
                            value={formData.smtpEncryption}
                            onChange={handleChange}
                            className="w-full border border-gray-300 rounded-md px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="none">None</option>
                            <option value="ssl">SSL</option>
                            <option value="tls">TLS</option>
                          </select>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4 mb-8">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 border border-transparent rounded-md shadow-sm text-white bg-[#38bdf8] hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Create Tenant
                </button>
              </div>
            </form>
          </div>
        </main>
      </div>
    </div>
  );
}
