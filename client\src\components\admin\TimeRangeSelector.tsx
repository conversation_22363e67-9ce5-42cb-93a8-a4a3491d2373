import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '@/context/ThemeContext';
import { ChevronDown, Calendar } from 'lucide-react';

type TimeRange = 'Today' | 'Yesterday' | 'This Week' | 'This Month' | 'Last Month' | 'This Year' | 'Custom Range';

interface TimeRangeSelectorProps {
  selectedRange: TimeRange;
  onRangeChange: (range: TimeRange) => void;
}

export default function TimeRangeSelector({ selectedRange, onRangeChange }: TimeRangeSelectorProps) {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const timeRanges: TimeRange[] = [
    'Today',
    'Yesterday',
    'This Week',
    'This Month',
    'Last Month',
    'This Year',
    'Custom Range'
  ];

  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [showCustomDatePicker, setShowCustomDatePicker] = useState(false);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleRangeSelect = (range: TimeRange) => {
    onRangeChange(range);
    if (range === 'Custom Range') {
      setShowCustomDatePicker(true);
    } else {
      setShowCustomDatePicker(false);
    }
    setIsOpen(false);
  };

  const handleCustomDateApply = () => {
    if (startDate && endDate) {
      // Here you would typically pass the custom date range to your parent component
      // For now, we'll just close the date picker
      setShowCustomDatePicker(false);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className={`${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border px-4 py-1 rounded-md shadow-sm flex items-center justify-between min-w-[120px]`}
      >
        <span className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'}`}>{selectedRange}</span>
        <ChevronDown size={16} className={`${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} ml-2`} />
      </button>

      {isOpen && (
        <div className={`absolute right-0 mt-1 w-40 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-md shadow-lg z-10`}>
          <ul className="py-1">
            {timeRanges.map((range) => (
              <li
                key={range}
                className={`px-4 py-2 text-sm cursor-pointer ${
                  selectedRange === range
                    ? 'bg-orange-500 text-white'
                    : `${theme === 'dark' ? 'text-gray-200 hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'}`
                }`}
                onClick={() => handleRangeSelect(range)}
              >
                {range}
              </li>
            ))}
          </ul>
        </div>
      )}

      {showCustomDatePicker && (
        <div className={`absolute right-0 mt-1 p-3 w-64 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-md shadow-lg z-10`}>
          <div className="space-y-3">
            <div>
              <label className={`block text-xs font-medium mb-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                Start Date
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className={`w-full px-2 py-1 text-sm border rounded ${theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`}
              />
            </div>
            <div>
              <label className={`block text-xs font-medium mb-1 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                End Date
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className={`w-full px-2 py-1 text-sm border rounded ${theme === 'dark' ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900'}`}
              />
            </div>
            <div className="flex justify-end pt-2">
              <button
                onClick={() => setShowCustomDatePicker(false)}
                className="px-3 py-1 text-xs mr-2 rounded bg-gray-200 text-gray-800 hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={handleCustomDateApply}
                className="px-3 py-1 text-xs rounded bg-orange-500 text-white hover:bg-orange-600"
                disabled={!startDate || !endDate}
              >
                Apply
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
