import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Home,
  Link as LinkIcon,
  Clock,
  Globe,
  FileText,
  RefreshCw,
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
// import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Sitemap type definition
interface SitemapInfo {
  url: string;
  lastGenerated: string;
  status: 'active' | 'outdated' | 'generating';
  pageCount: number;
  fileSize: string;
}

export default function CreateSitemapPage() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [sitemap, setSitemap] = useState<SitemapInfo>({
    url: 'https://engaxe.com/sitemap-main.xml',
    lastGenerated: '12-06-2018',
    status: 'outdated',
    pageCount: 1245,
    fileSize: '2.3 MB'
  });

  const handleGenerateSitemap = () => {
    setIsGenerating(true);
    setSitemap(prev => ({ ...prev, status: 'generating' }));
    setProgress(0);

    // Simulate sitemap generation with progress updates
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + 10;
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setIsGenerating(false);
            const newDate = new Date().toLocaleDateString('en-GB', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            }).replace(/\//g, '-');

            setSitemap(prev => ({
              ...prev,
              lastGenerated: newDate,
              status: 'active',
              pageCount: Math.floor(Math.random() * 500) + 1000, // Random page count for demo
              fileSize: (Math.random() * 3 + 1).toFixed(1) + ' MB' // Random file size for demo
            }));
          }, 500);
        }
        return newProgress;
      });
    }, 200);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Active</Badge>;
      case 'outdated':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Outdated</Badge>;
      case 'generating':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-200">Generating</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Create Sitemap</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Sitemap</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Create Sitemap</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Sitemap Generator Card */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Generate New Sitemap</CardTitle>
                  <CardDescription>
                    Create a sitemap for better search engine indexing of your website
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex flex-col items-center py-8">
                  <div className="bg-blue-50 rounded-full p-8 mb-6">
                    <div className="bg-blue-500 rounded-full p-4">
                      <Globe className="h-8 w-8 text-white" />
                    </div>
                  </div>

                  {isGenerating ? (
                    <div className="w-full max-w-md space-y-4 mb-8">
                      <div className="flex justify-between mb-2">
                        <span className="text-sm font-medium">Generating sitemap...</span>
                        <span className="text-sm font-medium">{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                      <p className="text-sm text-muted-foreground text-center">
                        Please wait while we crawl your website and generate the sitemap
                      </p>
                    </div>
                  ) : (
                    sitemap.status === 'active' ? (
                      <Alert className="mb-8 max-w-md bg-green-50 border-green-200">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertTitle className="text-green-800">Sitemap Generated Successfully</AlertTitle>
                        <AlertDescription className="text-green-700">
                          Your sitemap has been created and is ready to be submitted to search engines.
                        </AlertDescription>
                      </Alert>
                    ) : (
                      <Alert className="mb-8 max-w-md bg-yellow-50 border-yellow-200">
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                        <AlertTitle className="text-yellow-800">Sitemap Needs Update</AlertTitle>
                        <AlertDescription className="text-yellow-700">
                          Your sitemap is outdated. We recommend generating a new one for better SEO.
                        </AlertDescription>
                      </Alert>
                    )
                  )}

                  <Button
                    className="mb-6"
                    onClick={handleGenerateSitemap}
                    disabled={isGenerating}
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Generate New Sitemap
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* Sitemap Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Sitemap Information</CardTitle>
                  <CardDescription>
                    Details about your current sitemap
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Status:</span>
                    {getStatusBadge(sitemap.status)}
                  </div>

                  <Separator />

                  <div className="flex items-start gap-2">
                    <LinkIcon className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Sitemap URL:</p>
                      <div className="flex items-center">
                        <a
                          href={sitemap.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline flex items-center"
                        >
                          {sitemap.url}
                          <ExternalLink className="ml-1 h-3 w-3" />
                        </a>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Last Generated:</p>
                      <p className="text-sm">{sitemap.lastGenerated}</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Pages Indexed:</p>
                      <p className="text-sm">{sitemap.pageCount.toLocaleString()} pages</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">File Size:</p>
                      <p className="text-sm">{sitemap.fileSize}</p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="outline" size="sm" className="w-full">
                          <Globe className="mr-2 h-4 w-4" />
                          Submit to Search Engines
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Submit your sitemap to Google, Bing and other search engines</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </CardFooter>
              </Card>
            </div>

            {/* Additional Info */}
            <Card>
              <CardHeader>
                <CardTitle>Sitemap Best Practices</CardTitle>
                <CardDescription>
                  Tips to improve your sitemap for better SEO
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Keep it Updated</h3>
                    <p className="text-sm text-muted-foreground">Regularly update your sitemap to ensure search engines have the latest information about your website structure.</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Include Important Pages</h3>
                    <p className="text-sm text-muted-foreground">Make sure all important pages are included in your sitemap for better indexing by search engines.</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Submit to Search Engines</h3>
                    <p className="text-sm text-muted-foreground">After generating your sitemap, submit it to major search engines like Google and Bing through their webmaster tools.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
