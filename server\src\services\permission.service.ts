import { PermissionModel, UserModel, RoleModel } from '../models';
import { IPermission } from '../models/permission.model';
import { IUser } from '../models/user.model';
import { AppError, createNotFoundError, createConflictError, createInternalServerError, ErrorCodes } from '../utils/errors';
import { logger } from '../utils/logger';

/**
 * Permission service for handling permission-related operations
 */
export class PermissionService {
  /**
   * Create a new permission
   */
  async createPermission(permissionData: Partial<IPermission>, createdBy: string): Promise<IPermission> {
    try {
      // Check if permission code already exists
      const existingPermission = await PermissionModel.findOne({
        code: permissionData.code,
        deletedAt: { $exists: false },
      });

      if (existingPermission) {
        throw new Error(`Permission with code '${permissionData.code}' already exists`);
      }

      // Validate dependencies
      if (permissionData.dependencies && permissionData.dependencies.length > 0) {
        const dependencies = await PermissionModel.find({
          code: { $in: permissionData.dependencies },
          deletedAt: { $exists: false },
        });

        if (dependencies.length !== permissionData.dependencies.length) {
          throw new Error('One or more dependencies do not exist');
        }
      }

      // Validate conflicts
      if (permissionData.conflicts && permissionData.conflicts.length > 0) {
        const conflicts = await PermissionModel.find({
          code: { $in: permissionData.conflicts },
          deletedAt: { $exists: false },
        });

        if (conflicts.length !== permissionData.conflicts.length) {
          throw new Error('One or more conflicts do not exist');
        }
      }

      // Create new permission
      const permission = new PermissionModel({
        name: permissionData.name,
        description: permissionData.description,
        code: permissionData.code,
        category: permissionData.category,
        isActive: permissionData.isActive !== undefined ? permissionData.isActive : true,
        resourceType: permissionData.resourceType,
        action: permissionData.action,
        scope: permissionData.scope || 'own',
        conditions: permissionData.conditions,
        dependencies: permissionData.dependencies,
        conflicts: permissionData.conflicts,
        createdBy,
        updatedBy: createdBy,
      });

      await permission.save();
      return permission;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get all permissions
   */
  async getAllPermissions(options: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    resourceType?: string;
    action?: string;
    scope?: string;
    isActive?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ permissions: IPermission[]; total: number; page: number; limit: number; pages: number }> {
    try {
      const {
        page = 1,
        limit = 10,
        search = '',
        category,
        resourceType,
        action,
        scope,
        isActive,
        sortBy = 'category',
        sortOrder = 'asc',
      } = options;

      // Build query
      const query: any = {
        deletedAt: { $exists: false },
      };

      // Add search filter
      if (search) {
        query.$or = [
          { name: { $regex: search, $options: 'i' } },
          { code: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ];
      }

      // Add category filter
      if (category) {
        query.category = category;
      }

      // Add resourceType filter
      if (resourceType) {
        query.resourceType = resourceType;
      }

      // Add action filter
      if (action) {
        query.action = action;
      }

      // Add scope filter
      if (scope) {
        query.scope = scope;
      }

      // Add isActive filter
      if (isActive !== undefined) {
        query.isActive = isActive;
      }

      // Count total documents
      const total = await PermissionModel.countDocuments(query);

      // Calculate pagination
      const pages = Math.ceil(total / limit);
      const skip = (page - 1) * limit;

      // Build sort object
      const sort: any = {};
      sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute query
      const permissions = await PermissionModel.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit);

      return {
        permissions,
        total,
        page,
        limit,
        pages,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(id: string): Promise<IPermission> {
    try {
      const permission = await PermissionModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!permission) {
        throw new Error('Permission not found');
      }

      return permission;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Update permission
   */
  async updatePermission(id: string, permissionData: Partial<IPermission>, updatedBy: string): Promise<IPermission> {
    try {
      const permission = await PermissionModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!permission) {
        throw new Error('Permission not found');
      }

      // Validate dependencies
      if (permissionData.dependencies && permissionData.dependencies.length > 0) {
        const dependencies = await PermissionModel.find({
          code: { $in: permissionData.dependencies },
          deletedAt: { $exists: false },
        });

        if (dependencies.length !== permissionData.dependencies.length) {
          throw new Error('One or more dependencies do not exist');
        }
      }

      // Validate conflicts
      if (permissionData.conflicts && permissionData.conflicts.length > 0) {
        const conflicts = await PermissionModel.find({
          code: { $in: permissionData.conflicts },
          deletedAt: { $exists: false },
        });

        if (conflicts.length !== permissionData.conflicts.length) {
          throw new Error('One or more conflicts do not exist');
        }
      }

      // Update allowed fields
      if (permissionData.name) permission.name = permissionData.name;
      if (permissionData.description) permission.description = permissionData.description;
      if (permissionData.category) permission.category = permissionData.category;
      if (permissionData.isActive !== undefined) permission.isActive = permissionData.isActive;
      if (permissionData.scope) permission.scope = permissionData.scope;
      if (permissionData.conditions !== undefined) permission.conditions = permissionData.conditions;
      if (permissionData.dependencies !== undefined) permission.dependencies = permissionData.dependencies;
      if (permissionData.conflicts !== undefined) permission.conflicts = permissionData.conflicts;

      permission.updatedBy = updatedBy;
      await permission.save();

      return permission;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Delete permission
   */
  async deletePermission(id: string, deletedBy: string): Promise<boolean> {
    try {
      const permission = await PermissionModel.findOne({
        id,
        deletedAt: { $exists: false },
      });

      if (!permission) {
        throw new Error('Permission not found');
      }

      // Check if permission is used in any roles
      const rolesWithPermission = await RoleModel.countDocuments({
        permissions: permission.code,
        deletedAt: { $exists: false },
      });

      if (rolesWithPermission > 0) {
        throw new Error(`Cannot delete permission used in ${rolesWithPermission} roles`);
      }

      // Check if permission is assigned directly to any users
      const usersWithPermission = await UserModel.countDocuments({
        permissions: permission.code,
        deletedAt: { $exists: false },
      });

      if (usersWithPermission > 0) {
        throw new Error(`Cannot delete permission assigned to ${usersWithPermission} users`);
      }

      // Soft delete
      permission.deletedAt = new Date();
      permission.deletedBy = deletedBy;
      permission.isActive = false;
      await permission.save();

      return true;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Assign permissions to user
   */
  async assignPermissionsToUser(userId: string, permissionIds: string[], updatedBy: string): Promise<IUser> {
    try {
      // Find user
      const user = await UserModel.findOne({
        id: userId,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Validate permissions
      const permissions = await PermissionModel.find({
        id: { $in: permissionIds },
        isActive: true,
        deletedAt: { $exists: false },
      });

      if (permissions.length !== permissionIds.length) {
        throw new Error('One or more permissions do not exist or are not active');
      }

      // Get permission codes
      const permissionCodes = permissions.map(p => p.code);

      // Update user permissions
      user.permissions = permissionCodes;
      user.updatedBy = updatedBy;
      await user.save();

      return user;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user permissions (including from roles)
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Find user
      const user = await UserModel.findOne({
        id: userId,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Get direct permissions
      const directPermissions = user.permissions || [];

      // Get role permissions
      const roles = await RoleModel.find({
        id: { $in: user.roles },
        isActive: true,
        deletedAt: { $exists: false },
      });

      const rolePermissions = roles.reduce<string[]>((acc, role) => {
        return [...acc, ...(role.permissions || [])];
      }, []);

      // Combine and deduplicate permissions
      const allPermissions = [...new Set([...directPermissions, ...rolePermissions])];

      return allPermissions;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Check if user has permission
   */
  async userHasPermission(userId: string, permissionCode: string): Promise<boolean> {
    try {
      const permissions = await this.getUserPermissions(userId);
      return permissions.includes(permissionCode);
    } catch (error) {
      // If it's already an AppError, just rethrow it
      if (error instanceof AppError) {
        throw error;
      }

      // Otherwise, log it and wrap it in an AppError
      logger.error('Error in userHasPermission', error);
      throw createInternalServerError('Failed to check user permission', ErrorCodes.INTERNAL_SERVER_ERROR);
    }
  }
}

export default new PermissionService();
