import { useEffect, useRef } from 'react';
import { hashIdToEngaxeId, isValidEngaxeId } from '@/utils/videoIdConverter';

interface EngaxeEmbedProps {
  videoId: string;
  divId?: string;
  className?: string;
  width?: string | number;
  height?: string | number;
  autoplay?: boolean;
  userCreds?: string;
}

/**
 * Component for embedding Engaxe videos
 * Uses the Engaxe embedding API to play videos
 */
export default function EngaxeEmbed({
  videoId,
  divId = `engaxe-embed-${Math.random().toString(36).substring(2, 9)}`,
  className = '',
  width = '100%',
  height = '100%',
  autoplay = false,
  userCreds = ''
}: EngaxeEmbedProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const embedInitialized = useRef<boolean>(false);

  // Convert the video ID to a valid Engaxe ID if needed
  const getEngaxeId = (id: string): string => {
    if (!id) return '';
    
    // If it's already a valid Engaxe ID, return it as is
    if (isValidEngaxeId(id)) {
      console.log(`Video ID ${id} is already a valid Engaxe ID`);
      return id;
    }
    
    // Otherwise, convert it
    console.log(`Converting video ID ${id} to Engaxe ID`);
    const engaxeId = hashIdToEngaxeId(id);
    console.log(`Converted to: ${engaxeId}`);
    return engaxeId;
  };

  // Initialize the embed when the component mounts
  useEffect(() => {
    const engaxeId = getEngaxeId(videoId);
    
    if (!engaxeId) {
      console.error('Invalid video ID provided to EngaxeEmbed component');
      return;
    }

    // Method 1: Use the ngxEmbed function if available
    if (window.ngxEmbed && !embedInitialized.current) {
      console.log(`Embedding video ${engaxeId} using ngxEmbed function`);
      try {
        window.ngxEmbed(divId, engaxeId, userCreds);
        embedInitialized.current = true;
      } catch (error) {
        console.error('Error using ngxEmbed:', error);
        // Fall back to iframe method
        createIframeEmbed(engaxeId);
      }
    } else {
      // Method 2: Create an iframe embed
      createIframeEmbed(engaxeId);
    }

    // Cleanup function
    return () => {
      embedInitialized.current = false;
    };
  }, [videoId, divId, userCreds]);

  // Create an iframe embed
  const createIframeEmbed = (engaxeId: string) => {
    if (!containerRef.current) return;
    
    console.log(`Embedding video ${engaxeId} using iframe method`);
    
    // Clear any existing content
    if (containerRef.current.firstChild && containerRef.current.firstChild !== iframeRef.current) {
      containerRef.current.innerHTML = '';
    }
    
    // Create the iframe if it doesn't exist
    if (!iframeRef.current) {
      const iframe = document.createElement('iframe');
      iframe.src = `https://engaxe.com/e/${engaxeId}?vid=${engaxeId}`;
      iframe.width = width.toString();
      iframe.height = height.toString();
      iframe.frameBorder = '0';
      iframe.allowFullscreen = true;
      iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share';
      iframe.referrerPolicy = 'strict-origin-when-cross-origin';

      if (autoplay) {
        iframe.src += '&autoplay=1';
      }
      
      containerRef.current.appendChild(iframe);
      iframeRef.current = iframe;
    } else {
      // Update the existing iframe
      iframeRef.current.src = `https://engaxe.com/e/${engaxeId}?vid=${engaxeId}${autoplay ? '&autoplay=1' : ''}`;
    }
  };

  // Replace the current video with a new one
  const replaceVideo = (newVideoId: string) => {
    const newEngaxeId = getEngaxeId(newVideoId);

    if (!newEngaxeId) {
      console.error('Invalid video ID provided to replaceVideo');
      return;
    }

    console.log(`Replacing video with new ID: ${newEngaxeId}`);

    // Method 1: Use the ngxReplaceEmbed function if available
    if (window.ngxReplaceEmbed) {
      try {
        window.ngxReplaceEmbed(divId, videoId, newEngaxeId);
        return;
      } catch (error) {
        console.error('Error using ngxReplaceEmbed:', error);
      }
    }

    // Method 2: Update the iframe src
    if (iframeRef.current) {
      iframeRef.current.src = `https://engaxe.com/e/${newEngaxeId}?vid=${newEngaxeId}${autoplay ? '&autoplay=1' : ''}`;
    } else {
      createIframeEmbed(newEngaxeId);
    }
  };

  return (
    <div 
      id={divId}
      ref={containerRef}
      className={`engaxe-embed-container ${className}`}
      style={{ width, height, position: 'relative', overflow: 'hidden' }}
    />
  );
}

// Add type definitions for the Engaxe embedding functions
declare global {
  interface Window {
    ngxEmbed?: (divId: string, videoId: string, userCreds?: string) => void;
    ngxReplaceEmbed?: (divId: string, oldVideoId: string, newVideoId: string) => void;
  }
}
