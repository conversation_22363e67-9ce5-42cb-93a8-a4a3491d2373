
export interface User {
  id: string;
  username: string;
  avatar: string;
  email?: string;
  isOnline?: boolean;
  isAdmin?: boolean;
  engaxeUserId?: string; // Engaxe user ID for integration
}

export interface Video {
  id: string;
  title: string;
  thumbnail: string;
  thumbnailUrl?: string; // Added thumbnailUrl as an alternative source for thumbnail
  creator: User;
  views: number;
  likes: number;
  createdAt: string;
  languages: Language[];
  category: string;
  isLive?: boolean;
  description?: string;
  duration?: string;
  url?: string;
  savedLocally?: boolean;
}

export interface Language {
  code: string;
  name: string;
  flag: string;
  url?: string; // URL specific to this language version
  isDefault?: boolean; // Whether this is the default language
}

export interface Message {
  id: string;
  sender: User;
  content: string;
  timestamp: string;
  isTranslated?: boolean;
  originalContent?: string;
  originalLanguage?: string;
  isVoice?: boolean;
  audioUrl?: string;
  read?: boolean;
}

export interface Notification {
  id: string;
  title: string;
  content: string;
  timestamp: string;
  read: boolean;
  type: 'mention' | 'like' | 'comment' | 'achievement' | 'system';
  user?: User;
}

export interface Conversation {
  id: string;
  participants: User[];
  lastMessage: Message;
  unreadCount: number;
}
