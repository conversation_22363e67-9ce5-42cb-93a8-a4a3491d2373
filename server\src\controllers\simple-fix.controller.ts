import { FastifyRequest, FastifyReply, RouteGenericInterface } from 'fastify';
import { VideoModel } from '../models';
import { AuthenticatedUser } from '../types/user';

// Extend AuthenticatedUser to include isAdmin property
interface ExtendedAuthenticatedUser extends AuthenticatedUser {
  isAdmin?: boolean;
}

// Define a custom request type with authenticated user
interface AuthenticatedRequest<T extends RouteGenericInterface = RouteGenericInterface> extends FastifyRequest<T> {
  user: ExtendedAuthenticatedUser;
}

export class SimpleFixController {
  /**
   * Simple fix to add default language to all videos
   */
  simpleFixLanguages = async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      // Get user ID from authenticated request
      const userId = request.user.id;

      // Check if user is admin
      // In development mode, we'll allow any user to fix languages
      const isDevelopment = process.env.NODE_ENV === 'development';
      
      if (!isDevelopment && !request.user.isAdmin) {
        return reply.code(403).send({
          success: false,
          message: 'Only admins can fix all video languages',
          error: {
            code: 'FORBIDDEN',
            message: 'Only admins can fix all video languages',
          },
        });
      }
      
      // If we're in development mode and isAdmin is undefined, log a warning
      if (isDevelopment && request.user.isAdmin === undefined) {
        console.warn('Warning: isAdmin property is undefined. In production, this would fail.');
      }

      console.log(`User ${userId} requested simple fix for all video languages`);

      // Get all videos
      const videos = await VideoModel.find({});
      console.log(`Found ${videos.length} videos in the database`);

      let updatedCount = 0;
      let skippedCount = 0;
      let errorCount = 0;

      // Process each video
      for (const video of videos) {
        try {
          console.log(`Processing video: ${video.title || 'Untitled'} (ID: ${video.id || 'Unknown ID'})`);
          
          // Create a default English language
          const defaultLanguage = {
            code: 'en',
            name: 'English',
            flag: '🇺🇸',
            isDefault: true,
            url: video.url || video.id || ''
          };
          
          // Set the languages array directly
          video.languages = [defaultLanguage];
          
          // Save the video
          await video.save();
          updatedCount++;
          console.log(`Added default English language to video: ${video.title || 'Untitled'}`);
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : String(err);
          console.error(`Error processing video ${video.id || 'Unknown ID'}:`, errorMessage);
          errorCount++;
        }
      }

      const result = {
        total: videos.length,
        updated: updatedCount,
        skipped: skippedCount,
        errors: errorCount
      };

      console.log('Simple fix complete!');
      console.log(`Total videos: ${videos.length}`);
      console.log(`Updated: ${updatedCount}`);
      console.log(`Errors: ${errorCount}`);

      return reply.code(200).send({
        success: true,
        message: 'Simple fix for all video languages completed',
        stats: result,
      });
    } catch (error) {
      console.error('Error in simple fix for video languages:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      return reply.code(500).send({
        success: false,
        message: 'Error in simple fix for video languages',
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: errorMessage,
        },
      });
    }
  };
}
