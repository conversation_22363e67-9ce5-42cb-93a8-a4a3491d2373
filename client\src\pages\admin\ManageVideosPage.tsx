import React, { useState } from 'react';
import { useNavigate, <PERSON> } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Search, Filter, Edit, Trash2, Eye, MoreVertical, Play, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Sample video data
const videos = [
  {
    id: 756,
    videoId: 'xStzlCwln6yMMnLM',
    title: 'PLAY UNO WITH YOUR KEYBOARD..',
    author: 'Bhariya',
    category: 'Gaming',
    source: 'YouTube',
    privacy: 'Public',
    approved: 1,
    addedBy: 'Bhariya'
  },
  {
    id: 702,
    videoId: 'vPwWsQ4fBCT7hF3',
    title: 'PhD | All of Would in Detail..',
    author: 'Muskan <PERSON>',
    category: 'Film & Animation',
    source: 'YouTube',
    privacy: 'Public',
    approved: 1,
    addedBy: 'Muskan Dindoriya'
  },
  {
    id: 701,
    videoId: 'LSxXCSxbhCxQCNI',
    title: 'All Modal Verbs in English - Learn All M..',
    author: 'Muskan Dindoriya',
    category: 'Film & Animation',
    source: 'YouTube',
    privacy: 'Public',
    approved: 1,
    addedBy: 'Muskan Dindoriya'
  }
];

export default function ManageVideosPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Category');
  const [selectedFilter, setSelectedFilter] = useState('All (Default)');
  const [selectedLimit, setSelectedLimit] = useState('50 (Default)');
  const [selectedType, setSelectedType] = useState('All Videos');
  const [selectedPricing, setSelectedPricing] = useState('Paid & Free');
  const [selectedAction, setSelectedAction] = useState('Approve');
  const [currentPage, setCurrentPage] = useState(1);

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  const handleSearch = () => {
    console.log('Searching for:', searchQuery);
    // Implement search functionality
  };

  const handleActionSubmit = () => {
    console.log('Performing action:', selectedAction);
    // Implement action functionality
  };

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <div className="p-6 flex-1 bg-gray-50">
          {/* Breadcrumb */}
          <div className="flex items-center text-sm mb-6">
            <Link to="/admin" className="text-blue-600 hover:underline flex items-center">
              <Home size={14} className="mr-1" />
              Admin Panel
            </Link>
            <ChevronRight size={14} className="mx-2 text-gray-500" />
            <Link to="/admin/videos" className="text-blue-600 hover:underline">
              Videos
            </Link>
            <ChevronRight size={14} className="mx-2 text-gray-500" />
            <span className="text-gray-600">Manage Videos</span>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-6">Manage & Edit Videos</h1>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <input
                  type="text"
                  placeholder="Keyword, ID, Title"
                  className="w-full p-2 border border-gray-300 rounded"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div>
                <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select filter" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All (Default)">All (Default)</SelectItem>
                    <SelectItem value="Approved">Approved</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select value={selectedLimit} onValueChange={setSelectedLimit}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select limit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50 (Default)">50 (Default)</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All Videos">All Videos</SelectItem>
                    <SelectItem value="Featured">Featured</SelectItem>
                    <SelectItem value="Not Featured">Not Featured</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Category">Category</SelectItem>
                    <SelectItem value="Gaming">Gaming</SelectItem>
                    <SelectItem value="Film & Animation">Film & Animation</SelectItem>
                    <SelectItem value="Education">Education</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select value={selectedPricing} onValueChange={setSelectedPricing}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select pricing" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Paid & Free">Paid & Free</SelectItem>
                    <SelectItem value="Paid">Paid</SelectItem>
                    <SelectItem value="Free">Free</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="mb-6">
              <Button
                className="bg-blue-500 hover:bg-blue-600 text-white"
                onClick={handleSearch}
              >
                Search
              </Button>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="w-12 px-4 py-3 border">
                      <input type="checkbox" className="h-4 w-4" />
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      ID <span className="text-gray-400">↑</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      VIDEO ID <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      TITLE <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      CATEGORY <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      SOURCE <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      PRIVACY <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      ADDED BY <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      APPROVED <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      ACTION
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {videos.map((video) => (
                    <tr key={video.id}>
                      <td className="px-4 py-3 whitespace-nowrap border">
                        <input type="checkbox" className="h-4 w-4" />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {video.id}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {video.videoId}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {video.title}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {video.category}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {video.source}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {video.privacy}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        <div className="flex items-center">
                          <img src="/avatar.png" alt="Avatar" className="h-6 w-6 rounded-full mr-2" />
                          {video.addedBy}
                        </div>
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {video.approved}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium border">
                        <div className="flex space-x-2">
                          <button className="flex items-center px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                            <Edit size={12} className="mr-1" />
                            Edit
                          </button>
                          <button className="flex items-center px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">
                            <Eye size={12} className="mr-1" />
                            Fake views
                          </button>
                          <button className="flex items-center px-2 py-1 bg-red-100 text-red-700 rounded text-xs">
                            <Trash2 size={12} className="mr-1" />
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 flex flex-col md:flex-row justify-between items-start md:items-center">
              <div className="mb-4 md:mb-0">
                <p className="text-sm text-gray-600 mb-2">Showing 1 out of 2</p>
                <div className="flex items-center">
                  <span className="mr-2 text-sm text-gray-600">Action</span>
                  <div className="relative">
                    <Select value={selectedAction} onValueChange={setSelectedAction}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Select action" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Approve">Approve</SelectItem>
                        <SelectItem value="Disapprove">Disapprove</SelectItem>
                        <SelectItem value="Delete">Delete</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <Button
                    className="ml-2 bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={handleActionSubmit}
                  >
                    Submit
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-1">
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &lt;&lt;
                </button>
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &lt;
                </button>
                <button className="w-8 h-8 flex items-center justify-center bg-blue-500 text-white rounded">
                  1
                </button>
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  2
                </button>
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &gt;
                </button>
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &gt;&gt;
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
