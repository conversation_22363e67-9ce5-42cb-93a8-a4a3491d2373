/**
 * <PERSON><PERSON><PERSON> to fix video URLs in the database
 * This script directly calls the VideoService.fixVideoUrls method
 * to fix all videos in the database
 */

import mongoose from 'mongoose';
import { VideoService } from '../services/video.service';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectToDatabase = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';
    console.log(`Connecting to MongoDB at ${mongoUri}...`);
    
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB successfully');
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
};

// Run the migration
const runMigration = async () => {
  try {
    console.log('Starting video URL migration...');
    
    const videoService = new VideoService();
    const result = await videoService.fixVideoUrls();
    
    console.log(`Migration completed successfully!`);
    console.log(`Fixed ${result.fixedCount} out of ${result.totalCount} videos.`);
    
    return result;
  } catch (error) {
    console.error('Error running migration:', error);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    await connectToDatabase();
    await runMigration();
    
    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
    
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

// Run the script
main();
