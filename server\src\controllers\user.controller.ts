import { FastifyRequest, FastifyReply } from 'fastify';
import userService from '../services/user.service';
import { IUser } from '../models/user.model';
import {
  AuthenticatedUser,
  FastifyAuthInstance,
  RegisterUserBody,
  LoginUserBody,
  ChangePasswordBody,
  UpdateUserStatusBody,
  UserIdParams,
  GetAllUsersQuery
} from '../types/user';

// Define extended FastifyRequest with user property
interface RequestWith<PERSON>ser extends FastifyRequest {
  user: AuthenticatedUser;
}

/**
 * User controller for handling user-related requests
 */
export class UserController {
  /**
   * Register a new user
   */
  async registerUser(
    request: FastifyRequest<{
      Body: RegisterUserBody;
    }>,
    reply: FastifyReply
  ) {
    try {
      const userData = request.body;
      const result = await userService.registerUser(userData, request.server);

      // Remove sensitive data
      const userResponse = {
        id: result.user.id,
        username: result.user.username,
        email: result.user.email,
        firstName: result.user.firstName,
        lastName: result.user.lastName,
        displayName: result.user.displayName,
        createdAt: result.user.createdAt,
      };

      return reply.code(201).send({
        success: true,
        message: 'User registered successfully. Please check your email to verify your account.',
        user: userResponse,
        verificationToken: result.verificationToken,
      });
    } catch (error: any) {
      request.log.error(error);

      // Handle specific error types
      if (error.statusCode === 409) {
        // Conflict error (e.g., email already in use)
        return reply.code(409).send({
          success: false,
          message: error.message || 'Email or username already in use',
          error: {
            code: error.errorCode || 'CONFLICT',
            message: error.message
          }
        });
      }

      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to register user',
        error: {
          code: error.errorCode || 'BAD_REQUEST',
          message: error.message
        }
      });
    }
  }

  /**
   * Login user
   */
  async loginUser(
    request: FastifyRequest<{
      Body: LoginUserBody;
    }> & { server: FastifyAuthInstance },
    reply: FastifyReply
  ) {
    try {
      const { email, password } = request.body;
      const { user, isValid } = await userService.loginUser(email, password);

      if (!user || !isValid) {
        return reply.code(401).send({
          success: false,
          message: 'Invalid email or password',
        });
      }

      // Generate tokens
      const accessToken = request.server.generateAccessToken({
        id: user.id,
        email: user.email,
        roles: user.roles,
      });

      const refreshToken = request.server.generateRefreshToken({
        id: user.id,
        email: user.email,
      });

      // Remove sensitive data
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        roles: user.roles,
      };

      return reply.code(200).send({
        success: true,
        message: 'Login successful',
        accessToken,
        refreshToken,
        user: userResponse,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(401).send({
        success: false,
        message: error.message || 'Login failed',
      });
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(request: RequestWithUser, reply: FastifyReply) {
    try {
      const userId = request.user.id;
      const user = await userService.getUserById(userId);

      // Remove sensitive data
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        avatar: user.avatar,
        roles: user.roles,
        permissions: user.permissions,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      return reply.code(200).send({
        success: true,
        user: userResponse,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(404).send({
        success: false,
        message: error.message || 'User not found',
      });
    }
  }

  /**
   * Get user profile by ID
   */
  async getUserProfile(
    request: FastifyRequest<{
      Params: UserIdParams;
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const user = await userService.getUserById(id);

      // Remove sensitive data
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        avatar: user.avatar,
        roles: user.roles,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      return reply.code(200).send({
        success: true,
        user: userResponse,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(404).send({
        success: false,
        message: error.message || 'User not found',
      });
    }
  }

  /**
   * Update user profile
   */
  async updateUserProfile(
    request: FastifyRequest<{
      Params: UserIdParams;
      Body: Partial<IUser>;
    }> & { user: AuthenticatedUser, hasPermission: (permission: string) => Promise<boolean> },
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const userData = request.body;
      const updatedBy = request.user.id;

      // Check if user is updating their own profile or has admin permission
      if (id !== updatedBy && !await request.hasPermission('user:update')) {
        return reply.code(403).send({
          success: false,
          message: 'You do not have permission to update this user',
        });
      }

      const user = await userService.updateUserProfile(id, userData, updatedBy);

      // Remove sensitive data
      const userResponse = {
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        avatar: user.avatar,
        updatedAt: user.updatedAt,
      };

      return reply.code(200).send({
        success: true,
        message: 'Profile updated successfully',
        user: userResponse,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to update profile',
      });
    }
  }

  /**
   * Change password
   */
  async changePassword(
    request: FastifyRequest<{
      Body: ChangePasswordBody;
    }> & { user: AuthenticatedUser },
    reply: FastifyReply
  ) {
    try {
      const { currentPassword, newPassword, confirmPassword } = request.body;
      const userId = request.user.id;

      // Check if passwords match
      if (newPassword !== confirmPassword) {
        return reply.code(400).send({
          success: false,
          message: 'New password and confirm password do not match',
        });
      }

      await userService.changePassword(userId, currentPassword, newPassword);

      return reply.code(200).send({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to change password',
      });
    }
  }

  /**
   * Get all users (admin)
   */
  async getAllUsers(
    request: FastifyRequest<{
      Querystring: GetAllUsersQuery;
    }>,
    reply: FastifyReply
  ) {
    try {
      const options = request.query;
      const result = await userService.getAllUsers(options);

      // Map users to remove sensitive data
      const users = result.users.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        displayName: user.displayName,
        status: user.status,
        roles: user.roles,
        createdAt: user.createdAt,
      }));

      return reply.code(200).send({
        success: true,
        users,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          pages: result.pages,
        },
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get users',
      });
    }
  }

  /**
   * Delete user (admin)
   */
  async deleteUser(
    request: FastifyRequest<{
      Params: UserIdParams;
    }> & { user: AuthenticatedUser },
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const deletedBy = request.user.id;

      await userService.deleteUser(id, deletedBy);

      return reply.code(200).send({
        success: true,
        message: 'User deleted successfully',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to delete user',
      });
    }
  }

  /**
   * Update user status (admin)
   */
  async updateUserStatus(
    request: FastifyRequest<{
      Params: UserIdParams;
      Body: UpdateUserStatusBody;
    }> & { user: AuthenticatedUser },
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const { status, statusReason } = request.body;
      const updatedBy = request.user.id;

      const user = await userService.updateUserStatus(id, status, statusReason, updatedBy);

      return reply.code(200).send({
        success: true,
        message: `User status updated to ${status}`,
        user: {
          id: user.id,
          username: user.username,
          status: user.status,
          statusReason: user.statusReason,
        },
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to update user status',
      });
    }
  }
}

export default new UserController();
