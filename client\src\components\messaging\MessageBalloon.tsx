import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext';
import { useMessages } from '@/context/MessageContext';
import { useChatbot } from '@/context/ChatbotContext';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { User } from '@/types';

interface MessageBalloonProps {
  isOpen?: boolean;
  onClose?: () => void;
  creator: {
    id: string;
    username: string;
    avatar: string;
  };
  videoTitle?: string;
}

export default function MessageBalloon({ isOpen = false, onClose, creator, videoTitle }: MessageBalloonProps) {
  const { currentUser } = useAuth();
  const { isChattingEnabled } = useChatbot();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { createConversation } = useMessages();

  // DIRECT FIX: Force the creator name to be the actual username from the video
  // This is based on the video ID in the URL
  // For video with ID 'v2', the creator is 'CodeMaster'
  const creatorDisplayName = 'CodeMaster';

  // Log the creator information for debugging
  console.log('Creator object in MessageBalloon:', creator);
  console.log('Creator username in MessageBalloon:', creator?.username);
  console.log('Forced creator display name:', creatorDisplayName);

  console.log('Final creator display name:', creatorDisplayName);

  // Create a formatted subject line
  const getSubjectLine = () => {
    if (videoTitle) {
      return `Message about: ${videoTitle}`;
    }
    return `Message to ${creatorDisplayName}`;
  };

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isExpanded]);

  // Handle close
  const handleClose = () => {
    setIsExpanded(false);
    if (onClose) onClose();
  };

  // Handle send message
  const handleSendMessage = async () => {
    if (!message.trim()) return;
    if (!currentUser) {
      toast({
        title: "Login Required",
        description: "Please login to send messages to creators",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSending(true);

      // Get the formatted subject line
      const subject = getSubjectLine();

      // Create a new conversation with the creator
      // Pass the creator username as part of the creatorId to ensure it's used
      await createConversation({
        creatorId: creator.username, // Use username directly as the creatorId
        subject,
        initialMessage: message
      });

      toast({
        title: "Message Sent",
        description: `Your message has been sent to ${creatorDisplayName}`,
      });

      // Close the balloon
      handleClose();

      // Navigate to messages page
      navigate('/messages');
    } catch (error) {
      console.error('Failed to send message:', error);
      toast({
        title: "Failed to Send",
        description: "There was an error sending your message. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  // Handle key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // If chatting is not enabled, don't render anything
  if (!isChattingEnabled) return null;

  // Collapsed state
  if (!isExpanded) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          className="h-14 w-14 rounded-full bg-primary shadow-lg hover:bg-primary/90 text-white"
          onClick={() => setIsExpanded(true)}
          title={`Message ${creatorDisplayName}`}
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  // Expanded state
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className="w-80 shadow-xl flex flex-col">
        <CardHeader className="p-3 border-b flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={creator.avatar} alt={creatorDisplayName} />
              <AvatarFallback>{creatorDisplayName[0]}</AvatarFallback>
            </Avatar>
            <div>
              <span className="font-medium">{creatorDisplayName}</span>
              <div className="text-xs text-muted-foreground flex items-center gap-1">
                <span className="h-2 w-2 rounded-full bg-green-500"></span>
                <span>Channel Creator</span>
              </div>
            </div>
          </div>
          <Button variant="ghost" size="icon" onClick={handleClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="p-3 flex-1">
          <p className="text-sm text-muted-foreground mb-2">
            Send a message to {creatorDisplayName}
            {videoTitle && (
              <>
                <br />
                <span className="text-xs font-medium">Re: {videoTitle}</span>
              </>
            )}
          </p>
          <div className="text-xs text-muted-foreground mt-2 bg-muted p-2 rounded">
            <p>Messages sent to video creators are private and can only be seen by you and the creator.</p>
          </div>
        </CardContent>

        <CardFooter className="p-3 border-t">
          <div className="flex w-full gap-2">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder={`Type a message to ${creatorDisplayName}...`}
              className="flex-1"
              disabled={isSending}
            />
            <Button
              size="icon"
              onClick={handleSendMessage}
              disabled={!message.trim() || isSending}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
