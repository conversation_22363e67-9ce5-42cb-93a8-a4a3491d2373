import React from 'react';
import ExportButton from './ExportButton';
import ColumnVisibilityToggle from './ColumnVisibilityToggle';

interface TableToolbarProps {
  data: any[];
  filename?: string;
  columns: {
    id: string;
    label: string;
    isVisible: boolean;
  }[];
  onToggleColumn: (columnId: string) => void;
}

const TableToolbar: React.FC<TableToolbarProps> = ({
  data,
  filename,
  columns,
  onToggleColumn,
}) => {
  return (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center space-x-2">
        <ColumnVisibilityToggle columns={columns} onToggleColumn={onToggleColumn} />
      </div>
      <ExportButton data={data} filename={filename} />
    </div>
  );
};

export default TableToolbar;
