# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/

# Production
build/
dist/
out/
.next/
.nuxt/
.output/

# Misc
.DS_Store
*.pem
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# Turborepo
.turbo

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Logs
logs
*.log
lerna-debug.log*

# Cache
.npm
.eslintcache
.stylelintcache

# Server specific
server/dist/
server/build/
server/coverage/
server/node_modules/
server/.env
server/.env.*

# Client specific
client/.next/
client/out/
client/build/
client/dist/
client/node_modules/
client/.env
client/.env.*
client/.cache/
client/public/sw.js
client/public/workbox-*.js
client/storybook-static/

# MongoDB data
data/db/

# Redis data
data/redis/

# Typesense data
data/typesense/

# Docker volumes
volumes/

# Temporary files
tmp/
temp/

# Ignore batch files and markdown files
*.md
*.bat