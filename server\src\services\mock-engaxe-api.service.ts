import { v4 as uuidv4 } from 'uuid';

/**
 * Mock Engaxe API Service
 * This service simulates the Engaxe API for development purposes
 */
class MockEngaxeApiService {
  private readonly videoDatabase: Record<string, any> = {};

  constructor() {
    // Initialize with some sample videos
    this.addSampleVideo('XLcMq2', {
      title: 'Understanding Legal Rights in Employment',
      description: 'A comprehensive guide to understanding your legal rights in the workplace, including discrimination, harassment, and wrongful termination.',
      category: 'Employment Law',
      tags: 'legal,employment,rights,workplace',
      duration: 1830, // 30:30
      views: 25000,
      likes: 1200,
      dislikes: 45,
    });

    this.addSampleVideo('a71tuY', {
      title: 'Contract Law Fundamentals',
      description: 'Learn the basics of contract law including formation, terms, conditions, and remedies for breach of contract.',
      category: 'Contract Law',
      tags: 'legal,contract,law,fundamentals',
      duration: 2400, // 40:00
      views: 18500,
      likes: 950,
      dislikes: 30,
    });

    this.addSampleVideo('Kj7Lp9', {
      title: 'Intellectual Property Rights Explained',
      description: 'An overview of intellectual property rights including patents, trademarks, copyrights, and trade secrets.',
      category: 'Intellectual Property',
      tags: 'legal,intellectual property,patents,trademarks,copyrights',
      duration: 1620, // 27:00
      views: 12000,
      likes: 780,
      dislikes: 25,
    });
  }

  /**
   * Add a sample video to the mock database
   */
  private addSampleVideo(videoId: string, details: any) {
    const now = new Date();
    const publishedDate = new Date(now.getTime() - Math.random() * 30 * 24 * 60 * 60 * 1000); // Random date in the last 30 days

    this.videoDatabase[videoId] = {
      id: videoId,
      video_id: videoId,
      user_id: '456',
      title: details.title,
      description: details.description,
      thumbnail: `https://placehold.co/480x360/333333/FFFFFF?text=${encodeURIComponent(details.title.substring(0, 20))}`,
      video_location: `https://engaxe.com/videos/${videoId}/stream`,
      youtube: '',
      views: details.views.toString(),
      likes: details.likes.toString(),
      dislikes: details.dislikes.toString(),
      duration: details.duration.toString(),
      time: publishedDate.toISOString(),
      category_name: details.category,
      category_id: '1',
      tags: details.tags,
      size: '25MB',
      owner: {
        id: '456',
        username: 'LegalExpert',
        email: '<EMAIL>',
        avatar: 'https://placehold.co/100x100/333333/FFFFFF?text=LE',
      },
      is_liked: false,
      is_disliked: false,
      is_owner: false,
      is_purchased: true,
    };
  }

  /**
   * Get video details
   */
  async getVideoDetails(videoId: string) {
    console.log(`[Mock] Getting video details for ID: ${videoId}`);
    
    // If the video exists in our database, return it
    if (this.videoDatabase[videoId]) {
      return this.videoDatabase[videoId];
    }
    
    // Otherwise, generate a mock video
    console.log(`[Mock] Video ID ${videoId} not found, generating mock data`);
    
    const mockVideo = {
      id: videoId,
      video_id: videoId,
      user_id: '456',
      title: `Engaxe Video ${videoId}`,
      description: 'This is a sample description for an Engaxe video.',
      thumbnail: `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
      video_location: `https://engaxe.com/videos/${videoId}/stream`,
      youtube: '',
      views: '1000',
      likes: '50',
      dislikes: '5',
      duration: '330',
      time: new Date().toISOString(),
      category_name: 'Education',
      category_id: '1',
      tags: 'video,sample,engaxe',
      size: '25MB',
      owner: {
        id: '456',
        username: 'Engaxe Creator',
        email: '<EMAIL>',
        avatar: 'https://placehold.co/100x100/333333/FFFFFF?text=EC',
      },
      is_liked: false,
      is_disliked: false,
      is_owner: false,
      is_purchased: true,
    };
    
    // Add to database for future requests
    this.videoDatabase[videoId] = mockVideo;
    
    return mockVideo;
  }

  /**
   * Search videos
   */
  async searchVideos(query: string) {
    console.log(`[Mock] Searching videos with query: ${query}`);

    const results = Object.values(this.videoDatabase).filter(video => {
      const searchText = `${video.title}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    });

    return results;
  }

  /**
   * Get trending videos
   */
  async getTrendingVideos() {
    console.log('[Mock] Getting trending videos');
    
    // Sort by views and return top 10
    return Object.values(this.videoDatabase)
      .sort((a, b) => parseInt(b.views) - parseInt(a.views))
      .slice(0, 10);
  }

  /**
   * Get recent videos
   */
  async getRecentVideos() {
    console.log('[Mock] Getting recent videos');
    
    // Sort by time and return top 10
    return Object.values(this.videoDatabase)
      .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime())
      .slice(0, 10);
  }
}

// Export a singleton instance
export const mockEngaxeApiService = new MockEngaxeApiService();
