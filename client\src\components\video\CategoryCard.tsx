
import { Card } from '@/components/ui/card';

interface CategoryCardProps {
  name: string;
  isActive?: boolean;
}

export default function CategoryCard({ name, isActive = false }: CategoryCardProps) {
  const imageMapping: Record<string, string> = {
    Education: '🎓',
    Business: '💼',
    Technology: '💻',
    Cooking: '🍳',
    Travel: '✈️',
    Music: '🎵',
    Sports: '⚽',
    Gaming: '🎮',
    Health: '🩺',
    Science: '🔬'
  };

  const icon = imageMapping[name] || '📺';

  return (
    <Card 
      className={`rounded-lg overflow-hidden hover:border-lingstream-accent transition-colors cursor-pointer ${
        isActive ? 'border-2 border-lingstream-accent' : ''
      }`}
    >
      <div className="p-4 text-center">
        <div className="text-4xl mb-2">{icon}</div>
        <h3 className="text-sm font-medium">{name}</h3>
      </div>
    </Card>
  );
}
