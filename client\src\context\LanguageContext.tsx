import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { loadTranslations } from '@/utils/translationLoader';

// Define all supported languages
export type SupportedLanguage =
  | 'en' // English
  | 'hi' // Hindi
  | 'mr' // Marathi
  | 'gu' // Gujarati
  | 'ta' // Tamil
  | 'te' // Telugu
  | 'bn' // Bengali
  | 'kn' // Kannada
  | 'ml'; // Malayalam

// Language metadata
export interface LanguageInfo {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  flag: string;
  rtl?: boolean;
}

// Define all available languages with their metadata
// Only includes languages that are fully supported by our translation system
export const availableLanguages: LanguageInfo[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
  { code: 'hi', name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳' },
  { code: 'mr', name: 'Marathi', nativeName: 'मराठी', flag: '🇮🇳' },
  { code: 'gu', name: 'Gujarati', nativeName: 'ગુજરાતી', flag: '🇮🇳' },
  { code: 'ta', name: 'Tamil', nativeName: 'தமிழ்', flag: '🇮🇳' },
  { code: 'te', name: 'Telugu', nativeName: 'తెలుగు', flag: '🇮🇳' },
  { code: 'bn', name: 'Bengali', nativeName: 'বাংলা', flag: '🇮🇳' },
  { code: 'kn', name: 'Kannada', nativeName: 'ಕನ್ನಡ', flag: '🇮🇳' },
  { code: 'ml', name: 'Malayalam', nativeName: 'മലയാളം', flag: '🇮🇳' },
];

// Context type definition
interface LanguageContextType {
  currentLanguage: LanguageInfo;
  setLanguage: (code: SupportedLanguage) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
}

// Create the context
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

// Provider component
export function LanguageProvider({ children }: { children: ReactNode }) {
  // Default to English or user's stored preference
  const [currentLanguageCode, setCurrentLanguageCode] = useState<SupportedLanguage>(() => {
    const storedLanguage = localStorage.getItem('preferredLanguage') as SupportedLanguage;
    return storedLanguage || 'en';
  });

  // Get the full language info object
  const currentLanguage = availableLanguages.find(lang => lang.code === currentLanguageCode) || availableLanguages[0];

  // Translations state
  const [translations, setTranslations] = useState<Record<string, string>>({
    'nav.home': 'Home',
    'nav.trending': 'Trending',
    'nav.creator_studio': 'Creator Studio',
    'nav.profile': 'Profile',
    'nav.logout': 'Log out',
    'nav.search_placeholder': 'Search videos...',
    'auth.sign_in': 'Sign In',
    'auth.sign_up': 'Sign Up',
    'language.select': 'Select Language',
    'language.indian': 'Indian Languages',
    'language.foreign': 'Foreign Languages'
  });

  // Load translations when language changes
  useEffect(() => {
    console.log(`Loading translations for ${currentLanguageCode}...`);

    try {
      // Use our helper function to load translations
      const newTranslations = loadTranslations(currentLanguageCode);
      console.log('Translations loaded successfully');
      setTranslations(newTranslations);
    } catch (error) {
      console.error(`Failed to load translations for ${currentLanguageCode}:`, error);
      // Fallback to English
      if (currentLanguageCode !== 'en') {
        console.log('Falling back to English translations...');
        setTranslations(loadTranslations('en'));
      }
    }

    // Save preference to localStorage
    localStorage.setItem('preferredLanguage', currentLanguageCode);

    // Update document language for accessibility
    document.documentElement.lang = currentLanguageCode;
    document.documentElement.dir = currentLanguage.rtl ? 'rtl' : 'ltr';
  }, [currentLanguageCode, currentLanguage.rtl]);

  // Function to change language
  const setLanguage = (code: SupportedLanguage) => {
    setCurrentLanguageCode(code);
  };

  // Translation function with interpolation
  const t = (key: string, params?: Record<string, string | number>): string => {
    let translation = translations[key] || key;

    if (!translations[key]) {
      console.warn(`Missing translation for key: ${key} in language: ${currentLanguageCode}`);
    }

    if (params) {
      for (const [paramKey, paramValue] of Object.entries(params)) {
        translation = translation.replace(new RegExp(`{${paramKey}}`, 'g'), String(paramValue));
      }
    }

    return translation;
  };

  return (
    <LanguageContext.Provider value={{ currentLanguage, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

// Custom hook to use the language context
export function useLanguage() {
  const context = useContext(LanguageContext);

  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }

  return context;
}
