import mongoose, { <PERSON>hem<PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Notification interface extending the base entity
 */
export interface INotification extends IBaseEntity {
  /** ID of the user this notification is for */
  userId: string;

  /** ID of the notification template used to generate this notification */
  templateId: string;

  /** Code of the notification template (for easier querying) */
  templateCode: string;

  /**
   * Notification type/category
   * Examples: 'content', 'social', 'account', 'billing'
   */
  category: string;

  /** Title of the notification */
  title: string;

  /** Body content of the notification */
  body: string;

  /**
   * Data payload with variables used in the notification
   * Contains the values that replaced template variables
   */
  data: Record<string, any>;

  /**
   * Priority level of the notification
   *
   * Possible values:
   * - 1: Urgent/Critical
   * - 2: High
   * - 3: Medium (default)
   * - 4: Low
   * - 5: Very Low
   */
  priority: number;

  /**
   * Current status of the notification
   *
   * Possible values:
   * - pending: Created but not yet sent
   * - sent: Successfully delivered to at least one channel
   * - failed: Failed to deliver to any channel
   * - read: User has read/viewed the notification
   * - archived: User has archived the notification
   */
  status: 'pending' | 'sent' | 'failed' | 'read' | 'archived';

  /** Whether the notification has been read by the user */
  isRead: boolean;

  /** When the notification was read by the user */
  readAt?: Date;

  /** Whether the notification has been archived by the user */
  isArchived: boolean;

  /** When the notification was archived by the user */
  archivedAt?: Date;

  /**
   * Channels this notification was sent through
   * Array can include: 'email', 'push', 'sms', 'in_app'
   */
  channels: string[];

  /** Delivery status for each channel */
  delivery: {
    /** Email delivery status */
    email?: {
      /** Whether delivery was successful */
      success: boolean;

      /** When the email was sent */
      sentAt?: Date;

      /** Error message if delivery failed */
      error?: string;

      /** Email provider's message ID for tracking */
      messageId?: string;

      /** Whether the email was opened */
      opened?: boolean;

      /** When the email was opened */
      openedAt?: Date;

      /** Whether any links in the email were clicked */
      clicked?: boolean;

      /** When a link in the email was first clicked */
      clickedAt?: Date;
    };

    /** Push notification delivery status */
    push?: {
      /** Whether delivery was successful */
      success: boolean;

      /** When the push notification was sent */
      sentAt?: Date;

      /** Error message if delivery failed */
      error?: string;

      /** Push provider's message ID for tracking */
      messageId?: string;

      /** Whether the push notification was opened */
      opened?: boolean;

      /** When the push notification was opened */
      openedAt?: Date;
    };

    /** SMS delivery status */
    sms?: {
      /** Whether delivery was successful */
      success: boolean;

      /** When the SMS was sent */
      sentAt?: Date;

      /** Error message if delivery failed */
      error?: string;

      /** SMS provider's message ID for tracking */
      messageId?: string;

      /** Whether the SMS was delivered to the device */
      delivered?: boolean;

      /** When the SMS was delivered to the device */
      deliveredAt?: Date;
    };

    /** In-app notification delivery status */
    inApp?: {
      /** Whether delivery was successful */
      success: boolean;

      /** When the in-app notification was sent */
      sentAt?: Date;

      /** Error message if delivery failed */
      error?: string;
    };
  };

  /**
   * Actions that can be performed from this notification
   * Each action has a label and a URL or deep link
   */
  actions?: Array<{
    /** Action identifier */
    id: string;

    /** Display label for the action button/link */
    label: string;

    /** URL or deep link to navigate to when action is taken */
    url: string;

    /** Whether this is the primary/main action */
    primary: boolean;
  }>;

  /**
   * Reference to the entity this notification is about
   * Allows for easy navigation to the relevant content
   */
  reference?: {
    /** Type of entity being referenced */
    type: string;

    /** ID of the referenced entity */
    id: string;

    /** URL to the referenced entity */
    url?: string;
  };

  /** When this notification should expire and be auto-archived */
  expiresAt?: Date;

  /** Whether this notification has expired */
  isExpired: boolean;
}

/**
 * Notification schema definition
 */
const NotificationSchema = new Schema<INotification>(
  {
    userId: {
      type: String,
      required: true,
      ref: 'User',
    },
    templateId: {
      type: String,
      required: true,
      ref: 'NotificationTemplate',
    },
    templateCode: {
      type: String,
      required: true,
    },
    category: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    body: {
      type: String,
      required: true,
    },
    data: {
      type: Schema.Types.Mixed,
      default: {},
    },
    priority: {
      type: Number,
      default: 3,
      min: 1,
      max: 5,
    },
    status: {
      type: String,
      enum: ['pending', 'sent', 'failed', 'read', 'archived'],
      default: 'pending',
    },
    isRead: {
      type: Boolean,
      default: false,
    },
    readAt: {
      type: Date,
    },
    isArchived: {
      type: Boolean,
      default: false,
    },
    archivedAt: {
      type: Date,
    },
    channels: {
      type: [String],
      enum: ['email', 'push', 'sms', 'in_app'],
      default: [],
    },
    delivery: {
      email: {
        success: {
          type: Boolean,
        },
        sentAt: {
          type: Date,
        },
        error: {
          type: String,
        },
        messageId: {
          type: String,
        },
        opened: {
          type: Boolean,
        },
        openedAt: {
          type: Date,
        },
        clicked: {
          type: Boolean,
        },
        clickedAt: {
          type: Date,
        },
      },
      push: {
        success: {
          type: Boolean,
        },
        sentAt: {
          type: Date,
        },
        error: {
          type: String,
        },
        messageId: {
          type: String,
        },
        opened: {
          type: Boolean,
        },
        openedAt: {
          type: Date,
        },
      },
      sms: {
        success: {
          type: Boolean,
        },
        sentAt: {
          type: Date,
        },
        error: {
          type: String,
        },
        messageId: {
          type: String,
        },
        delivered: {
          type: Boolean,
        },
        deliveredAt: {
          type: Date,
        },
      },
      inApp: {
        success: {
          type: Boolean,
        },
        sentAt: {
          type: Date,
        },
        error: {
          type: String,
        },
      },
    },
    actions: [
      {
        id: {
          type: String,
          required: true,
        },
        label: {
          type: String,
          required: true,
        },
        url: {
          type: String,
          required: true,
        },
        primary: {
          type: Boolean,
          default: false,
        },
      },
    ],
    reference: {
      type: {
        type: String,
      },
      id: {
        type: String,
      },
      url: {
        type: String,
      },
    },
    expiresAt: {
      type: Date,
    },
    isExpired: {
      type: Boolean,
      default: false,
    },
  }
);

// Merge with base schema
NotificationSchema.add(BaseSchema);

// Add indexes for common queries
NotificationSchema.index({ userId: 1, isRead: 1 });
NotificationSchema.index({ userId: 1, isArchived: 1 });
NotificationSchema.index({ userId: 1, status: 1 });
NotificationSchema.index({ userId: 1, category: 1 });
NotificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Create and export the Notification model
const NotificationModel = mongoose.model<INotification>('Notification', NotificationSchema);
export default NotificationModel;
