const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    addLogarithmVideo();
  })
  .catch(err => {
    console.error('Error connecting to MongoDB:', err);
    process.exit(1);
  });

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  url: { type: String, required: true },
  thumbnailUrl: { type: String, required: true },
  duration: { type: Number, required: true },
  userId: { type: String, required: true },
  channelId: { type: String, required: true },
  visibility: {
    type: String,
    enum: ['public', 'unlisted', 'private', 'scheduled'],
    default: 'public'
  },
  tags: { type: [String], default: [] },
  category: { type: String, required: true },
  contentRating: {
    type: String,
    enum: ['general', 'teen', 'mature', 'explicit'],
    default: 'general'
  },
  processingStatus: {
    type: String,
    enum: ['uploading', 'processing', 'ready', 'failed'],
    default: 'ready'
  },
  commentsEnabled: { type: Boolean, default: true },
  ratingsEnabled: { type: Boolean, default: true },
  embeddingEnabled: { type: Boolean, default: true },
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 }
  },
  file: {
    originalName: { type: String },
    size: { type: Number },
    mimeType: { type: String }
  },
  source: {
    type: { type: String, enum: ['import', 'embed'] },
    originalUrl: { type: String },
    platform: { type: String },
    externalId: { type: String }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  deletedAt: { type: Date },
  createdBy: { type: String },
  updatedBy: { type: String }
});

// Create the Video model
const Video = mongoose.model('Video', VideoSchema);

// Function to add logarithm video
async function addLogarithmVideo() {
  try {
    // Check if logarithm video already exists
    const existingVideo = await Video.findOne({ 
      title: { $regex: /logarithm/i } 
    });

    if (existingVideo) {
      console.log('Logarithm video already exists:', existingVideo.title);
      mongoose.disconnect();
      return;
    }

    // Generate a unique Engaxe ID (6-7 characters)
    const generateEngaxeId = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < 7; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    };

    // Create the logarithm video
    const logarithmVideo = new Video({
      id: uuidv4(),
      title: 'Logarithm Introduction | Laws Of Logarithm |Existence Of Log | Mathematics Concept Series',
      description: 'A comprehensive introduction to logarithms covering the basic concepts, laws of logarithms, and their existence. This video explains what logarithms are, how they work, and their practical applications in mathematics and science.',
      url: generateEngaxeId(), // Generate a unique Engaxe ID
      thumbnailUrl: 'https://i.ytimg.com/vi/-pg9T2a9k8w/maxresdefault.jpg',
      duration: 1200, // 20 minutes
      userId: 'system', // Using system as default user
      channelId: 'mathematics-channel', // Mathematics channel
      visibility: 'public',
      tags: ['logarithm', 'mathematics', 'math', 'algebra', 'education', 'tutorial', 'concept', 'laws'],
      category: 'Mathematics',
      contentRating: 'general',
      processingStatus: 'ready',
      commentsEnabled: true,
      ratingsEnabled: true,
      embeddingEnabled: true,
      stats: {
        views: 1500,
        likes: 120,
        dislikes: 5,
        comments: 45,
        shares: 30
      },
      file: {
        originalName: 'logarithm-introduction.mp4',
        size: 150000000, // 150MB
        mimeType: 'video/mp4'
      },
      source: {
        type: 'embed',
        originalUrl: 'https://www.youtube.com/watch?v=-pg9T2a9k8w',
        platform: 'youtube',
        externalId: '-pg9T2a9k8w'
      },
      createdBy: 'system',
      updatedBy: 'system'
    });

    // Save the video to the database
    const savedVideo = await logarithmVideo.save();
    console.log('Logarithm video added successfully:', savedVideo.title);
    console.log('Video ID:', savedVideo.id);
    console.log('Video URL (Engaxe ID):', savedVideo.url);
    
    mongoose.disconnect();
  } catch (error) {
    console.error('Error adding logarithm video:', error);
    mongoose.disconnect();
    process.exit(1);
  }
}
