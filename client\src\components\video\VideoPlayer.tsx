import { useState, useEffect, useRef } from 'react';
import { Video, Language } from '@/types';
import { Button } from '@/components/ui/button';
import { ChevronDown, Play, Pause, Volume2, VolumeX, Maximize, Minimize } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  hashIdToEngaxeId,
  isValidEngaxeId,
  extractEngaxeVideoId,
  getLanguageFlag
} from '@/utils/videoIdConverter';

interface VideoPlayerProps {
  video: Video;
  onLanguageChange?: (language: Language) => void;
  onTimeUpdate?: (time: number) => void;
}

export default function VideoPlayer({ video, onLanguageChange, onTimeUpdate }: VideoPlayerProps) {
  // Ensure video has languages array
  const safeVideo = {
    ...video,
    languages: Array.isArray(video.languages) && video.languages.length > 0
      ? video.languages
      : [{ code: 'en', name: 'English', flag: '🇺🇸', isDefault: true, url: video.url }]
  };

  // Find default language or use first language
  const defaultLanguage = safeVideo.languages.find(lang => lang.isDefault) || safeVideo.languages[0];

  const [selectedLanguage, setSelectedLanguage] = useState<Language>(defaultLanguage);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [volume, setVolume] = useState<number>(1);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  // Changed from HTMLVideoElement to HTMLIFrameElement
  const videoRef = useRef<HTMLIFrameElement>(null);
  const progressRef = useRef<HTMLDivElement>(null);

  // Function to create an iframe embed
  const createIframeEmbed = (container: HTMLElement, videoId: string) => {
    console.log(`Creating iframe embed for video ID: ${videoId}`);

    // Create the iframe
    const iframe = document.createElement('iframe');
    iframe.src = `https://engaxe.com/e/${videoId}?vid=${videoId}`;
    iframe.width = '100%';
    iframe.height = '100%';
    iframe.frameBorder = '0';
    iframe.allowFullscreen = true;
    iframe.allow = 'accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share';
    iframe.referrerPolicy = 'strict-origin-when-cross-origin';
    iframe.className = 'h5p-iframe h5p-initialized';
    iframe.scrolling = 'no';
    iframe.setAttribute('data-content-id', videoId);

    // Add styles to ensure iframe takes up full space
    iframe.style.position = 'absolute';
    iframe.style.top = '0';
    iframe.style.left = '0';
    iframe.style.width = '100%';
    iframe.style.height = '100%';
    iframe.style.border = 'none';
    iframe.style.display = 'block';

    // Add event listener to hide loading indicator when iframe loads
    iframe.onload = () => {
      console.log(`Iframe loaded for video ID: ${videoId}`);
      setIsLoading(false);

      // Start monitoring for video duration after iframe loads
      setTimeout(() => {
        console.log(`Starting duration monitoring for video ${videoId}`);
        const cleanup = monitorVideoDuration(
          'engaxe-player-container',
          (duration: number) => {
            console.log(`✅ Video duration detected: ${duration} seconds (${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')})`);
            setDuration(duration);
            if (onDurationUpdate) {
              onDurationUpdate(duration);
            }
          },
          20000 // 20 second timeout for better reliability
        );

        // Store cleanup function for later use
        (iframe as any)._durationCleanup = cleanup;
      }, 2000); // Wait 2 seconds after iframe loads for better reliability
    };

    // Make sure container has position relative for absolute positioning of iframe
    container.style.position = 'relative';
    container.style.width = '100%';
    container.style.height = '532px'; // Reduced height to match the iframe height

    // Add the iframe to the container
    container.appendChild(iframe);

    // Store a reference to the iframe
    if (videoRef) {
      videoRef.current = iframe;
    }
  };

  // We're now using the getLanguageFlag function from videoIdConverter.js

  // Initialize the player when the component mounts or when the video changes
  useEffect(() => {
    console.log('Initializing video player with video:', video);
    console.log('Video languages:', video.languages);

    // Create a deep copy of the video object to avoid mutating the original
    // Use JSON parse/stringify for a true deep copy to handle nested objects
    const processedVideo = JSON.parse(JSON.stringify(video));

    // Log the original video object for debugging
    console.log('Original video object:', JSON.stringify(video, null, 2));

    // Process languages to ensure they have all required properties
    let videoLanguages: Language[] = [];

    // If languages are directly provided, process them
    if (processedVideo.languages && Array.isArray(processedVideo.languages) && processedVideo.languages.length > 0) {
      console.log(`Processing ${processedVideo.languages.length} languages for video player`);

      // Process each language to ensure it has all required properties
      // IMPORTANT: Don't filter out languages, just ensure they have valid properties
      videoLanguages = processedVideo.languages.map((lang: Language) => {
        // Create a new language object with all required properties
        const processedLang = {
          code: lang.code || 'en',
          name: lang.name || 'English',
          flag: lang.flag || getLanguageFlag(lang.code || 'en'),
          // Ensure the language has a URL (use video URL as fallback)
          url: (lang.url && lang.url.trim() !== '') ? lang.url : (processedVideo.url || ''),
          isDefault: typeof lang.isDefault !== 'undefined' ? lang.isDefault : lang.code === 'en'
        };

        console.log(`Processed language: ${processedLang.name} (${processedLang.code}), URL: ${processedLang.url}, isDefault: ${processedLang.isDefault}`);
        return processedLang;
      });

      // Ensure at least one language is marked as default
      const hasDefaultLanguage = videoLanguages.some(lang => lang.isDefault);
      if (!hasDefaultLanguage && videoLanguages.length > 0) {
        console.log(`No default language found, setting ${videoLanguages[0].name} as default`);
        videoLanguages[0].isDefault = true;
      }
    } else {
      console.log('Video has no languages defined');
      // If no languages, create a default one
      videoLanguages = [{
        code: 'en',
        name: 'English',
        flag: '🇺🇸',
        isDefault: true,
        url: processedVideo.url || ''
      }];
      console.log('Created default English language with URL:', processedVideo.url || '');
    }

    // Update the processed video with the processed languages
    processedVideo.languages = videoLanguages;

    // IMPORTANT: Update the original video object's languages
    // This ensures the dropdown shows the correct languages
    video.languages = processedVideo.languages;

    // Log all processed languages
    console.log(`Final languages for video player:`);
    processedVideo.languages.forEach((lang: Language, index: number) => {
      console.log(`Language ${index + 1}/${processedVideo.languages.length}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}, flag: ${lang.flag}`);
    });

    // Process video URL to ensure it's a valid Engaxe ID
    if (processedVideo.url) {
      // First try to extract the Engaxe ID if it's a URL
      const extractedId = extractEngaxeVideoId(processedVideo.url);
      if (extractedId) {
        console.log(`Extracted Engaxe ID from video URL: ${extractedId} (original: ${processedVideo.url})`);
        processedVideo.url = extractedId;
      }
      // If not a valid Engaxe ID, convert hash ID to Engaxe ID
      else if (!isValidEngaxeId(processedVideo.url)) {
        console.log(`Converting video URL from hash ID ${processedVideo.url} to Engaxe ID`);
        processedVideo.url = hashIdToEngaxeId(processedVideo.url);
        console.log(`Converted to: ${processedVideo.url}`);
      }
    }

    // Process language URLs - ENHANCED VERSION
    if (processedVideo.languages && processedVideo.languages.length > 0) {
      console.log(`Processing ${processedVideo.languages.length} language URLs for video player`);

      // Process each language
      processedVideo.languages = processedVideo.languages.map((lang: Language, idx: number) => {
        // Create a new language object to avoid mutating the original
        const processedLang = { ...lang };

        console.log(`Processing language ${idx + 1}/${processedVideo.languages.length}: ${processedLang.name} (${processedLang.code}), URL: ${processedLang.url || 'none'}, isDefault: ${processedLang.isDefault || false}, flag: ${processedLang.flag || 'none'}`);

        // Ensure the language has a flag
        if (!processedLang.flag) {
          // Add flag based on language code
          processedLang.flag = getLanguageFlag(processedLang.code);
          console.log(`Added flag ${processedLang.flag} for language ${processedLang.name} (${processedLang.code})`);
        }

        // Process the URL to ensure it's a valid Engaxe ID
        if (processedLang.url) {
          // First try to extract the Engaxe ID if it's a URL
          const extractedId = extractEngaxeVideoId(processedLang.url);
          if (extractedId) {
            console.log(`Extracted Engaxe ID from language ${processedLang.name} URL: ${extractedId} (original: ${processedLang.url})`);
            processedLang.url = extractedId;
          }
          // If not a valid Engaxe ID, convert hash ID to Engaxe ID
          else if (!isValidEngaxeId(processedLang.url)) {
            console.log(`Converting language ${processedLang.name} URL from hash ID ${processedLang.url} to Engaxe ID`);
            processedLang.url = hashIdToEngaxeId(processedLang.url);
            console.log(`Converted to: ${processedLang.url}`);
          }
        } else if (processedVideo.url) {
          // If language has no URL, use the video URL
          console.log(`Language ${processedLang.name} has no URL, using video URL: ${processedVideo.url}`);
          processedLang.url = processedVideo.url;
        }

        // Validate the URL is a valid Engaxe ID
        if (!isValidEngaxeId(processedLang.url)) {
          console.warn(`Language ${processedLang.name} still has invalid URL after processing: ${processedLang.url}`);

          // As a last resort, use a known valid Engaxe ID
          const validEngaxeIds = ['XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', '4OE4QR', 'KxyzuN'];
          const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
          processedLang.url = validEngaxeIds[randomIndex];
          console.log(`Using fallback valid Engaxe ID for language ${processedLang.name}: ${processedLang.url}`);
        }

        console.log(`Final processed language ${idx + 1}: ${processedLang.name} (${processedLang.code}), URL: ${processedLang.url}, isDefault: ${processedLang.isDefault}, flag: ${processedLang.flag}`);
        return processedLang;
      });

      // Check if we have at least one default language
      const hasDefaultLanguage = processedVideo.languages.some((lang: Language) => lang.isDefault);

      // If no default language, set the first one as default
      if (!hasDefaultLanguage && processedVideo.languages.length > 0) {
        console.log(`No default language found, setting ${processedVideo.languages[0].name} as default`);
        processedVideo.languages[0].isDefault = true;
      }

      // Log all processed languages
      console.log(`Final processed languages (${processedVideo.languages.length}):`);
      processedVideo.languages.forEach((lang: Language, idx: number) => {
        console.log(`Language ${idx + 1}: ${lang.name} (${lang.code}), URL: ${lang.url}, isDefault: ${lang.isDefault}, flag: ${lang.flag}`);
      });
    }

    // Update the video object with the processed languages
    // This is important to ensure the dropdown shows the correct languages
    video.languages = processedVideo.languages;

    // Set the initial language
    if (processedVideo.languages && processedVideo.languages.length > 0) {
      // Find the default language or use the first one
      const defaultLang = processedVideo.languages.find((lang: Language) => lang.isDefault) || processedVideo.languages[0];
      setSelectedLanguage(defaultLang);
      console.log('Initial language set to:', defaultLang.name, 'URL:', defaultLang.url);

      // Log all available languages
      console.log('Available languages:');
      processedVideo.languages.forEach((lang: Language) => {
        console.log(`- ${lang.name} (${lang.code}): URL=${lang.url}, isDefault=${lang.isDefault}`);
      });
    }

    // Reset player state
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setIsLoading(true);

    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [video]);

  // Handle play/pause - simulated for iframe
  const togglePlay = () => {
    // We can't directly control an iframe, so we'll just update the state
    // In a real implementation, you would use the Engaxe API to control playback
    setIsPlaying(!isPlaying);

    // For demonstration purposes, we'll reload the iframe with autoplay parameter
    if (videoRef.current && !isPlaying) {
      const currentSrc = videoRef.current.src;
      const hasParams = currentSrc.includes('?');
      const newSrc = hasParams
        ? `${currentSrc}&autoplay=1`
        : `${currentSrc}?autoplay=1`;

      videoRef.current.src = newSrc;
    }
  };

  // Handle volume change - simulated for iframe
  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    // We can't control the volume of an iframe directly
    // In a real implementation, you would use the Engaxe API to control volume
    setIsMuted(newVolume === 0);
  };

  // Handle mute toggle - simulated for iframe
  const toggleMute = () => {
    // We can't mute an iframe directly
    // In a real implementation, you would use the Engaxe API to control muting
    setIsMuted(!isMuted);
    setVolume(isMuted ? 1 : 0);
  };

  // Handle fullscreen
  const toggleFullscreen = () => {
    try {
      // Get the container element instead of using the iframe directly
      const container = document.getElementById('engaxe-player-container');

      if (container) {
        if (document.fullscreenElement) {
          document.exitFullscreen().catch(err => {
            console.error('Error exiting fullscreen:', err);
          });
          setIsFullscreen(false);
        } else {
          // First, ensure the container has the right styles for fullscreen
          container.style.position = 'relative';
          container.style.width = '100%';
          container.style.height = '100%';

          // Use the container element for fullscreen instead of the iframe
          const requestFullscreen = container.requestFullscreen ||
                                   (container as any).mozRequestFullScreen ||
                                   (container as any).webkitRequestFullscreen ||
                                   (container as any).msRequestFullscreen;

          if (requestFullscreen) {
            // Request fullscreen with options if supported
            try {
              if ('navigationUI' in (Element.prototype as any).requestFullscreen.prototype) {
                requestFullscreen.call(container, { navigationUI: 'hide' }).catch(err => {
                  console.error('Error entering fullscreen with options:', err);
                  // Fallback to standard fullscreen
                  requestFullscreen.call(container).catch(err => {
                    console.error('Error entering fullscreen:', err);
                  });
                });
              } else {
                requestFullscreen.call(container).catch(err => {
                  console.error('Error entering fullscreen:', err);
                });
              }
              setIsFullscreen(true);
            } catch (err) {
              console.error('Error with fullscreen options:', err);
              // Fallback to standard fullscreen
              requestFullscreen.call(container).catch(err => {
                console.error('Error entering fullscreen:', err);
              });
              setIsFullscreen(true);
            }
          } else {
            console.error('Fullscreen API is not supported in this browser');
          }
        }
      } else {
        console.error('Video container element not found');
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }
  };

  // Handle exit fullscreen
  const exitFullscreen = () => {
    try {
      if (document.fullscreenElement) {
        // Exit fullscreen
        document.exitFullscreen().catch(err => {
          console.error('Error exiting fullscreen:', err);

          // Try alternative methods if standard method fails
          try {
            if ((document as any).mozCancelFullScreen) {
              (document as any).mozCancelFullScreen();
            } else if ((document as any).webkitExitFullscreen) {
              (document as any).webkitExitFullscreen();
            } else if ((document as any).msExitFullscreen) {
              (document as any).msExitFullscreen();
            }
          } catch (altErr) {
            console.error('Alternative exit fullscreen methods failed:', altErr);
          }
        });

        // Update state
        setIsFullscreen(false);

        // Reset container styles if needed
        const container = document.getElementById('engaxe-player-container');
        if (container) {
          // Reset any inline styles that might interfere with normal display
          container.style.position = '';
          container.style.top = '';
          container.style.left = '';
          container.style.width = '';
          container.style.height = '';
          container.style.zIndex = '';
        }
      }
    } catch (error) {
      console.error('Error exiting fullscreen:', error);
      setIsFullscreen(false); // Ensure state is updated even if there's an error
    }
  };

  // Handle progress bar click - simulated for iframe
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (progressRef.current) {
      const rect = progressRef.current.getBoundingClientRect();
      const pos = (e.clientX - rect.left) / rect.width;
      const newTime = pos * duration;

      // We can't set the current time of an iframe directly
      // In a real implementation, you would use the Engaxe API to seek
      setCurrentTime(newTime);

      // For demonstration purposes, we'll reload the iframe with a start parameter
      if (videoRef.current) {
        const url = new URL(videoRef.current.src);
        url.searchParams.set('start', Math.floor(newTime).toString());
        videoRef.current.src = url.toString();
      }
    }
  };

  // Handle time update - simulated for iframe
  const handleTimeUpdate = () => {
    // We can't get the current time from an iframe, so we'll simulate it
    if (isPlaying) {
      setCurrentTime(prev => {
        const newTime = prev + 0.1;
        const updatedTime = newTime > duration ? duration : newTime;

        // Notify parent component about time update
        if (onTimeUpdate) {
          onTimeUpdate(updatedTime);
        }

        return updatedTime;
      });
    }
  };

  // Set up a timer to update the time
  useEffect(() => {
    const timer = isPlaying ? setInterval(handleTimeUpdate, 100) : null;
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [isPlaying, duration]);

  // Set a default duration for the video since we can't get it from the iframe
  useEffect(() => {
    setDuration(100);
  }, []);

  // Add fullscreen change event listener
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    // Add event listeners for fullscreen change
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      // Clean up event listeners
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // Load saved language preference from localStorage
  useEffect(() => {
    // Always use safeVideo which guarantees a valid languages array
    try {
      const savedLanguageCode = localStorage.getItem('selectedLanguageCode');
      console.log(`Found saved language preference in localStorage: ${savedLanguageCode || 'none'}`);

      if (savedLanguageCode) {
        // Find the language with the saved code
        const savedLanguage = safeVideo.languages.find(lang => lang.code === savedLanguageCode);

        if (savedLanguage) {
          console.log(`Applying saved language preference: ${savedLanguage.name} (${savedLanguage.code})`);
          handleLanguageChange(savedLanguage);
        } else {
          console.log(`Saved language ${savedLanguageCode} not available for this video`);
        }
      }
    } catch (error) {
      console.error('Error loading language preference from localStorage:', error);
    }
  }, [safeVideo]);

  // Function to extract Engaxe video ID from URL
  const extractEngaxeVideoId = (url: string): string | null => {
    if (!url) return null;

    // Trim the input
    const trimmedUrl = url.trim();

    // If it's just a video ID (no slashes, dots, or protocol)
    if (!trimmedUrl.includes('/') && !trimmedUrl.includes('.') && !trimmedUrl.includes(':')) {
      // Check for standard Engaxe ID format (6-7 alphanumeric characters)
      if (/^[a-zA-Z0-9]{6,7}$/.test(trimmedUrl)) {
        console.log(`Input appears to be a standard Engaxe ID: ${trimmedUrl}`);
        return trimmedUrl;
      }
      return null;
    }

    // Engaxe URL patterns - support multiple formats, but only match 6-7 character IDs
    const patterns = [
      // Format: engaxe.com/videos/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/videos\/([a-zA-Z0-9]{6,7})/i,
      // Format: engaxe.com/v/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([a-zA-Z0-9]{6,7})/i,
      // Format: engaxe.com/watch/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/watch\/([a-zA-Z0-9]{6,7})/i,
      // Format: engaxe.com/embed/[id] - only match 6-7 character IDs
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/embed\/([a-zA-Z0-9]{6,7})/i
    ];

    // Try each pattern
    for (const pattern of patterns) {
      const match = trimmedUrl.match(pattern);
      if (match && match[1]) {
        console.log(`Successfully extracted video ID: ${match[1]} using pattern: ${pattern}`);
        return match[1];
      }
    }

    // If no pattern matches, try a simple extraction as a fallback
    try {
      // Try to parse as URL first
      const urlObj = new URL(trimmedUrl.startsWith('http') ? trimmedUrl : `https://${trimmedUrl}`);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart && /^[a-zA-Z0-9]{6,7}$/.test(lastPart) && !lastPart.includes('.') && !lastPart.includes('?')) {
          console.log(`Using URL path extraction, got valid Engaxe ID: ${lastPart}`);
          return lastPart;
        }
      }
    } catch (error) {
      // If URL parsing fails, try simple string splitting
      const parts = trimmedUrl.split('/');
      const lastPart = parts[parts.length - 1];
      if (lastPart && /^[a-zA-Z0-9]{6,7}$/.test(lastPart) && !lastPart.includes('.') && !lastPart.includes('?')) {
        console.log(`Using fallback string extraction, got valid Engaxe ID: ${lastPart}`);
        return lastPart;
      }
    }

    console.log('Failed to extract video ID from input');
    return null;
  };

  // Handle language switch - ENHANCED VERSION
  const handleLanguageChange = (language: Language) => {
    console.log(`Switching to language: ${language.name}`, language);

    // Store the current language
    setSelectedLanguage(language);

    // Notify parent component about language change
    if (onLanguageChange) {
      onLanguageChange(language);
    }

    // Save the language selection to localStorage for persistence
    try {
      localStorage.setItem('selectedLanguageCode', language.code);
      console.log(`Saved language preference to localStorage: ${language.code}`);
    } catch (error) {
      console.error('Failed to save language preference to localStorage:', error);
    }

    // Show loading indicator
    setIsLoading(true);

    // Get a valid Engaxe ID for the video source
    let engaxeId: string;

    // Validate and process the language URL
    if (language.url) {
      // Check if the URL is already a valid Engaxe ID (6-7 alphanumeric characters)
      if (isValidEngaxeId(language.url)) {
        console.log(`Language URL is already a valid Engaxe ID: ${language.url}`);
        engaxeId = language.url;
      } else {
        // First try to extract the Engaxe ID if it's a URL
        const extractedId = extractEngaxeVideoId(language.url);
        if (extractedId) {
          console.log(`Extracted Engaxe ID from language URL: ${extractedId}`);
          engaxeId = extractedId;
        } else {
          // If extraction fails, try the hashIdToEngaxeId function
          const convertedId = hashIdToEngaxeId(language.url);
          if (isValidEngaxeId(convertedId)) {
            console.log(`Converted hash ID to Engaxe ID: ${convertedId}`);
            engaxeId = convertedId;
          } else {
            // As a last resort, use a known valid Engaxe ID
            console.warn('Failed to get valid Engaxe ID from language URL, using fallback');
            const validEngaxeIds = ['XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', '4OE4QR', 'KxyzuN'];
            const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
            engaxeId = validEngaxeIds[randomIndex];
            console.log(`Using fallback Engaxe ID: ${engaxeId}`);
          }
        }
      }
    } else {
      // If no language URL, use the video URL or a fallback
      console.warn('No language URL provided, using video URL or fallback');

      if (video.url) {
        const extractedId = extractEngaxeVideoId(video.url);
        if (extractedId) {
          console.log(`Extracted Engaxe ID from video URL: ${extractedId}`);
          engaxeId = extractedId;
        } else if (isValidEngaxeId(video.url)) {
          console.log(`Video URL is already a valid Engaxe ID: ${video.url}`);
          engaxeId = video.url;
        } else {
          const convertedId = hashIdToEngaxeId(video.url);
          if (isValidEngaxeId(convertedId)) {
            console.log(`Converted video hash ID to Engaxe ID: ${convertedId}`);
            engaxeId = convertedId;
          } else {
            // Fallback to a known valid ID
            const validEngaxeIds = ['XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', '4OE4QR', 'KxyzuN'];
            const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
            engaxeId = validEngaxeIds[randomIndex];
            console.log(`Using fallback Engaxe ID: ${engaxeId}`);
          }
        }
      } else {
        // No video URL either, use a fallback
        const validEngaxeIds = ['XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', '4OE4QR', 'KxyzuN'];
        const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
        engaxeId = validEngaxeIds[randomIndex];
        console.log(`No video URL, using fallback Engaxe ID: ${engaxeId}`);
      }
    }

    // Log detailed information about the language change
    console.log(`Language change details:
      - Language: ${language.name} (${language.code})
      - Original URL: ${language.url || 'none'}
      - Is valid Engaxe ID: ${language.url ? isValidEngaxeId(language.url) : false}
      - Final Engaxe ID: ${engaxeId}
    `);

    // Get the container element
    const container = document.getElementById('engaxe-player-container');

    if (container) {
      // Clear the container
      container.innerHTML = '';

      // Show loading indicator
      setIsLoading(true);

      // Try to use the ngxEmbed function if available
      if (window.ngxEmbed) {
        try {
          console.log(`Using ngxEmbed function with ID: ${engaxeId}`);
          window.ngxEmbed('engaxe-player-container', engaxeId);

          // Hide loading indicator after a short delay
          setTimeout(() => {
            setIsLoading(false);

            // Start monitoring for video duration after language change
            console.log(`Starting duration monitoring after language change for ${engaxeId}`);
            const cleanup = monitorVideoDuration(
              'engaxe-player-container',
              (duration: number) => {
                console.log(`✅ Video duration detected after language change: ${duration} seconds (${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')})`);
                setDuration(duration);
                if (onDurationUpdate) {
                  onDurationUpdate(duration);
                }
              },
              20000 // 20 second timeout
            );
          }, 1000);
        } catch (error) {
          console.error('Error using ngxEmbed:', error);
          // Fall back to iframe method
          createIframeEmbed(container, engaxeId);
        }
      } else {
        // Fall back to iframe method
        console.log('ngxEmbed function not available, using iframe method');
        createIframeEmbed(container, engaxeId);
      }

      // Log the language change for debugging
      console.log(`Language changed to ${language.name} (${language.code}) with URL: ${engaxeId}`);
    } else {
      console.error('No container element found for video player');
      // Hide loading indicator since we can't update the video
      setIsLoading(false);
    }
  };

  // Format time in YouTube-style format (H:MM:SS or M:SS)
  const formatTime = (time: number) => {
    const hours = Math.floor(time / 3600);
    const minutes = Math.floor((time % 3600) / 60);
    const seconds = Math.floor(time % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  };

  return (
    <div className="relative">
      {/* Language selection dropdown */}
      <div className="absolute right-4 top-16 z-20">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="secondary" size="sm" className="flex items-center gap-2 bg-white border border-black text-black">
              <span className="text-base mr-1 inline-block align-middle translate-y-0.5">{selectedLanguage.flag || '🌐'}</span>
              {selectedLanguage.name}
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="bg-lingstream-card border-gray-700">
            <DropdownMenuLabel>
              {safeVideo.languages && safeVideo.languages.length > 1
                ? `Select Language (${safeVideo.languages.length} available)`
                : 'Select Language'}
            </DropdownMenuLabel>
            {/* Debug info is logged in useEffect and in the map function */}

            {/* Debug info for languages */}
            {(() => {
              console.log('Languages in dropdown:', JSON.stringify(safeVideo.languages));
              if (Array.isArray(safeVideo.languages)) {
                console.log(`Found ${safeVideo.languages.length} languages in video object:`);
                safeVideo.languages.forEach((lang: Language, idx: number) => {
                  console.log(`Language ${idx + 1}: ${lang.name || 'Unknown'} (${lang.code || 'unknown'}), URL: ${lang.url || 'none'}, isDefault: ${lang.isDefault || false}, flag: ${lang.flag || 'none'}`);
                });
              } else {
                console.log('No languages array found in video object');
              }
              return null;
            })()}

            {/* Map all languages from the video object - ENHANCED VERSION */}
            {(() => {
              // Use the safeVideo languages array
              const languages = safeVideo.languages;

              console.log(`Rendering ${languages.length} language options in dropdown`);

              return languages.map((language: Language, index: number) => {
                // Ensure language has all required properties
                const safeLanguage = {
                  code: language.code || `lang-${index}`,
                  name: language.name || `Language ${index + 1}`,
                  flag: language.flag || getLanguageFlag(language.code || 'en'),
                  url: language.url || safeVideo.url || '',
                  isDefault: !!language.isDefault
                };

                // Log outside of the JSX return
                console.log(`Rendering language option ${index + 1}/${languages.length}: ${safeLanguage.name} (${safeLanguage.code}), URL: ${safeLanguage.url}, isDefault: ${safeLanguage.isDefault}`);

                return (
                  <DropdownMenuItem
                    key={`${safeLanguage.code}-${index}`}
                    onClick={() => handleLanguageChange(safeLanguage)}
                    className={selectedLanguage.code === safeLanguage.code ? 'bg-lingstream-hover' : ''}
                  >
                    <span className="text-base mr-2">{safeLanguage.flag}</span>
                    {safeLanguage.name}
                    {safeLanguage.isDefault && (
                      <span className="ml-1 text-xs text-lingstream-muted">(Default)</span>
                    )}
                    {selectedLanguage.code === safeLanguage.code && (
                      <span className="ml-2 h-1.5 w-1.5 rounded-full bg-lingstream-accent" />
                    )}
                  </DropdownMenuItem>
                );
              });
            })()}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Loading overlay */}
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-black bg-opacity-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-lingstream-accent mx-auto mb-2"></div>
            <p className="text-white text-sm">Loading {selectedLanguage.name} version, please wait...</p>
          </div>
        </div>
      )}

      {/* Video player */}
      <div className={`rounded-lg overflow-hidden ${isFullscreen ? 'fullscreen-video-container' : ''}`}>
        <div className={`relative ${isFullscreen ? '' : 'h5p-video-container'}`}>
          {/* Minimize button - only visible in fullscreen mode */}
          <div className="video-minimize-button-container">
            <Button
              variant="ghost"
              size="sm"
              className="video-minimize-button"
              onClick={exitFullscreen}
              aria-label="Exit Fullscreen"
            >
              <Minimize className="h-5 w-5" />
            </Button>
          </div>

          {/* Engaxe Embed */}
          <div className="w-full h-full tag_main_player">
            <div className="player-video">
              <div className="video-player position-relative">
                <div
                  id="video-container"
                  className="h5p-iframe-wrapper"
                  style={{ backgroundColor: 'transparent' }}
                >
                  <div
                    id="engaxe-player-container"
                    className={`w-full h-full ${isFullscreen ? 'fullscreen-active' : ''}`}
              ref={containerRef => {
                if (containerRef) {
                  // Clear any existing content
                  containerRef.innerHTML = '';

                  // Get the video ID to use
                  const getVideoId = () => {
                    console.log('Getting video ID for selected language:', selectedLanguage);

                    // Ensure we have a valid URL
                    if (!selectedLanguage || !selectedLanguage.url) {
                      console.warn('No selected language or URL available, using fallback');

                      // Try to use the video URL first
                      if (safeVideo.url && isValidEngaxeId(safeVideo.url)) {
                        console.log(`Using video URL as fallback: ${safeVideo.url}`);
                        return safeVideo.url;
                      }

                      // Use a known valid Engaxe ID as fallback
                      const validEngaxeIds = ['XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', '4OE4QR', 'KxyzuN'];
                      const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
                      return validEngaxeIds[randomIndex];
                    }

                    // Check if the URL is already a valid Engaxe ID (6-7 alphanumeric characters)
                    if (isValidEngaxeId(selectedLanguage.url)) {
                      console.log(`Using valid Engaxe ID: ${selectedLanguage.url}`);
                      return selectedLanguage.url;
                    }

                    // If not a valid ID, try to extract the Engaxe ID if it's a URL
                    const extractedId = extractEngaxeVideoId(selectedLanguage.url);
                    if (extractedId) {
                      console.log(`Using extracted ID: ${extractedId}`);
                      return extractedId;
                    }

                    // If all else fails, try to convert hash ID to Engaxe ID
                    const fallbackId = hashIdToEngaxeId(selectedLanguage.url);

                    // Validate the fallback ID
                    if (isValidEngaxeId(fallbackId)) {
                      console.log(`Using converted fallback ID: ${fallbackId}`);
                      return fallbackId;
                    }

                    // As a last resort, use a known valid Engaxe ID
                    console.warn('All attempts to get valid Engaxe ID failed, using hardcoded fallback');
                    const validEngaxeIds = ['XLcMq2', 'xW36l7', 'suZKhW', 'wollzl', 'axHkJa', '4OE4QR', 'KxyzuN'];
                    const randomIndex = Math.floor(Math.random() * validEngaxeIds.length);
                    return validEngaxeIds[randomIndex];
                  };

                  const videoId = getVideoId();
                  console.log(`Final video ID for embedding: ${videoId}`);

                  // Try to use the ngxEmbed function if available
                  if (window.ngxEmbed) {
                    try {
                      console.log(`Using ngxEmbed function with ID: ${videoId}`);
                      window.ngxEmbed('engaxe-player-container', videoId);
                      setIsLoading(false);

                      // Start monitoring for video duration after initial load
                      setTimeout(() => {
                        console.log(`Starting duration monitoring for initial load of ${videoId}`);
                        const cleanup = monitorVideoDuration(
                          'engaxe-player-container',
                          (duration: number) => {
                            console.log(`✅ Video duration detected on initial load: ${duration} seconds (${Math.floor(duration / 60)}:${(duration % 60).toString().padStart(2, '0')})`);
                            setDuration(duration);
                            if (onDurationUpdate) {
                              onDurationUpdate(duration);
                            }
                          },
                          20000 // 20 second timeout
                        );
                      }, 2000);
                    } catch (error) {
                      console.error('Error using ngxEmbed:', error);
                      // Fall back to iframe method
                      createIframeEmbed(containerRef, videoId);
                    }
                  } else {
                    // Fall back to iframe method
                    console.log('ngxEmbed function not available, using iframe method');
                    createIframeEmbed(containerRef, videoId);
                  }
                }
              }}
            />
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>


    </div>
  );
}
