import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { MessageSquare, Eye, ThumbsUp, ThumbsDown, TrendingUp } from 'lucide-react';
import { useLanguage } from '@/context/LanguageContext';

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  trend?: number;
  className?: string;
}

const StatsCard = ({ title, value, icon, description, trend, className }: StatsCardProps) => (
  <Card className={className}>
    <CardHeader className="flex flex-row items-center justify-between pb-2">
      <CardTitle className="text-sm font-medium text-muted-foreground">
        {title}
      </CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-4xl font-bold">{value}</div>
      {description && (
        <p className="text-xs text-muted-foreground mt-1">{description}</p>
      )}
      {trend !== undefined && (
        <div className="flex items-center mt-2">
          <span className="text-2xl font-bold">{trend}%</span>
          <TrendingUp className="h-4 w-4 ml-2 text-green-500" />
        </div>
      )}
    </CardContent>
  </Card>
);

export default function DashboardStats() {
  const { t } = useLanguage();

  // Mock data for dashboard stats
  const stats = {
    totalComments: 0,
    totalViews: 0,
    totalLikes: 0,
    totalDislikes: 0,
    commentsTrend: 0,
    viewsTrend: 0,
    likesTrend: 0,
    dislikesTrend: 0
  };

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title={t('creator.total_comments')}
          value={stats.totalComments}
          icon={<MessageSquare className="h-5 w-5 text-teal-500" />}
          className="bg-teal-50/10"
        />
        <StatsCard
          title={t('creator.total_views')}
          value={stats.totalViews}
          icon={<Eye className="h-5 w-5 text-blue-500" />}
          className="bg-blue-50/10"
        />
        <StatsCard
          title={t('creator.total_likes')}
          value={stats.totalLikes}
          icon={<ThumbsUp className="h-5 w-5 text-amber-500" />}
          className="bg-amber-50/10"
        />
        <StatsCard
          title={t('creator.total_dislikes')}
          value={stats.totalDislikes}
          icon={<ThumbsDown className="h-5 w-5 text-purple-500" />}
          className="bg-purple-50/10"
        />
      </div>

      <h2 className="text-xl font-semibold mt-8 mb-4">{t('creator.this_month')}</h2>

      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title={t('creator.comments')}
          value={stats.commentsTrend}
          icon={<MessageSquare className="h-5 w-5 text-teal-500" />}
          trend={stats.commentsTrend}
          className="bg-teal-50/10"
        />
        <StatsCard
          title={t('video.views')}
          value={stats.viewsTrend}
          icon={<Eye className="h-5 w-5 text-blue-500" />}
          trend={stats.viewsTrend}
          className="bg-blue-50/10"
        />
        <StatsCard
          title={t('video.likes')}
          value={stats.likesTrend}
          icon={<ThumbsUp className="h-5 w-5 text-amber-500" />}
          trend={stats.likesTrend}
          className="bg-amber-50/10"
        />
        <StatsCard
          title={t('video.dislikes')}
          value={stats.dislikesTrend}
          icon={<ThumbsDown className="h-5 w-5 text-purple-500" />}
          trend={stats.dislikesTrend}
          className="bg-purple-50/10"
        />
      </div>
    </div>
  );
}
