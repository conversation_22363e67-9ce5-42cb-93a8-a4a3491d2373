
import { useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Trophy, ChevronDown, ChevronUp } from "lucide-react";

interface LeaderboardUser {
  id: string;
  username: string;
  avatar: string;
  score: number;
  rank: number;
}

interface LeaderboardTableProps {
  users: LeaderboardUser[];
  language?: string;
}

export default function LeaderboardTable({ users, language }: LeaderboardTableProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-lingstream-card p-4 rounded-lg">
      <div
        className="flex items-center justify-between mb-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <h3 className="font-medium flex items-center gap-2">
          <Trophy className="h-4 w-4 text-yellow-500" />
          Global Leaderboard {language ? `for ${language}` : ''}
        </h3>
        <div className="flex items-center gap-2">
          <span className="text-xs text-lingstream-muted">Updated hourly</span>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-lingstream-muted" />
          ) : (
            <ChevronDown className="h-4 w-4 text-lingstream-muted" />
          )}
        </div>
      </div>

      {isExpanded && (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">Rank</TableHead>
              <TableHead>Learner</TableHead>
              <TableHead className="text-right">Points</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell className="font-medium">
                  {user.rank === 1 ? (
                    <span className="text-yellow-500">🥇</span>
                  ) : user.rank === 2 ? (
                    <span className="text-gray-400">🥈</span>
                  ) : user.rank === 3 ? (
                    <span className="text-amber-700">🥉</span>
                  ) : (
                    user.rank
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={user.avatar} alt={user.username} />
                      <AvatarFallback>{user.username[0]}</AvatarFallback>
                    </Avatar>
                    <span>{user.username}</span>
                  </div>
                </TableCell>
                <TableCell className="text-right">{user.score.toLocaleString()}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
