import { FastifyRequest, FastifyReply } from 'fastify';
import { ReportModel, VideoModel, UserModel } from '../models';
import { createBadRequestError, createNotFoundError, createForbiddenError } from '../utils/errors/AppError';
import { ErrorCodes } from '../utils/errors/errorCodes';
import { generateId } from '../utils/generators';
import { AuthenticatedRequest } from '../types/fastify';

/**
 * Controller for handling report-related operations
 */
class ReportController {
  /**
   * Get all reports with filtering and pagination
   */
  getAllReports = async (request: FastifyRequest<{
    Querystring: {
      page?: number | string;
      limit?: number | string;
      status?: string;
      reportType?: string;
      search?: string;
      sortBy?: string;
      sortDirection?: 'asc' | 'desc';
    }
  }>, reply: FastifyReply) => {
    try {
      // Parse query parameters
      const page = request.query.page ? Number(request.query.page) : 1;
      const limit = request.query.limit ? Number(request.query.limit) : 10;
      const skip = (page - 1) * limit;
      const status = request.query.status;
      const reportType = request.query.reportType;
      const search = request.query.search;
      const sortBy = request.query.sortBy || 'createdAt';
      const sortDirection = request.query.sortDirection || 'desc';

      // Build query
      const query: any = { deletedAt: null };

      // Filter by status if provided
      if (status && status !== 'all') {
        query.status = status;
      }

      // Filter by report type if provided
      if (reportType) {
        query.reportType = reportType;
      }

      // Search functionality
      if (search) {
        query.$or = [
          { reporterUsername: { $regex: search, $options: 'i' } },
          { contentTitle: { $regex: search, $options: 'i' } },
          { reason: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ];
      }

      // Count total reports matching the query
      const total = await ReportModel.countDocuments(query);

      // Get reports with pagination and sorting
      const sortOptions: any = {};
      sortOptions[sortBy] = sortDirection === 'asc' ? 1 : -1;

      const reports = await ReportModel.find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit);

      return reply.code(200).send({
        success: true,
        data: {
          reports,
          pagination: {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit),
          },
        },
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };

  /**
   * Get a report by ID
   */
  getReportById = async (request: FastifyRequest<{
    Params: {
      id: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;

      // Find report
      const report = await ReportModel.findOne({ id, deletedAt: null });
      if (!report) {
        throw createNotFoundError('Report not found', ErrorCodes.NOT_FOUND);
      }

      return reply.code(200).send({
        success: true,
        data: report,
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };

  /**
   * Create a new report
   */
  createReport = async (request: FastifyRequest<{
    Body: {
      reportType: 'video' | 'comment' | 'user' | 'channel';
      targetId: string;
      category: string;
      reason: string;
      description?: string;
      evidence?: string[];
    }
  }>, reply: FastifyReply) => {
    try {
      const { reportType, targetId, category, reason, description, evidence } = request.body;

      // Get reporter info if authenticated
      let reporterId = null;
      let reporterUsername = 'Anonymous';

      if ((request as any).user) {
        reporterId = (request as any).user.id;
        const reporter = await UserModel.findOne({ id: reporterId });
        if (reporter) {
          reporterUsername = reporter.username;
        }
      }

      // Get content title based on report type
      let contentTitle = '';
      if (reportType === 'video') {
        const video = await VideoModel.findOne({ id: targetId });
        if (video) {
          contentTitle = video.title;
        }
      }
      // Add similar logic for other report types as needed

      // Create report
      const report = new ReportModel({
        id: generateId(),
        reportType,
        targetId,
        reporterId: reporterId || 'anonymous',
        reporterUsername,
        category,
        reason,
        description: description || '',
        evidence: evidence || [],
        contentTitle,
        status: 'pending',
        priority: 'medium',
        createdBy: reporterId || 'anonymous',
        updatedBy: reporterId || 'anonymous',
        adminComments: [],
        history: [
          {
            action: 'created',
            actor: reporterId || 'anonymous',
            timestamp: new Date(),
            note: 'Report created',
          },
        ],
      });

      await report.save();

      return reply.code(201).send({
        success: true,
        message: 'Report created successfully',
        data: report,
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };

  /**
   * Update a report's status
   */
  updateReportStatus = async (request: AuthenticatedRequest<{
    Params: {
      id: string;
    };
    Body: {
      status: 'pending' | 'investigating' | 'resolved' | 'rejected' | 'safe';
      note?: string;
      adminComment?: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { status, note, adminComment } = request.body;
      const userId = request.user.id;

      // Find report
      const report = await ReportModel.findOne({ id, deletedAt: null });
      if (!report) {
        throw createNotFoundError('Report not found', ErrorCodes.NOT_FOUND);
      }

      // Update status
      report.status = status;

      // Add admin comment if provided
      if (adminComment) {
        report.adminComments = report.adminComments || [];
        report.adminComments.push({
          comment: adminComment,
          addedBy: userId,
          addedAt: new Date()
        });
      }

      // Add resolution if status is resolved or safe
      if (status === 'resolved' || status === 'safe') {
        report.resolution = {
          action: status === 'safe' ? 'marked_safe' : 'resolved',
          note: note || '',
          takenBy: userId,
          takenAt: new Date(),
        };
      }

      // Add to history
      report.history.push({
        action: 'status_changed',
        actor: userId,
        timestamp: new Date(),
        note: `Status changed to ${status}${note ? ': ' + note : ''}`,
      });

      await report.save();

      return reply.code(200).send({
        success: true,
        message: 'Report status updated successfully',
        data: report,
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };

  /**
   * Delete a report (soft delete)
   */
  deleteReport = async (request: AuthenticatedRequest<{
    Params: {
      id: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const userId = request.user.id;

      // Find report
      const report = await ReportModel.findOne({ id, deletedAt: null });
      if (!report) {
        throw createNotFoundError('Report not found', ErrorCodes.NOT_FOUND);
      }

      // Soft delete
      report.deletedAt = new Date();
      report.deletedBy = userId;
      report.updatedAt = new Date();
      report.updatedBy = userId;

      await report.save();

      return reply.code(200).send({
        success: true,
        message: 'Report deleted successfully',
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };

  /**
   * Bulk update report statuses
   */
  bulkUpdateReportStatus = async (request: AuthenticatedRequest<{
    Body: {
      ids: string[];
      status: 'pending' | 'investigating' | 'resolved' | 'rejected' | 'safe';
      note?: string;
    }
  }>, reply: FastifyReply) => {
    try {
      const { ids, status, note } = request.body;
      const userId = request.user.id;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        throw createBadRequestError('No report IDs provided', ErrorCodes.BAD_REQUEST);
      }

      // Update all reports
      const updatePromises = ids.map(async (id) => {
        const report = await ReportModel.findOne({ id, deletedAt: null });
        if (!report) return null;

        // Update status
        report.status = status;

        // Add resolution if status is resolved or safe
        if (status === 'resolved' || status === 'safe') {
          report.resolution = {
            action: status === 'safe' ? 'marked_safe' : 'resolved',
            note: note || '',
            takenBy: userId,
            takenAt: new Date(),
          };
        }

        // Add to history
        report.history.push({
          action: 'status_changed',
          actor: userId,
          timestamp: new Date(),
          note: `Status changed to ${status}${note ? ': ' + note : ''}`,
        });

        return report.save();
      });

      await Promise.all(updatePromises);

      return reply.code(200).send({
        success: true,
        message: `${ids.length} reports updated successfully`,
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };

  /**
   * Bulk delete reports
   */
  bulkDeleteReports = async (request: AuthenticatedRequest<{
    Body: {
      ids: string[];
    }
  }>, reply: FastifyReply) => {
    try {
      const { ids } = request.body;
      const userId = request.user.id;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        throw createBadRequestError('No report IDs provided', ErrorCodes.BAD_REQUEST);
      }

      // Soft delete all reports
      const updatePromises = ids.map(async (id) => {
        const report = await ReportModel.findOne({ id, deletedAt: null });
        if (!report) return null;

        // Soft delete
        report.deletedAt = new Date();
        report.deletedBy = userId;
        report.updatedAt = new Date();
        report.updatedBy = userId;

        return report.save();
      });

      await Promise.all(updatePromises);

      return reply.code(200).send({
        success: true,
        message: `${ids.length} reports deleted successfully`,
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };

  /**
   * Create a new issue report (can be anonymous)
   */
  createIssueReport = async (request: FastifyRequest<{
    Body: {
      title: string;
      type: 'bug' | 'suggestion' | 'other';
      description: string;
      evidence?: string[];
    }
  }>, reply: FastifyReply) => {
    try {
      console.log('Received issue report request:', request.body);
      console.log('Request URL:', request.url);
      console.log('Request method:', request.method);
      const { title, type, description, evidence } = request.body;

      // Get reporter info if authenticated
      let reporterId = null;
      let reporterUsername = 'Anonymous';

      if ((request as any).user) {
        reporterId = (request as any).user.id;
        const reporter = await UserModel.findOne({ id: reporterId });
        if (reporter) {
          reporterUsername = reporter.username;
        }
      }

      // Create report
      const report = new ReportModel({
        id: generateId(),
        reportType: 'issue',
        targetId: 'system', // Using 'system' as a placeholder for issue reports
        reporterId: reporterId || 'anonymous',
        reporterUsername,
        category: type,
        reason: title,
        description: description || '',
        evidence: evidence || [],
        status: 'pending',
        priority: 'medium',
        createdBy: reporterId || 'anonymous',
        updatedBy: reporterId || 'anonymous',
        history: [
          {
            action: 'created',
            actor: reporterId || 'anonymous',
            timestamp: new Date(),
            note: 'Issue report created',
          },
        ],
      });

      await report.save();

      return reply.code(201).send({
        success: true,
        message: 'Issue report created successfully',
        data: report,
      });
    } catch (error: any) {
      request.log.error(error);
      throw error;
    }
  };
}

const reportController = new ReportController();
export default reportController;
