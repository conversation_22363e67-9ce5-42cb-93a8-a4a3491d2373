import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Home,
  Database,
  CheckCircle,
  Server,
  HardDrive,
  Cpu,
  RefreshCw,
  Clock,
  FileText,
  Settings,
  Info,
  Gauge,
  Wrench
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
// import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// System requirement interface
interface SystemRequirement {
  name: string;
  required: string;
  current: string;
  status: 'ok' | 'warning' | 'error';
  icon: React.ReactNode;
}

// System status interface
interface SystemStatus {
  name: string;
  status: 'operational' | 'degraded' | 'outage';
  lastChecked: string;
  uptime: string;
  icon: React.ReactNode;
}

export default function SystemStatusPage() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('status');

  // System requirements data
  const systemRequirements: SystemRequirement[] = [
    {
      name: 'PHP Version',
      required: '7.4.0',
      current: '8.1.2',
      status: 'ok',
      icon: <FileText className="h-4 w-4" />
    },
    {
      name: 'MySQL Version',
      required: '5.7.0',
      current: '8.0.28',
      status: 'ok',
      icon: <Database className="h-4 w-4" />
    },
    {
      name: 'Memory Limit',
      required: '128M',
      current: '256M',
      status: 'ok',
      icon: <Database className="h-4 w-4" />
    },
    {
      name: 'Post Max Size',
      required: '8M',
      current: '64M',
      status: 'ok',
      icon: <FileText className="h-4 w-4" />
    },
    {
      name: 'Upload Max Filesize',
      required: '8M',
      current: '32M',
      status: 'ok',
      icon: <FileText className="h-4 w-4" />
    },
    {
      name: 'Max Execution Time',
      required: '30',
      current: '60',
      status: 'ok',
      icon: <Clock className="h-4 w-4" />
    }
  ];

  // System status data
  const systemStatuses: SystemStatus[] = [
    {
      name: 'Web Server',
      status: 'operational',
      lastChecked: '2 minutes ago',
      uptime: '99.9%',
      icon: <Server className="h-4 w-4" />
    },
    {
      name: 'Database Server',
      status: 'operational',
      lastChecked: '2 minutes ago',
      uptime: '99.8%',
      icon: <Database className="h-4 w-4" />
    },
    {
      name: 'File Storage',
      status: 'operational',
      lastChecked: '2 minutes ago',
      uptime: '100%',
      icon: <HardDrive className="h-4 w-4" />
    },
    {
      name: 'Cache System',
      status: 'operational',
      lastChecked: '2 minutes ago',
      uptime: '99.9%',
      icon: <Database className="h-4 w-4" />
    },
    {
      name: 'Email System',
      status: 'operational',
      lastChecked: '2 minutes ago',
      uptime: '99.7%',
      icon: <FileText className="h-4 w-4" />
    }
  ];

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ok':
      case 'operational':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Operational</Badge>;
      case 'warning':
      case 'degraded':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Degraded</Badge>;
      case 'error':
      case 'outage':
        return <Badge variant="destructive">Outage</Badge>;
      default:
        return null;
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setIsRefreshing(true);

    // Simulate refresh
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1500);
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">System Requirements & Status</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#" className="flex items-center">
                        <Wrench className="h-4 w-4 mr-1" />
                        Tools
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>System Requirements & Status</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* System Status Overview */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>System Status Overview</CardTitle>
                    <CardDescription>
                      Check the current status of your system components
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                    className="h-8"
                  >
                    <RefreshCw className={`h-3.5 w-3.5 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
                    {isRefreshing ? 'Refreshing...' : 'Refresh'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center py-6">
                  <div className="bg-purple-50 rounded-full p-8 mb-6">
                    <div className="bg-purple-500 rounded-full p-4">
                      <Gauge className="h-8 w-8 text-white" />
                    </div>
                  </div>

                  <Alert className="mb-6 bg-green-50 border-green-200">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertTitle className="text-green-800">All Systems Operational</AlertTitle>
                    <AlertDescription className="text-green-700">
                      Your system is running smoothly with no detected issues.
                    </AlertDescription>
                  </Alert>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
                    <Card className="bg-green-50 border-green-200">
                      <CardHeader className="pb-2 pt-4">
                        <CardTitle className="text-sm font-medium text-green-800 flex items-center">
                          <Server className="h-4 w-4 mr-2" />
                          Server Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0 pb-4">
                        <p className="text-xs text-green-700">
                          All server components are running optimally.
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="bg-green-50 border-green-200">
                      <CardHeader className="pb-2 pt-4">
                        <CardTitle className="text-sm font-medium text-green-800 flex items-center">
                          <Database className="h-4 w-4 mr-2" />
                          Database Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0 pb-4">
                        <p className="text-xs text-green-700">
                          Database connections and queries are performing well.
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="bg-green-50 border-green-200">
                      <CardHeader className="pb-2 pt-4">
                        <CardTitle className="text-sm font-medium text-green-800 flex items-center">
                          <Database className="h-4 w-4 mr-2" />
                          Memory Usage
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0 pb-4">
                        <p className="text-xs text-green-700">
                          Memory usage is within optimal parameters.
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Detailed System Information */}
            <Card>
              <CardHeader>
                <CardTitle>Detailed System Information</CardTitle>
                <CardDescription>
                  View detailed information about your system requirements and status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="status" className="flex items-center gap-1">
                      <Gauge className="h-4 w-4" />
                      System Status
                    </TabsTrigger>
                    <TabsTrigger value="requirements" className="flex items-center gap-1">
                      <Settings className="h-4 w-4" />
                      System Requirements
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="status" className="pt-6">
                    <div className="rounded-md border">
                      <div className="grid grid-cols-4 p-4 bg-muted/50 text-sm font-medium">
                        <div>Component</div>
                        <div>Status</div>
                        <div>Uptime</div>
                        <div>Last Checked</div>
                      </div>
                      {systemStatuses.map((item, index) => (
                        <div key={index} className="grid grid-cols-4 p-4 border-t items-center text-sm">
                          <div className="flex items-center gap-2">
                            {item.icon}
                            {item.name}
                          </div>
                          <div>
                            {getStatusBadge(item.status)}
                          </div>
                          <div>{item.uptime}</div>
                          <div className="text-muted-foreground">{item.lastChecked}</div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="requirements" className="pt-6">
                    <div className="rounded-md border">
                      <div className="grid grid-cols-4 p-4 bg-muted/50 text-sm font-medium">
                        <div>Requirement</div>
                        <div>Required</div>
                        <div>Current</div>
                        <div>Status</div>
                      </div>
                      {systemRequirements.map((item, index) => (
                        <div key={index} className="grid grid-cols-4 p-4 border-t items-center text-sm">
                          <div className="flex items-center gap-2">
                            {item.icon}
                            {item.name}
                          </div>
                          <div>{item.required}</div>
                          <div className="font-medium">{item.current}</div>
                          <div>
                            {getStatusBadge(item.status)}
                          </div>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="border-t pt-6">
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Regular system checks are performed automatically every 30 minutes. You can also manually refresh the status using the refresh button.
                  </AlertDescription>
                </Alert>
              </CardFooter>
            </Card>

            {/* System Resources */}
            <Card>
              <CardHeader>
                <CardTitle>System Resources</CardTitle>
                <CardDescription>
                  Current resource usage of your system
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between mb-2">
                      <div className="flex items-center">
                        <Cpu className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm font-medium">CPU Usage</span>
                      </div>
                      <span className="text-sm">32%</span>
                    </div>
                    <Progress value={32} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <div className="flex items-center">
                        <Database className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm font-medium">Memory Usage</span>
                      </div>
                      <span className="text-sm">45%</span>
                    </div>
                    <Progress value={45} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <div className="flex items-center">
                        <HardDrive className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm font-medium">Disk Usage</span>
                      </div>
                      <span className="text-sm">68%</span>
                    </div>
                    <Progress value={68} className="h-2" />
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <div className="flex items-center">
                        <Database className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm font-medium">Database Connections</span>
                      </div>
                      <span className="text-sm">12/50</span>
                    </div>
                    <Progress value={24} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
