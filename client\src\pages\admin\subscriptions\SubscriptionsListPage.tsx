import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { UserPlus, ArrowUpDown, Download, Filter, Search, Calendar, CheckCircle, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Sample subscriptions data
const subscriptionsData = [
  {
    id: 1,
    userId: 'user-3376',
    username: 'sejal_2508',
    email: '<EMAIL>',
    plan: 'Premium',
    status: 'active',
    startDate: '2023-11-15',
    endDate: '2024-11-15',
    amount: 99.99,
    autoRenew: true
  },
  {
    id: 2,
    userId: 'user-3375',
    username: 'debratorof6282',
    email: '<EMAIL>',
    plan: 'Basic',
    status: 'active',
    startDate: '2023-10-22',
    endDate: '2024-10-22',
    amount: 49.99,
    autoRenew: true
  },
  {
    id: 3,
    userId: 'user-3374',
    username: 'shree',
    email: '<EMAIL>',
    plan: 'Premium',
    status: 'active',
    startDate: '2023-12-01',
    endDate: '2024-12-01',
    amount: 99.99,
    autoRenew: true
  },
  {
    id: 4,
    userId: 'user-3373',
    username: 'Heatherjane',
    email: '<EMAIL>',
    plan: 'Basic',
    status: 'expired',
    startDate: '2023-06-15',
    endDate: '2023-12-15',
    amount: 49.99,
    autoRenew: false
  },
  {
    id: 5,
    userId: 'user-3372',
    username: 'Carolynmary',
    email: '<EMAIL>',
    plan: 'Premium',
    status: 'canceled',
    startDate: '2023-08-10',
    endDate: '2024-02-10',
    amount: 99.99,
    autoRenew: false
  }
];

// Summary stats
const summaryStats = {
  totalSubscriptions: 8,
  activeSubscriptions: 6,
  expiredSubscriptions: 1,
  canceledSubscriptions: 1,
  totalRevenue: 649.93,
  subscriptionGrowth: 15
};

export default function SubscriptionsListPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortColumn, setSortColumn] = useState('startDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // Filter and sort data
  const filteredData = subscriptionsData.filter(subscription => {
    const matchesSearch = 
      subscription.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subscription.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      subscription.plan.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = 
      statusFilter === 'all' || 
      subscription.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const sortedData = [...filteredData].sort((a, b) => {
    const aValue = a[sortColumn as keyof typeof a];
    const bValue = b[sortColumn as keyof typeof b];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      if (sortDirection === 'asc') {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    } else {
      if (sortDirection === 'asc') {
        return Number(aValue) - Number(bValue);
      } else {
        return Number(bValue) - Number(aValue);
      }
    }
  });

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('desc');
    }
  };

  // Status badge renderer
  const renderStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500 hover:bg-green-600">Active</Badge>;
      case 'expired':
        return <Badge className="bg-amber-500 hover:bg-amber-600">Expired</Badge>;
      case 'canceled':
        return <Badge className="bg-red-500 hover:bg-red-600">Canceled</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <main className="flex-1 p-6 overflow-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">Total Subscriptions</h1>
              <p className="text-muted-foreground">Manage and track user subscriptions across the platform</p>
            </div>
            <Button className="flex items-center gap-2">
              <Download size={16} />
              Export Data
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Subscriptions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{summaryStats.totalSubscriptions}</div>
                <p className="text-xs text-muted-foreground mt-1">All subscription plans</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Active Subscriptions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-500">{summaryStats.activeSubscriptions}</div>
                <p className="text-xs text-muted-foreground mt-1">Currently active plans</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Revenue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">${summaryStats.totalRevenue.toFixed(2)}</div>
                <p className="text-xs text-muted-foreground mt-1">From all subscriptions</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Subscription Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-500">+{summaryStats.subscriptionGrowth}%</div>
                <p className="text-xs text-muted-foreground mt-1">Compared to last period</p>
              </CardContent>
            </Card>
          </div>

          <div className="bg-card rounded-lg shadow-sm border mb-6">
            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-4 mb-6 justify-between">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search by username, email or plan..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[180px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="expired">Expired</SelectItem>
                      <SelectItem value="canceled">Canceled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Username</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('plan')}
                      >
                        <div className="flex items-center">
                          Plan
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('status')}
                      >
                        <div className="flex items-center">
                          Status
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('startDate')}
                      >
                        <div className="flex items-center">
                          Start Date
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('endDate')}
                      >
                        <div className="flex items-center">
                          End Date
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('amount')}
                      >
                        <div className="flex items-center">
                          Amount
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('autoRenew')}
                      >
                        <div className="flex items-center">
                          Auto-Renew
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedData.map((subscription) => (
                      <TableRow key={subscription.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{subscription.username}</TableCell>
                        <TableCell>{subscription.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-semibold">
                            {subscription.plan}
                          </Badge>
                        </TableCell>
                        <TableCell>{renderStatusBadge(subscription.status)}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            {subscription.startDate}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            {subscription.endDate}
                          </div>
                        </TableCell>
                        <TableCell>${subscription.amount.toFixed(2)}</TableCell>
                        <TableCell>
                          {subscription.autoRenew ? (
                            <div className="flex items-center text-green-500">
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Yes
                            </div>
                          ) : (
                            <div className="flex items-center text-red-500">
                              <XCircle className="mr-2 h-4 w-4" />
                              No
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
