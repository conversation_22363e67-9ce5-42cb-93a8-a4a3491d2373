import mongoose, { Schema, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Log interface extending the base entity
 */
export interface ILog extends IBaseEntity {
  /**
   * Action performed
   * Examples: 'create', 'update', 'delete', 'login', 'export'
   */
  action: string;

  /**
   * Category of the action
   * Examples: 'user', 'content', 'security', 'admin', 'data'
   */
  category: string;

  /** User ID of the person who performed the action */
  userId: string;

  /** IP address from which the action was performed */
  userIp: string;

  /** User agent string from the browser/app */
  userAgent: string;

  /**
   * Type of resource affected
   * Examples: 'user', 'video', 'comment', 'setting'
   */
  resourceType: string;

  /** ID of the resource affected */
  resourceId?: string;

  /** Previous state of the resource (for auditing changes) */
  previousState?: Record<string, any>;

  /** New state of the resource (for auditing changes) */
  newState?: Record<string, any>;

  /** Status of the action (success, failure, etc.) */
  status: 'success' | 'failure' | 'warning' | 'info';

  /** Additional details about the action */
  details?: string;

  /** Severity level of the log entry */
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Log schema definition
 */
const LogSchema = new Schema<ILog>(
  {
    action: {
      type: String,
      required: true,
      index: true,
    },
    category: {
      type: String,
      required: true,
      index: true,
    },
    userId: {
      type: String,
      required: true,
      index: true,
    },
    userIp: {
      type: String,
      required: true,
    },
    userAgent: {
      type: String,
      required: true,
    },
    resourceType: {
      type: String,
      required: true,
      index: true,
    },
    resourceId: {
      type: String,
      index: true,
    },
    previousState: {
      type: Schema.Types.Mixed,
    },
    newState: {
      type: Schema.Types.Mixed,
    },
    status: {
      type: String,
      enum: ['success', 'failure', 'warning', 'info'],
      default: 'info',
      index: true,
    },
    details: {
      type: String,
    },
    severity: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical'],
      default: 'low',
      index: true,
    },
  }
);

// Merge with base schema
LogSchema.add(BaseSchema);

// Create and export the Log model
const LogModel = mongoose.model<ILog>('Log', LogSchema);
export default LogModel;
