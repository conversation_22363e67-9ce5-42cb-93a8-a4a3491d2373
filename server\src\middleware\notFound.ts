import { FastifyRequest, FastifyReply } from 'fastify';
import { createNotFoundError, ErrorCodes } from '../utils/errors';

/**
 * Not found middleware
 * Handles 404 errors for routes that don't exist
 */
export async function notFoundHandler(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  throw createNotFoundError(`Route ${request.method}:${request.url} not found`, ErrorCodes.NOT_FOUND);
}
