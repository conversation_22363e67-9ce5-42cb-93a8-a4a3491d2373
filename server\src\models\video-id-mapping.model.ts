import mongoose, { Document, Schema } from 'mongoose';

/**
 * Interface for VideoIdMapping document
 */
export interface VideoIdMapping extends Document {
  hashId: string;       // The internal hash ID (32 characters)
  engaxeId: string;     // The Engaxe ID (6-7 characters)
  videoTitle: string;   // The title of the video (for reference)
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Schema for VideoIdMapping
 */
const VideoIdMappingSchema = new Schema<VideoIdMapping>(
  {
    hashId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    engaxeId: {
      type: String,
      required: true,
      unique: true,
      index: true,
      validate: {
        validator: function(v: string) {
          return /^[a-zA-Z0-9]{6,7}$/.test(v);
        },
        message: props => `${props.value} is not a valid Engaxe ID!`
      }
    },
    videoTitle: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for faster lookups
VideoIdMappingSchema.index({ hashId: 1 });
VideoIdMappingSchema.index({ engaxeId: 1 });

// Create the model
export const VideoIdMappingModel = mongoose.model<VideoIdMapping>('VideoIdMapping', VideoIdMappingSchema);
