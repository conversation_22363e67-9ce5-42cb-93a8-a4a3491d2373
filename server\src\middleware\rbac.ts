import { FastifyRequest, FastifyReply } from 'fastify';
import permissionService from '../services/permission.service';
import roleService from '../services/role.service';
import { AuthenticatedUser } from '../types/user';

// Extend FastifyRequest to include permission/role methods
declare module 'fastify' {
  interface FastifyRequest {
    hasPermission: (permission: string) => Promise<boolean> | boolean;
    hasRole: (role: string) => Promise<boolean> | boolean;
  }
}

/**
 * Permission check middleware factory
 * Creates middleware that checks if user has the required permission
 */
export function checkPermission(requiredPermission: string) {
  return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const userId = (request.user as AuthenticatedUser).id;

      // Check if user is admin (has admin role)
      const isAdmin = await roleService.userHasRole(userId, 'admin');
      if (isAdmin) {
        // Admins have all permissions
        request.hasPermission = (): boolean => true;
        return;
      }

      // Check specific permission
      const hasPermission = await permissionService.userHasPermission(userId, requiredPermission);
      if (!hasPermission) {
        throw new Error(`You don't have permission to perform this action (${requiredPermission})`);
      }

      // Attach permission checker to request for use in controllers
      request.hasPermission = async (permission: string): Promise<boolean> => {
        return await permissionService.userHasPermission(userId, permission);
      };
    } catch (error) {
      reply.code(403).send({
        success: false,
        message: (error as Error).message || 'Permission denied',
      });
    }
  };
}

/**
 * Role check middleware factory
 * Creates middleware that checks if user has the required role
 */
export function checkRole(requiredRole: string) {
  return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    try {
      const userId = (request.user as AuthenticatedUser).id;

      // Check if user has the required role
      const hasRole = await roleService.userHasRole(userId, requiredRole);
      if (!hasRole) {
        throw new Error(`You don't have the required role to perform this action (${requiredRole})`);
      }

      // Attach role checker to request for use in controllers
      request.hasRole = async (role: string): Promise<boolean> => {
        return await roleService.userHasRole(userId, role);
      };
    } catch (error) {
      reply.code(403).send({
        success: false,
        message: (error as Error).message || 'Role requirement not met',
      });
    }
  };
}
