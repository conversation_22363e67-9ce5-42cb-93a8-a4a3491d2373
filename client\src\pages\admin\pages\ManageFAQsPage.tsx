import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Home,
  Plus,
  Edit,
  Trash2,
  HelpCircle,
  MessageSquare,
  Search,
  ChevronDown,
  ChevronUp,
  MoreHorizontal
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';

// Enhanced FAQ model
interface FAQ {
  id: number;
  question: string;
  answer: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
  status: 'published' | 'draft';
}

// Mock data for FAQs
const mockFaqs: FAQ[] = [
  {
    id: 1,
    question: 'How do I reset my password?',
    answer: 'You can reset your password by clicking on the "Forgot Password" link on the login page.',
    category: 'Account',
    createdAt: '2023-09-15T10:30:00Z',
    updatedAt: '2023-10-20T14:45:00Z',
    status: 'published'
  },
  {
    id: 2,
    question: 'How do I contact support?',
    answer: 'You can contact support by sending an <NAME_EMAIL>.',
    category: 'Support',
    createdAt: '2023-08-05T09:15:00Z',
    updatedAt: '2023-11-10T11:20:00Z',
    status: 'published'
  },
  {
    id: 3,
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards, PayPal, and bank transfers for payments.',
    category: 'Billing',
    createdAt: '2023-07-22T13:45:00Z',
    updatedAt: '2023-10-18T16:30:00Z',
    status: 'published'
  },
  {
    id: 4,
    question: 'How can I change my notification settings?',
    answer: 'You can change your notification preferences in your account settings under the "Notifications" tab.',
    category: 'Account',
    createdAt: '2023-11-01T08:20:00Z',
    updatedAt: '2023-11-05T10:15:00Z',
    status: 'draft'
  },
];

export default function ManageFAQsPage() {
  const [faqs, setFaqs] = useState<FAQ[]>(mockFaqs);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddEditDialog, setShowAddEditDialog] = useState(false);
  const [currentFaq, setCurrentFaq] = useState<FAQ | null>(null);
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Filter FAQs based on search term and category
  const filteredFaqs = faqs.filter(faq => {
    const matchesSearch =
      faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (faq.category && faq.category.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = categoryFilter === 'all' || faq.category === categoryFilter;

    return matchesSearch && matchesCategory;
  });

  // Get unique categories
  const categories = Array.from(new Set(faqs.map(faq => faq.category).filter(Boolean)));

  // Handle create/edit FAQ
  const handleSaveFaq = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const newFaq: FAQ = {
      id: currentFaq?.id || (faqs.length > 0 ? Math.max(...faqs.map(faq => faq.id)) + 1 : 1),
      question: formData.get('question') as string,
      answer: formData.get('answer') as string,
      category: formData.get('category') as string || undefined,
      status: (formData.get('status') as 'published' | 'draft') || 'draft',
      createdAt: currentFaq?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (currentFaq) {
      // Update existing FAQ
      setFaqs(faqs.map(faq => faq.id === currentFaq.id ? newFaq : faq));
    } else {
      // Add new FAQ
      setFaqs([...faqs, newFaq]);
    }

    setShowAddEditDialog(false);
    setCurrentFaq(null);
  };

  // Handle edit FAQ
  const handleEdit = (faq: FAQ) => {
    setCurrentFaq(faq);
    setShowAddEditDialog(true);
  };

  // Handle delete FAQ
  const handleDelete = (id: number) => {
    if (window.confirm('Are you sure you want to delete this FAQ?')) {
      setFaqs(faqs.filter(faq => faq.id !== id));
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Published</Badge>;
      case 'draft':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Draft</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Manage FAQs</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Content</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Pages</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Manage FAQs</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Search and Actions */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>FAQ Management</CardTitle>
                <CardDescription>
                  Create and manage frequently asked questions for your website
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex-1 relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search FAQs..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  <Button
                    onClick={() => {
                      setCurrentFaq(null);
                      setShowAddEditDialog(true);
                    }}
                    className="flex items-center gap-1"
                  >
                    <Plus className="h-4 w-4" /> Add New FAQ
                  </Button>
                </div>

                {/* Category filter pills */}
                <div className="flex flex-wrap gap-2 mb-6">
                  <Badge
                    variant={categoryFilter === 'all' ? 'default' : 'outline'}
                    className="cursor-pointer"
                    onClick={() => setCategoryFilter('all')}
                  >
                    All
                  </Badge>
                  {categories.map(category => (
                    <Badge
                      key={category}
                      variant={categoryFilter === category ? 'default' : 'outline'}
                      className="cursor-pointer"
                      onClick={() => setCategoryFilter(category || 'all')}
                    >
                      {category}
                    </Badge>
                  ))}
                </div>

                {/* FAQs Accordion */}
                <div className="border rounded-md">
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFaqs.length > 0 ? (
                      filteredFaqs.map((faq) => (
                        <AccordionItem key={faq.id} value={`faq-${faq.id}`}>
                          <AccordionTrigger className="px-4 hover:no-underline">
                            <div className="flex flex-1 items-center justify-between pr-4">
                              <div className="flex items-center gap-2">
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                                <span>{faq.question}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                {faq.category && (
                                  <Badge variant="outline" className="mr-2">
                                    {faq.category}
                                  </Badge>
                                )}
                                {getStatusBadge(faq.status)}
                              </div>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent className="px-4 pb-4">
                            <div className="space-y-4">
                              <div className="flex items-start gap-2">
                                <MessageSquare className="h-4 w-4 text-muted-foreground mt-1" />
                                <div className="flex-1">
                                  <p className="text-gray-700">{faq.answer}</p>
                                  <p className="text-xs text-muted-foreground mt-2">
                                    Last updated: {formatDate(faq.updatedAt)}
                                  </p>
                                </div>
                              </div>
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                  onClick={() => handleEdit(faq)}
                                >
                                  <Edit className="h-3.5 w-3.5 mr-1" /> Edit
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 bg-red-50 hover:bg-red-100 text-red-600 border-red-200"
                                  onClick={() => handleDelete(faq.id)}
                                >
                                  <Trash2 className="h-3.5 w-3.5 mr-1" /> Delete
                                </Button>
                              </div>
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        No FAQs found. Create your first FAQ.
                      </div>
                    )}
                  </Accordion>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <div className="text-sm text-muted-foreground">
                  Showing {filteredFaqs.length} of {faqs.length} FAQs
                </div>
                <Button variant="outline" onClick={() => window.print()}>
                  Export FAQs
                </Button>
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>

      {/* Add/Edit FAQ Dialog */}
      <Dialog open={showAddEditDialog} onOpenChange={setShowAddEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{currentFaq ? 'Edit FAQ' : 'Create New FAQ'}</DialogTitle>
            <DialogDescription>
              {currentFaq
                ? 'Update the details for this FAQ.'
                : 'Fill in the details to create a new FAQ.'}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSaveFaq}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="question" className="text-right">
                  Question
                </Label>
                <Input
                  id="question"
                  name="question"
                  placeholder="Enter the question"
                  defaultValue={currentFaq?.question || ''}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="answer" className="text-right pt-2">
                  Answer
                </Label>
                <Textarea
                  id="answer"
                  name="answer"
                  placeholder="Enter the answer"
                  defaultValue={currentFaq?.answer || ''}
                  className="col-span-3 min-h-[100px]"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="category" className="text-right">
                  Category
                </Label>
                <Input
                  id="category"
                  name="category"
                  placeholder="E.g., Account, Billing, Support"
                  defaultValue={currentFaq?.category || ''}
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="status" className="text-right">
                  Status
                </Label>
                <select
                  id="status"
                  name="status"
                  defaultValue={currentFaq?.status || 'draft'}
                  className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="published">Published</option>
                  <option value="draft">Draft</option>
                </select>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowAddEditDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {currentFaq ? 'Update FAQ' : 'Create FAQ'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
