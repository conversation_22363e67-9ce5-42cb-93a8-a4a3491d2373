import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Search, Filter, Download, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Sample verification stats
const verificationStats = {
  total: 156,
  pending: 45,
  approved: 98,
  rejected: 13,
  growth: '+12% from last month'
};

// Sample verification request data
const verificationRequests = [
  {
    id: 1,
    username: '<PERSON>',
    email: '<EMAIL>',
    documentType: 'Government ID',
    requestDate: '2023-05-15',
    status: 'Pending',
    priority: 'High'
  }
];

export default function VerificationRequestsPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const [timeRange, setTimeRange] = useState('All Time');
  const [status, setStatus] = useState('All Status');
  const [sortBy, setSortBy] = useState('Newest First');

  // Redirect non-admin users
  React.useEffect(() => {
    if (!isAdmin) navigate('/');
  }, [isAdmin, navigate]);

  if (!isAdmin) return null;

  const StatCard = ({ title, value, description, icon }: any) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />
      <div className="flex-1 flex flex-col">
        <AdminHeader />
        <div className="flex-1 bg-background p-6">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 mb-4 text-sm">
            <Link to="/admin" className="flex items-center text-muted-foreground hover:text-primary">
              <Home size={16} className="mr-1" />
              Admin Panel
            </Link>
            <ChevronRight size={14} className="text-muted-foreground" />
            <Link to="/admin/users" className="text-muted-foreground hover:text-primary">Users</Link>
            <ChevronRight size={14} className="text-muted-foreground" />
            <span className="text-foreground">Verification Requests</span>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <StatCard
              title="Total Requests"
              value={verificationStats.total}
              description={verificationStats.growth}
              icon={<div className="rounded-full p-2 bg-blue-100/50" />}
            />
            <StatCard
              title="Pending"
              value={verificationStats.pending}
              description="Awaiting review"
              icon={<div className="rounded-full p-2 bg-yellow-100/50" />}
            />
            <StatCard
              title="Approved"
              value={verificationStats.approved}
              description="Successfully verified"
              icon={<div className="rounded-full p-2 bg-green-100/50" />}
            />
            <StatCard
              title="Rejected"
              value={verificationStats.rejected}
              description="Declined requests"
              icon={<div className="rounded-full p-2 bg-red-100/50" />}
            />
          </div>

          {/* Verification Requests Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Verification Requests</CardTitle>
                <Button variant="outline" className="flex items-center gap-2">
                  <Download size={16} />
                  Export
                </Button>
              </div>
              <div className="flex items-center gap-4 mt-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-2 top-1/2 -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by name, email..."
                      className="w-full pl-8"
                    />
                  </div>
                </div>
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Time Range" />
                  </SelectTrigger>
                  <SelectContent>
                    {['All Time', 'Today', 'This Week', 'This Month', 'Custom Range'].map((option) => (
                      <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={status} onValueChange={setStatus}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    {['All Status', 'Pending', 'Approved', 'Rejected'].map((option) => (
                      <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    {['Newest First', 'Oldest First', 'Priority'].map((option) => (
                      <SelectItem key={option} value={option}>{option}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="h-12 px-4 text-left align-middle font-medium">ID</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">User</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Document Type</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Requested Date</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Status</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Priority</th>
                      <th className="h-12 px-4 text-left align-middle font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {verificationRequests.map((request) => (
                      <tr key={request.id} className="border-b">
                        <td className="p-4">{request.id}</td>
                        <td className="p-4">
                          <div>
                            <div className="font-medium">{request.username}</div>
                            <div className="text-sm text-muted-foreground">{request.email}</div>
                          </div>
                        </td>
                        <td className="p-4">{request.documentType}</td>
                        <td className="p-4">{request.requestDate}</td>
                        <td className="p-4">
                          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                            {request.status}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
                            {request.priority}
                          </Badge>
                        </td>
                        <td className="p-4">
                          <Button variant="ghost" size="sm">...</Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
