
import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useChatbot, AIAssistantProvider } from '@/context/ChatbotContext';
import { useLanguage } from '@/context/LanguageContext';
import Navbar from '@/components/layout/Navbar';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { Pencil, Settings, Languages, Key, Info, UserCheck, ChevronDown, PlusCircle, LogOut, User, Mail, Shield, FileText, Link as LinkIcon, Lock, Trash2, Image, UserPlus, UserCog, LayoutDashboard, Check } from 'lucide-react';

import { Sheet, SheetContent, Sheet<PERSON>es<PERSON>, Sheet<PERSON>eader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import UserVideos from '@/components/profile/UserVideos';
import UserChannels from '@/components/profile/UserChannels';
import SavedVideos from '@/components/profile/SavedVideos';
import Watchlist from '@/components/profile/Watchlist';
import CreatorApplicationForm from '@/components/profile/CreatorApplicationForm';
import AccountSwitcher from '@/components/profile/AccountSwitcher';
import AddAccountDialog from '@/components/profile/AddAccountDialog';
import MultiProviderSettings from '@/components/profile/MultiProviderSettings';
import TranslationSettings from '@/components/settings/TranslationSettings';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { SettingsSwitch } from '@/components/ui/settings-switch';
import { Separator } from '@/components/ui/separator';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Switch } from '@/components/ui/switch';

export default function ProfilePage() {
  const { currentUser, isAdmin } = useAuth();
  const { toast } = useToast();
  const { t } = useLanguage();
  const location = useLocation();
  const navigate = useNavigate();
  const [showAddAccountDialog, setShowAddAccountDialog] = useState(false);
  const {
    // Chatbot
    isChatbotEnabled, enableChatbot, disableChatbot,
    chatbotApiKey, setChatbotApiKey,
    chatbotSecurityKey, setChatbotSecurityKey,

    // AI Assistant
    isAIAssistantEnabled, enableAIAssistant, disableAIAssistant,
    aiAssistantProvider, setAIAssistantProvider,
    aiAssistantApiKey, setAIAssistantApiKey,
    aiAssistantEndpoint, setAIAssistantEndpoint,

    // Voice calling
    isVoiceCallingEnabled, enableVoiceCalling, disableVoiceCalling,
    voiceCallingApiKey, setVoiceCallingApiKey,
    voiceCallingSecurityKey, setVoiceCallingSecurityKey,

    // Other APIs
    isChattingEnabled, setChattingEnabled,
    chattingApiKey, setChattingApiKey,
    chattingSecurityKey, setChattingSecurityKey,

    isVideoTranslationEnabled, setVideoTranslationEnabled,
    videoTranslationApiKey, setVideoTranslationApiKey,
    videoTranslationSecurityKey, setVideoTranslationSecurityKey,

    isVoiceTranslationEnabled, setVoiceTranslationEnabled,
    voiceTranslationApiKey, setVoiceTranslationApiKey,
    voiceTranslationSecurityKey, setVoiceTranslationSecurityKey
  } = useChatbot();

  const [username, setUsername] = useState(currentUser?.username || '');
  const [email, setEmail] = useState('<EMAIL>');
  const [status, setStatus] = useState('active');
  const [accountType, setAccountType] = useState('standard');
  const [isVerified, setIsVerified] = useState(false);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [about, setAbout] = useState('');
  const [socialMediaUrl, setSocialMediaUrl] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [coverFile, setCoverFile] = useState<File | null>(null);

  const [primaryLanguage, setPrimaryLanguage] = useState('English 🇺🇸');
  const [isEditing, setIsEditing] = useState(false);
  const [isCreator, setIsCreator] = useState(false);
  const [creatorApplicationPending, setCreatorApplicationPending] = useState(false);
  const [activeSettingsTab, setActiveSettingsTab] = useState('general');
  const [activeTab, setActiveTab] = useState('videos');

  // Handle URL parameters
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab');
    if (tab === 'settings') {
      setActiveTab('settings');
      // Get the settings subtab if provided
      const settingsTab = searchParams.get('settingsTab');
      if (settingsTab && ['general', 'profile', 'api'].includes(settingsTab)) {
        setActiveSettingsTab(settingsTab);
      }
    } else if (tab && ['videos', 'channels', 'saved', 'history', 'watchlist'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [location.search]);

  // Update URL when tabs change
  useEffect(() => {
    const url = new URL(window.location.href);
    url.searchParams.set('tab', activeTab);

    if (activeTab === 'settings') {
      url.searchParams.set('settingsTab', activeSettingsTab);
    } else {
      url.searchParams.delete('settingsTab');
    }

    window.history.replaceState({}, '', url.toString());
  }, [activeTab, activeSettingsTab]);

  // Available languages
  const availableLanguages = [
    { value: 'English 🇺🇸', label: 'English 🇺🇸' },
    { value: 'Spanish 🇪🇸', label: 'Spanish 🇪🇸' },
    { value: 'French 🇫🇷', label: 'French 🇫🇷' },
    { value: 'German 🇩🇪', label: 'German 🇩🇪' },
    { value: 'Japanese 🇯🇵', label: 'Japanese 🇯🇵' },
    { value: 'Chinese 🇨🇳', label: 'Chinese 🇨🇳' },
    { value: 'Russian 🇷🇺', label: 'Russian 🇷🇺' },
    { value: 'Arabic 🇸🇦', label: 'Arabic 🇸🇦' },
    { value: 'Hindi 🇮🇳', label: 'Hindi 🇮🇳' },
    { value: 'Portuguese 🇧🇷', label: 'Portuguese 🇧🇷' }
  ];

  // Simulated user stats
  const stats = {
    videosUploaded: 12,
    subscribers: 2504,
    languages: ['English 🇺🇸', 'Spanish 🇪🇸', 'French 🇫🇷'],
    membersince: 'May 2023'
  };

  if (!currentUser) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container py-8 flex items-center justify-center">
          <Card>
            <CardHeader>
              <CardTitle>Please Sign In</CardTitle>
              <CardDescription>
                You need to be signed in to view your profile
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link to="/signin">
                <Button>Sign In</Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  const saveProfile = () => {
    // Here you would typically save to backend
    setIsEditing(false);
    // Success notification
  };

  const handleCreatorApplication = (channelUrl: string) => {
    console.log('Creator application submitted with channel URL:', channelUrl);
    // In a real app, you would send this to your backend
    // For this demo, we'll just set the application as pending
    setCreatorApplicationPending(true);

    // Simulate application approval after 5 seconds
    setTimeout(() => {
      setCreatorApplicationPending(false);
      setIsCreator(true);
      toast({
        title: "Creator Status Approved",
        description: "Congratulations! You are now a creator and can upload videos."
      });
    }, 5000);
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="container py-4 max-w-6xl mt-[60px]">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Sidebar */}
          <div className="w-full md:w-1/3">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex flex-col items-center">
                    <Avatar className="h-24 w-24">
                      <AvatarImage src={currentUser.avatar} alt={currentUser.username} />
                      <AvatarFallback>{currentUser.username[0]}</AvatarFallback>
                    </Avatar>


                  </div>
                  <Sheet>
                    <SheetTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <Settings className="h-5 w-5" />
                      </Button>
                    </SheetTrigger>
                    <SheetContent>
                      <SheetHeader>
                        <SheetTitle>Edit Profile</SheetTitle>
                        <SheetDescription>
                          Make changes to your profile here. Click save when you're done.
                        </SheetDescription>
                      </SheetHeader>
                      <div className="space-y-4 py-4">
                        <div className="flex flex-col space-y-2">
                          <label htmlFor="username">Username</label>
                          <Input
                            id="username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                          />
                        </div>
                        <div className="flex flex-col space-y-2">
                          <label htmlFor="primaryLanguage">Primary Language</label>
                          <Select value={primaryLanguage} onValueChange={setPrimaryLanguage}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select your primary language" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableLanguages.map((lang) => (
                                <SelectItem key={lang.value} value={lang.value}>{lang.label}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="flex flex-col space-y-2">
                          <label htmlFor="avatar">Profile Picture</label>
                          <Input
                            id="avatar"
                            type="file"
                            accept="image/*"
                          />
                        </div>
                        <Button onClick={saveProfile} className="w-full">
                          Save Changes
                        </Button>
                      </div>
                    </SheetContent>
                  </Sheet>
                </div>

                <CardTitle className="text-center mt-4">{currentUser.username}</CardTitle>
                <CardDescription className="text-center">
                  {t('profile.member_since')} {stats.membersince}
                </CardDescription>

                <div className="mt-4">
                  <AccountSwitcher onAddAccount={() => setShowAddAccountDialog(true)} />
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div className="bg-lingstream-card p-3 rounded-lg">
                      <div className="text-2xl font-bold">{stats.videosUploaded}</div>
                      <div className="text-xs text-lingstream-muted">Videos</div>
                    </div>
                    <div className="bg-lingstream-card p-3 rounded-lg">
                      <div className="text-2xl font-bold">{stats.subscribers}</div>
                      <div className="text-xs text-lingstream-muted">Subscribers</div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <Languages className="h-4 w-4" /> {t('profile.primary_language')}
                    </h4>
                    <div className="text-sm font-medium">{primaryLanguage}</div>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <UserCheck className="h-4 w-4" /> {t('profile.creator_status')}
                    </h4>
                    {isCreator ? (
                      <div className="flex items-center gap-2">
                        <div className="bg-green-500/20 text-green-500 text-xs px-2 py-1 rounded-full">
                          {t('profile.verified_creator')}
                        </div>
                        <Link to="/creator-studio">
                          <Button variant="link" className="h-auto p-0 text-xs">
                            Go to Creator Studio
                          </Button>
                        </Link>
                      </div>
                    ) : creatorApplicationPending ? (
                      <div className="bg-amber-500/20 text-amber-500 text-xs px-2 py-1 rounded-full">
                        Application Pending
                      </div>
                    ) : (
                      <CreatorApplicationForm onApply={handleCreatorApplication} />
                    )}
                  </div>

                  {isAdmin && (
                    <div className="border-t pt-4">
                      <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                        <LayoutDashboard className="h-4 w-4" /> Admin Access
                      </h4>
                      <div className="flex items-center gap-2">
                        <div className="bg-blue-500/20 text-blue-500 text-xs px-2 py-1 rounded-full">
                          Admin User
                        </div>
                        <Button
                          variant="link"
                          className="h-auto p-0 text-xs text-blue-500"
                          onClick={() => navigate('/admin')}
                        >
                          Go to Admin Panel
                        </Button>
                      </div>
                    </div>
                  )}

                  <div className="border-t pt-4">
                    <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                      <Languages className="h-4 w-4" /> {t('profile.also_speaks')}
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {stats.languages
                        .filter(lang => lang !== primaryLanguage)
                        .map((lang, index) => (
                          <span
                            key={index}
                            className="text-xs bg-lingstream-card px-2 py-1 rounded-full"
                          >
                            {lang}
                          </span>
                        ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main content */}
          <div className="flex-1">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full mb-6">
                <TabsTrigger value="videos" className="flex-1">{t('profile.videos')}</TabsTrigger>
                <TabsTrigger value="channels" className="flex-1">{t('profile.channels')}</TabsTrigger>
                <TabsTrigger value="saved" className="flex-1">{t('profile.saved')}</TabsTrigger>
                <TabsTrigger value="history" className="flex-1">{t('profile.history')}</TabsTrigger>
                <TabsTrigger value="watchlist" className="flex-1">{t('profile.watchlist')}</TabsTrigger>
                <TabsTrigger value="settings" className="flex-1">{t('profile.settings')}</TabsTrigger>
              </TabsList>

              <TabsContent value="videos">
                <UserVideos />
              </TabsContent>

              <TabsContent value="channels">
                <UserChannels />
              </TabsContent>

              <TabsContent value="saved">
                <SavedVideos />
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Watch History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-lingstream-muted">Your watch history will appear here.</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="watchlist">
                <Watchlist />
              </TabsContent>

              <TabsContent value="settings">
                <div className="space-y-6">
                  {/* Settings Tabs */}
                  <div className="flex border-b">
                    <button
                      className={`px-4 py-2 font-medium text-sm ${activeSettingsTab === 'general' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveSettingsTab('general')}
                    >
                      <UserCog className="h-4 w-4 inline mr-2" />
                      {t('settings.general')}
                    </button>
                    <button
                      className={`px-4 py-2 font-medium text-sm ${activeSettingsTab === 'profile' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => setActiveSettingsTab('profile')}
                    >
                      <User className="h-4 w-4 inline mr-2" />
                      {t('settings.profile')}
                    </button>
                    <button
                      className={`px-4 py-2 font-medium text-sm ${activeSettingsTab === 'api' ? 'border-b-2 border-primary text-primary' : 'text-muted-foreground'}`}
                      onClick={() => {
                        setActiveSettingsTab('api');
                      }}
                    >
                      <Key className="h-4 w-4 inline mr-2" />
                      {t('settings.api_keys')}
                    </button>
                    {isAdmin && (
                      <button
                        className={`px-4 py-2 font-medium text-sm text-muted-foreground hover:text-primary`}
                        onClick={() => navigate('/admin')}
                      >
                        <LayoutDashboard className="h-4 w-4 inline mr-2" />
                        Admin Panel
                      </button>
                    )}
                  </div>

                  {/* General Settings */}
                  {activeSettingsTab === 'general' && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <UserCog className="h-5 w-5" />
                          {t('settings.general_settings')}
                        </CardTitle>
                        <CardDescription>
                          {t('settings.manage_account')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="username">{t('settings.username')}</Label>
                            <Input
                              id="username"
                              value={username}
                              onChange={(e) => setUsername(e.target.value)}
                              placeholder="Your username"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="email">{t('settings.email')}</Label>
                            <Input
                              id="email"
                              type="email"
                              value={email}
                              onChange={(e) => setEmail(e.target.value)}
                              placeholder="Your email address"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>{t('settings.account_status')}</Label>
                            <RadioGroup value={status} onValueChange={setStatus} className="flex space-x-4">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="active" id="active" />
                                <Label htmlFor="active" className="cursor-pointer">{t('settings.active')}</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="inactive" id="inactive" />
                                <Label htmlFor="inactive" className="cursor-pointer">{t('settings.inactive')}</Label>
                              </div>
                            </RadioGroup>
                          </div>

                          <div className="space-y-2">
                            <Label>{t('settings.account_type')}</Label>
                            <div className="flex items-center space-x-2">
                              <div className="bg-lingstream-card px-3 py-1 rounded text-sm">
                                {accountType === 'standard' ? t('settings.standard_user') : t('settings.creator')}
                              </div>
                              {accountType === 'standard' && !isCreator && !creatorApplicationPending && (
                                <CreatorApplicationForm onApply={handleCreatorApplication} />
                              )}
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>{t('settings.verification_status')}</Label>
                            <div className="flex items-center space-x-2">
                              {isVerified ? (
                                <div className="bg-green-500/20 text-green-500 px-3 py-1 rounded-full text-sm flex items-center">
                                  <Shield className="h-4 w-4 mr-2" />
                                  {t('settings.verified')}
                                </div>
                              ) : (
                                <div className="flex items-center space-x-2">
                                  <div className="bg-yellow-500/20 text-yellow-500 px-3 py-1 rounded-full text-sm flex items-center">
                                    <Shield className="h-4 w-4 mr-2" />
                                    {t('settings.not_verified')}
                                  </div>
                                  <Button variant="outline" size="sm" onClick={() => setIsVerified(true)}>
                                    {t('settings.verify_now')}
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        <Button className="w-full">
                          {t('settings.save_general')}
                        </Button>
                      </CardContent>
                    </Card>
                  )}

                  {/* Profile Settings */}
                  {activeSettingsTab === 'profile' && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <User className="h-5 w-5" />
                          {t('settings.profile_settings')}
                        </CardTitle>
                        <CardDescription>
                          {t('settings.update_profile')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="space-y-4">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label htmlFor="firstName">{t('settings.first_name')}</Label>
                              <Input
                                id="firstName"
                                value={firstName}
                                onChange={(e) => setFirstName(e.target.value)}
                                placeholder="Your first name"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="lastName">{t('settings.last_name')}</Label>
                              <Input
                                id="lastName"
                                value={lastName}
                                onChange={(e) => setLastName(e.target.value)}
                                placeholder="Your last name"
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="about">{t('settings.about')}</Label>
                            <Textarea
                              id="about"
                              value={about}
                              onChange={(e) => setAbout(e.target.value)}
                              placeholder="Tell us about yourself"
                              rows={4}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="socialMedia">{t('settings.social_media')}</Label>
                            <div className="flex">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-700 bg-gray-800 text-gray-400">
                                <LinkIcon className="h-4 w-4" />
                              </span>
                              <Input
                                id="socialMedia"
                                value={socialMediaUrl}
                                onChange={(e) => setSocialMediaUrl(e.target.value)}
                                placeholder="https://twitter.com/yourusername"
                                className="rounded-l-none"
                              />
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-2">
                            <Label>{t('settings.change_password')}</Label>
                            <div className="space-y-4">
                              <Input
                                type="password"
                                value={currentPassword}
                                onChange={(e) => setCurrentPassword(e.target.value)}
                                placeholder={t('settings.current_password')}
                              />
                              <Input
                                type="password"
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                placeholder={t('settings.new_password')}
                              />
                              <Input
                                type="password"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                placeholder={t('settings.confirm_password')}
                              />
                            </div>
                          </div>

                          <Separator />

                          <div className="space-y-2">
                            <Label>{t('settings.profile_picture')}</Label>
                            <div className="flex items-center gap-4">
                              <Avatar className="h-16 w-16">
                                <AvatarImage src={currentUser.avatar} alt={currentUser.username} />
                                <AvatarFallback>{currentUser.username[0]}</AvatarFallback>
                              </Avatar>
                              <Input
                                type="file"
                                accept="image/*"
                                onChange={(e) => setAvatarFile(e.target.files?.[0] || null)}
                              />
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>{t('settings.cover_image')}</Label>
                            <div className="aspect-[3/1] bg-lingstream-card rounded-md overflow-hidden relative">
                              {/* Cover image would be displayed here */}
                              <div className="absolute inset-0 flex items-center justify-center">
                                <Image className="h-8 w-8 text-lingstream-muted" />
                              </div>
                            </div>
                            <Input
                              type="file"
                              accept="image/*"
                              onChange={(e) => setCoverFile(e.target.files?.[0] || null)}
                              className="mt-2"
                            />
                          </div>

                          <Separator />

                          <div className="space-y-2">
                            <Label className="text-red-500">{t('settings.danger_zone')}</Label>
                            <Button variant="destructive" className="w-full">
                              <Trash2 className="h-4 w-4 mr-2" />
                              {t('settings.delete_account')}
                            </Button>
                          </div>
                        </div>

                        <Button className="w-full">
                          {t('settings.save_profile')}
                        </Button>
                      </CardContent>
                    </Card>
                  )}

                  {/* API Keys Settings */}
                  {activeSettingsTab === 'api' && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Key className="h-5 w-5" />
                          {t('settings.api_keys')}
                        </CardTitle>
                        <CardDescription>
                          {t('settings.configure_keys')}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                    <form className="space-y-6">
                      {/* Translation Settings */}
                      <div className="border p-4 rounded-md">
                        <h3 className="text-lg font-medium mb-4">Bhashini Translation API</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Configure Bhashini API for Indian language translation. This API is provided by the Government of India.
                        </p>

                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="bhashini-enabled" className="font-medium">Enable Bhashini Translation</Label>
                            <Switch
                              id="bhashini-enabled"
                              checked={true}
                              onCheckedChange={(checked) => {
                                // Update translation context
                                const translationContext = (window as any).__TRANSLATION_CONTEXT__;
                                if (translationContext) {
                                  if (checked) {
                                    translationContext.enableTranslation();
                                    translationContext.setTranslationProvider('bhashini');
                                    translationContext.updateTranslationProvider('bhashini', {
                                      enabled: true,
                                      apiKey: 'cee60134c6bb4d179efd3fda48ff32fe',
                                      userId: 'cee60134c6bb4d179efd3fda48ff32fe',
                                      ulcaApiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318'
                                    });
                                  } else {
                                    translationContext.updateTranslationProvider('bhashini', {
                                      enabled: false
                                    });
                                  }
                                }
                              }}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="bhashini-user-id" className="font-medium">User ID</Label>
                            <Input
                              id="bhashini-user-id"
                              type="text"
                              value="cee60134c6bb4d179efd3fda48ff32fe"
                              readOnly
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="bhashini-ulca-key" className="font-medium">ULCA API Key</Label>
                            <Input
                              id="bhashini-ulca-key"
                              type="password"
                              value="13a647c84b-2747-4f0c-afcd-2ac8235f5318"
                              readOnly
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="bhashini-endpoint" className="font-medium">API Endpoint</Label>
                            <Input
                              id="bhashini-endpoint"
                              type="text"
                              value="https://bhashini.gov.in/api/v1/inference/translation"
                              readOnly
                            />
                          </div>

                          <Alert className="bg-green-50 border-green-200">
                            <Check className="h-4 w-4 text-green-500" />
                            <AlertDescription className="text-green-800">
                              Bhashini API is configured and ready to use. You can now translate messages in Indian languages.
                            </AlertDescription>
                          </Alert>

                          <Button
                            type="button"
                            className="w-full"
                            onClick={() => {
                              // Test the Bhashini API
                              const translationContext = (window as any).__TRANSLATION_CONTEXT__;
                              if (translationContext) {
                                translationContext.translateText("Hello, how are you?", "hi", "en")
                                  .then((result: string) => {
                                    toast({
                                      title: "Translation Test",
                                      description: `Translation result: ${result}`,
                                    });
                                  })
                                  .catch((error: any) => {
                                    toast({
                                      title: "Translation Test Failed",
                                      description: `Error: ${error.message}`,
                                      variant: "destructive",
                                    });
                                  });
                              }
                            }}
                          >
                            Test Bhashini API
                          </Button>
                        </div>
                      </div>

                      {/* Multi-Provider AI Assistant Configuration */}
                      <MultiProviderSettings />

                      {/* Chatbot API Key Section */}
                      <div className="space-y-4 border p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <h3 className="text-md font-medium">{t('settings.chatbot')}</h3>
                          <div className="flex items-center gap-2">
                            <SettingsSwitch
                              id="chatbotToggle"
                              checked={isChatbotEnabled}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  enableChatbot();
                                } else {
                                  disableChatbot();
                                }
                              }}
                            />
                          </div>
                        </div>

                        {isChatbotEnabled && (
                          <>
                            <div className="space-y-2">
                              <Label htmlFor="chatbotApiKey">{t('settings.api_key')}</Label>
                              <Input
                                id="chatbotApiKey"
                                type="password"
                                placeholder="Enter your Chatbot API key"
                                value={chatbotApiKey}
                                onChange={(e) => setChatbotApiKey(e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="chatbotSecurityKey">{t('settings.security_key')}</Label>
                              <Input
                                id="chatbotSecurityKey"
                                type="password"
                                placeholder="Enter your Chatbot Security Key"
                                value={chatbotSecurityKey}
                                onChange={(e) => setChatbotSecurityKey(e.target.value)}
                              />
                            </div>
                          </>
                        )}
                      </div>

                      {/* Voice Calling API Key Section */}
                      <div className="space-y-4 border p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <h3 className="text-md font-medium">{t('settings.chatbot_voice')}</h3>
                          <div className="flex items-center gap-2">
                            <SettingsSwitch
                              id="voiceCallingToggle"
                              checked={isVoiceCallingEnabled}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  enableVoiceCalling();
                                } else {
                                  disableVoiceCalling();
                                }
                              }}
                            />
                          </div>
                        </div>

                        {isVoiceCallingEnabled && (
                          <>
                            <div className="space-y-2">
                              <Label htmlFor="voiceCallingApiKey">{t('settings.api_key')}</Label>
                              <Input
                                id="voiceCallingApiKey"
                                type="password"
                                placeholder="Enter your Voice Calling API key"
                                value={voiceCallingApiKey}
                                onChange={(e) => setVoiceCallingApiKey(e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="voiceCallingSecurityKey">{t('settings.security_key')}</Label>
                              <Input
                                id="voiceCallingSecurityKey"
                                type="password"
                                placeholder="Enter your Voice Calling Security Key"
                                value={voiceCallingSecurityKey}
                                onChange={(e) => setVoiceCallingSecurityKey(e.target.value)}
                              />
                            </div>
                          </>
                        )}
                      </div>

                      {/* Chatting API Key Section */}
                      <div className="space-y-4 border p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <h3 className="text-md font-medium">{t('settings.chatting')}</h3>
                          <div className="flex items-center gap-2">
                            <SettingsSwitch
                              id="chattingToggle"
                              checked={isChattingEnabled}
                              onCheckedChange={(checked) => {
                                setChattingEnabled(checked);
                                localStorage.setItem('chattingEnabled', checked.toString());
                              }}
                            />
                          </div>
                        </div>

                        {isChattingEnabled && (
                          <>
                            <div className="space-y-2">
                              <Label htmlFor="chattingApiKey">API Key</Label>
                              <Input
                                id="chattingApiKey"
                                type="password"
                                placeholder="Enter your Chatting API key"
                                value={chattingApiKey}
                                onChange={(e) => setChattingApiKey(e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="chattingSecurityKey">Security Key</Label>
                              <Input
                                id="chattingSecurityKey"
                                type="password"
                                placeholder="Enter your Chatting Security Key"
                                value={chattingSecurityKey}
                                onChange={(e) => setChattingSecurityKey(e.target.value)}
                              />
                            </div>
                          </>
                        )}
                      </div>

                      {/* Video Translation API Key Section */}
                      <div className="space-y-4 border p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <h3 className="text-md font-medium">{t('settings.video_translation')}</h3>
                          <div className="flex items-center gap-2">
                            <SettingsSwitch
                              id="videoTranslationToggle"
                              checked={isVideoTranslationEnabled}
                              onCheckedChange={(checked) => {
                                setVideoTranslationEnabled(checked);
                                localStorage.setItem('videoTranslationEnabled', checked.toString());
                              }}
                            />
                          </div>
                        </div>

                        {isVideoTranslationEnabled && (
                          <>
                            <div className="space-y-2">
                              <Label htmlFor="videoTranslationApiKey">{t('settings.api_key')}</Label>
                              <Input
                                id="videoTranslationApiKey"
                                type="password"
                                placeholder="Enter your Video Translation API key"
                                value={videoTranslationApiKey}
                                onChange={(e) => setVideoTranslationApiKey(e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="videoTranslationSecurityKey">{t('settings.security_key')}</Label>
                              <Input
                                id="videoTranslationSecurityKey"
                                type="password"
                                placeholder="Enter your Video Translation Security Key"
                                value={videoTranslationSecurityKey}
                                onChange={(e) => setVideoTranslationSecurityKey(e.target.value)}
                              />
                            </div>
                          </>
                        )}
                      </div>

                      {/* Voice Translation API Key Section */}
                      <div className="space-y-4 border p-4 rounded-md">
                        <div className="flex items-center justify-between">
                          <h3 className="text-md font-medium">{t('settings.voice_translation')}</h3>
                          <div className="flex items-center gap-2">
                            <SettingsSwitch
                              id="voiceTranslationToggle"
                              checked={isVoiceTranslationEnabled}
                              onCheckedChange={(checked) => {
                                setVoiceTranslationEnabled(checked);
                                localStorage.setItem('voiceTranslationEnabled', checked.toString());
                              }}
                            />
                          </div>
                        </div>

                        {isVoiceTranslationEnabled && (
                          <>
                            <div className="space-y-2">
                              <Label htmlFor="voiceTranslationApiKey">{t('settings.api_key')}</Label>
                              <Input
                                id="voiceTranslationApiKey"
                                type="password"
                                placeholder="Enter your Voice Translation API key"
                                value={voiceTranslationApiKey}
                                onChange={(e) => setVoiceTranslationApiKey(e.target.value)}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="voiceTranslationSecurityKey">{t('settings.security_key')}</Label>
                              <Input
                                id="voiceTranslationSecurityKey"
                                type="password"
                                placeholder="Enter your Voice Translation Security Key"
                                value={voiceTranslationSecurityKey}
                                onChange={(e) => setVoiceTranslationSecurityKey(e.target.value)}
                              />
                            </div>
                          </>
                        )}
                      </div>

                      <Button type="button" className="w-full" onClick={() => {
                        // Save chatbot API keys
                        if (isChatbotEnabled && chatbotApiKey) {
                          localStorage.setItem('chatbotApiKey', chatbotApiKey);
                          localStorage.setItem('chatbotSecurityKey', chatbotSecurityKey);
                        } else if (!isChatbotEnabled) {
                          localStorage.removeItem('chatbotApiKey');
                          localStorage.removeItem('chatbotSecurityKey');
                        }

                        // Save voice calling API keys
                        if (isVoiceCallingEnabled && voiceCallingApiKey) {
                          localStorage.setItem('voiceCallingApiKey', voiceCallingApiKey);
                          localStorage.setItem('voiceCallingSecurityKey', voiceCallingSecurityKey);
                        } else if (!isVoiceCallingEnabled) {
                          localStorage.removeItem('voiceCallingApiKey');
                          localStorage.removeItem('voiceCallingSecurityKey');
                        }

                        // Save chatting API keys
                        if (isChattingEnabled && chattingApiKey) {
                          localStorage.setItem('chattingApiKey', chattingApiKey);
                          localStorage.setItem('chattingSecurityKey', chattingSecurityKey);
                        } else if (!isChattingEnabled) {
                          localStorage.removeItem('chattingApiKey');
                          localStorage.removeItem('chattingSecurityKey');
                        }

                        // Save video translation API keys
                        if (isVideoTranslationEnabled && videoTranslationApiKey) {
                          localStorage.setItem('videoTranslationApiKey', videoTranslationApiKey);
                          localStorage.setItem('videoTranslationSecurityKey', videoTranslationSecurityKey);
                        } else if (!isVideoTranslationEnabled) {
                          localStorage.removeItem('videoTranslationApiKey');
                          localStorage.removeItem('videoTranslationSecurityKey');
                        }

                        // Save voice translation API keys
                        if (isVoiceTranslationEnabled && voiceTranslationApiKey) {
                          localStorage.setItem('voiceTranslationApiKey', voiceTranslationApiKey);
                          localStorage.setItem('voiceTranslationSecurityKey', voiceTranslationSecurityKey);
                        } else if (!isVoiceTranslationEnabled) {
                          localStorage.removeItem('voiceTranslationApiKey');
                          localStorage.removeItem('voiceTranslationSecurityKey');
                        }

                        // AI Assistant settings are now saved automatically by the MultiProviderSettings component

                        toast({
                          title: "Settings Saved",
                          description: "Your API keys and settings have been saved successfully.",
                        });
                      }}>
                        {t('settings.save')}
                      </Button>
                    </form>
                  </CardContent>
                </Card>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>

      {/* Add Account Dialog */}
      <AddAccountDialog
        open={showAddAccountDialog}
        onOpenChange={setShowAddAccountDialog}
      />
    </div>
  );
}
