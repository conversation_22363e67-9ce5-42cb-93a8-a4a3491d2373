import axios from 'axios';
import {
  BhashiniConfig,
  toBhashiniLanguageCode,
  executeModelComputation,
  SUPPORTED_LANGUAGES
} from './bhashiniApiService';
import { translateText as directTranslateText } from './bhashiniDirectApi';

/**
 * Bhashini Translation Service
 *
 * This service provides integration with the Bhashini API for translation
 * Bhashini is an AI-based translation service developed by the Government of India
 * that specializes in Indian languages.
 *
 * Documentation: https://bhashini.gov.in/ulca/apis
 */

// Interface for translation request
interface TranslationRequest {
  text: string;
  sourceLanguage: string;
  targetLanguage: string;
}

// Interface for translation response
interface TranslationResponse {
  translatedText: string;
  sourceLanguage: string;
  targetLanguage: string;
}

/**
 * Translate text using Bhashini API
 * @param text Text to translate
 * @param sourceLanguage Source language code (ISO)
 * @param targetLanguage Target language code (ISO)
 * @param config Bhashini API configuration
 * @returns Translated text
 */
export async function translateText(
  text: string,
  sourceLanguage: string,
  targetLanguage: string,
  config: BhashiniConfig
): Promise<string> {
  try {
    // If source and target languages are the same, return the original text
    if (sourceLanguage === targetLanguage) {
      return text;
    }

    // Check if the language is supported by Bhashini
    const isSourceSupported = Object.keys(SUPPORTED_LANGUAGES).includes(sourceLanguage);
    const isTargetSupported = Object.keys(SUPPORTED_LANGUAGES).includes(targetLanguage);

    // For Indian languages, use the direct Bhashini API implementation (Python-style)
    if (isSourceSupported && isTargetSupported) {
      console.log(`Using direct Bhashini API (Python-style) for translation from ${sourceLanguage} to ${targetLanguage}`);

      try {
        // Try the direct API first with the Python-style implementation
        return await directTranslateText(
          text,
          sourceLanguage,
          targetLanguage,
          {
            userId: config.userId,
            apiKey: config.ulcaApiKey || config.apiKey,
            authorization: "W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun" // Using the authorization from Python code
          }
        );
      } catch (directApiError) {
        console.error('Direct Bhashini API failed, falling back to standard implementation:', directApiError);
        // Continue to standard implementation as fallback
      }
    }

    // Convert ISO language codes to Bhashini language codes
    const bhashiniSourceLang = toBhashiniLanguageCode(sourceLanguage);
    const bhashiniTargetLang = toBhashiniLanguageCode(targetLanguage);

    // Use the executeModelComputation function from bhashiniApiService
    const response = await executeModelComputation(
      'ai4bharat/indictrans-v2-all-gpu',
      'translation',
      [{ source: text }],
      {
        language: {
          sourceLanguage: bhashiniSourceLang,
          targetLanguage: bhashiniTargetLang,
        },
      },
      config
    );

    // Extract and return the translated text
    if (response &&
        response.output &&
        response.output.length > 0 &&
        response.output[0].target) {
      return response.output[0].target;
    }

    throw new Error('Invalid response from Bhashini API');
  } catch (error) {
    console.error('Error translating text with Bhashini API:', error);

    // Add more detailed error logging
    if (axios.isAxiosError(error)) {
      console.error('API Error details:', error.response?.data);
      console.error('API Error status:', error.response?.status);
      console.error('API Error headers:', error.response?.headers);

      // Return more specific error message
      return `[Translation Error: ${error.response?.status} - ${error.response?.data?.message || error.message}] ${text}`;
    }

    // Return original text with error indicator if translation fails
    return `[Translation Error: ${error instanceof Error ? error.message : 'Unknown error'}] ${text}`;
  }
}

/**
 * Detect language of text using Bhashini API
 * @param text Text to detect language for
 * @param config Bhashini API configuration
 * @returns Detected language code
 */
export async function detectLanguage(
  text: string,
  config: BhashiniConfig
): Promise<string> {
  try {
    // Use the executeModelComputation function from bhashiniApiService
    const response = await executeModelComputation(
      'ai4bharat/indiclid',
      'language-detection',
      [{ source: text }],
      {},
      config
    );

    // Extract and return the detected language
    if (response &&
        response.output &&
        response.output.length > 0 &&
        response.output[0].langPrediction) {
      // Return the language with highest confidence
      const predictions = response.output[0].langPrediction;
      const sortedPredictions = Object.entries(predictions)
        .sort(([, a]: any, [, b]: any) => b - a);

      if (sortedPredictions.length > 0) {
        return sortedPredictions[0][0];
      }
    }

    throw new Error('Invalid response from Bhashini API');
  } catch (error) {
    console.error('Error detecting language with Bhashini API:', error);
    return 'en'; // Default to English on error
  }
}

/**
 * Translates a chat message using the Bhashini API
 *
 * @param message The message to translate
 * @param sourceLanguage The source language code
 * @param targetLanguage The target language code
 * @param config The Bhashini API configuration
 * @returns The translated message
 */
export async function translateChatMessage(
  message: { id: string; text: string; sender: string; timestamp: Date },
  sourceLanguage: string,
  targetLanguage: string,
  config: BhashiniConfig
): Promise<{ id: string; text: string; translatedText: string; sender: string; timestamp: Date }> {
  try {
    const translatedText = await translateText(message.text, sourceLanguage, targetLanguage, config);

    return {
      ...message,
      translatedText: translatedText.startsWith('[Translation Error]') ? message.text : translatedText
    };
  } catch (error) {
    console.error('Error translating chat message:', error);
    return {
      ...message,
      translatedText: message.text
    };
  }
}

export default {
  translateText,
  detectLanguage,
  toBhashiniLanguageCode,
  translateChatMessage,
  SUPPORTED_LANGUAGES,
};
