import { FastifyInstance } from 'fastify';
import { SimpleFixController } from '../controllers/simple-fix.controller';
import { authenticate } from '../middleware/auth';

export default async function (fastify: FastifyInstance) {
  const simpleFixController = new SimpleFixController();

  // Simple fix for all videos (admin only)
  fastify.post(
    '/languages',
    {
      preHandler: authenticate, // Add authentication to ensure only admins can access this endpoint
    },
    (request, reply) => simpleFixController.simpleFixLanguages(request as any, reply)
  );
}
