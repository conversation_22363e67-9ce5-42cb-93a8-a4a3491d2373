import { FastifySchema } from 'fastify';
import { Type } from '@sinclair/typebox';

/**
 * Create permission schema
 */
export const createPermissionSchema: FastifySchema = {
  body: Type.Object({
    name: Type.String(),
    description: Type.String(),
    code: Type.String({ pattern: '^[a-z0-9_:.-]+$' }),
    category: Type.String(),
    isActive: Type.Optional(Type.Boolean()),
    resourceType: Type.String(),
    action: Type.String(),
    scope: Type.Enum({ global: 'global', own: 'own', group: 'group' }),
    conditions: Type.Optional(Type.String()),
    dependencies: Type.Optional(Type.Array(Type.String())),
    conflicts: Type.Optional(Type.Array(Type.String())),
  }),
  response: {
    201: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      permission: Type.Object({
        id: Type.String(),
        name: Type.String(),
        description: Type.String(),
        code: Type.String(),
        category: Type.String(),
        isActive: Type.Boolean(),
        resourceType: Type.String(),
        action: Type.String(),
        scope: Type.String(),
        createdAt: Type.String({ format: 'date-time' }),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      error: Type.Optional(Type.String()),
    }),
  },
};

/**
 * Get all permissions schema
 */
export const getAllPermissionsSchema: FastifySchema = {
  querystring: Type.Object({
    page: Type.Optional(Type.Number({ minimum: 1, default: 1 })),
    limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100, default: 10 })),
    search: Type.Optional(Type.String()),
    category: Type.Optional(Type.String()),
    resourceType: Type.Optional(Type.String()),
    action: Type.Optional(Type.String()),
    scope: Type.Optional(Type.String()),
    isActive: Type.Optional(Type.Boolean()),
    sortBy: Type.Optional(Type.String()),
    sortOrder: Type.Optional(Type.Enum({ asc: 'asc', desc: 'desc' })),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      permissions: Type.Array(
        Type.Object({
          id: Type.String(),
          name: Type.String(),
          description: Type.String(),
          code: Type.String(),
          category: Type.String(),
          isActive: Type.Boolean(),
          resourceType: Type.String(),
          action: Type.String(),
          scope: Type.String(),
          createdAt: Type.String({ format: 'date-time' }),
        })
      ),
      pagination: Type.Object({
        total: Type.Number(),
        page: Type.Number(),
        limit: Type.Number(),
        pages: Type.Number(),
      }),
    }),
  },
};

/**
 * Get permission by ID schema
 */
export const getPermissionByIdSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      permission: Type.Object({
        id: Type.String(),
        name: Type.String(),
        description: Type.String(),
        code: Type.String(),
        category: Type.String(),
        isActive: Type.Boolean(),
        resourceType: Type.String(),
        action: Type.String(),
        scope: Type.String(),
        conditions: Type.Optional(Type.String()),
        dependencies: Type.Optional(Type.Array(Type.String())),
        conflicts: Type.Optional(Type.Array(Type.String())),
        createdAt: Type.String({ format: 'date-time' }),
        updatedAt: Type.String({ format: 'date-time' }),
      }),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Update permission schema
 */
export const updatePermissionSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  body: Type.Object({
    name: Type.Optional(Type.String()),
    description: Type.Optional(Type.String()),
    category: Type.Optional(Type.String()),
    isActive: Type.Optional(Type.Boolean()),
    scope: Type.Optional(Type.Enum({ global: 'global', own: 'own', group: 'group' })),
    conditions: Type.Optional(Type.String()),
    dependencies: Type.Optional(Type.Array(Type.String())),
    conflicts: Type.Optional(Type.Array(Type.String())),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      permission: Type.Object({
        id: Type.String(),
        name: Type.String(),
        description: Type.String(),
        code: Type.String(),
        category: Type.String(),
        isActive: Type.Boolean(),
        resourceType: Type.String(),
        action: Type.String(),
        scope: Type.String(),
        updatedAt: Type.String({ format: 'date-time' }),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Delete permission schema
 */
export const deletePermissionSchema: FastifySchema = {
  params: Type.Object({
    id: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Assign direct permissions to user schema
 */
export const assignPermissionsToUserSchema: FastifySchema = {
  params: Type.Object({
    userId: Type.String(),
  }),
  body: Type.Object({
    permissionIds: Type.Array(Type.String()),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
      user: Type.Object({
        id: Type.String(),
        username: Type.String(),
        permissions: Type.Array(Type.String()),
      }),
    }),
    400: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
    404: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};
