import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Search, Link } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Language } from '@/types';

interface BasicInfoStepProps {
  engaxeUrl: string;
  setEngaxeUrl: (value: string) => void;
  title: string;
  setTitle: (value: string) => void;
  description: string;
  setDescription: (value: string) => void;
  thumbnail: string | null;
  setThumbnail: (value: string | null) => void;
  setThumbnailFile: (value: File | null) => void;
  category: string;
  setCategory: (value: string) => void;
  categories: string[];
  defaultLanguage: string;
  setDefaultLanguage: (value: string) => void;
  languages: Language[];
  videoPreviewUrl: string;
  handleFetchVideo: () => void;
  isFetching: boolean;
}

export default function BasicInfoStep({
  engaxeUrl,
  setEngaxeUrl,
  title,
  setTitle,
  description,
  setDescription,
  thumbnail,
  setThumbnail,
  setThumbnailFile,
  category,
  setCategory,
  categories,
  defaultLanguage,
  setDefaultLanguage,
  languages,
  videoPreviewUrl,
  handleFetchVideo,
  isFetching
}: BasicInfoStepProps) {
  // State to track if URL has changed since last fetch
  const [urlChanged, setUrlChanged] = useState(false);

  // Handle URL change
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUrl = e.target.value;
    setEngaxeUrl(newUrl);
    setUrlChanged(true);
  };

  // Auto-fetch metadata when URL is pasted
  const handleUrlPaste = async (e: React.ClipboardEvent<HTMLInputElement>) => {
    const pastedUrl = e.clipboardData.getData('text');
    if (pastedUrl && pastedUrl.includes('engaxe.com')) {
      // Small delay to ensure the input value is updated
      setTimeout(() => {
        handleFetchVideo();
      }, 100);
    }
  };
  // Function to handle thumbnail upload
  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      alert("Please upload an image file");
      return;
    }

    // Set the thumbnail file
    setThumbnailFile(file);

    // Create a URL for the thumbnail preview
    const objectUrl = URL.createObjectURL(file);
    setThumbnail(objectUrl);
  };

  return (
    <div className="space-y-6">
      {/* Engaxe URL for default language */}
      <div className="space-y-2">
        <label htmlFor="engaxeUrl" className="text-sm font-medium">
          Engaxe URL <span className="text-xs text-lingstream-muted">(Default Language: {languages.find(l => l.code === defaultLanguage)?.name || 'English'})</span>
        </label>
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Input
              id="engaxeUrl"
              value={engaxeUrl}
              onChange={handleUrlChange}
              onPaste={handleUrlPaste}
              placeholder="https://engaxe.com/videos/VIDEOID"
              className="pr-10"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
              <Link className="h-4 w-4" />
            </div>
          </div>

          <Button
            type="button"
            onClick={handleFetchVideo}
            disabled={!engaxeUrl || isFetching}
            className="whitespace-nowrap"
          >
            {isFetching ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Fetching...
              </>
            ) : (
              <>
                <Search className="h-4 w-4 mr-2" />
                Fetch
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Video Preview (if available) */}
      {videoPreviewUrl && (
        <div className="space-y-2">
          <label className="text-sm font-medium">Video Preview</label>
          <div className="flex rounded-md overflow-hidden bg-card">
            {/* Left side - Video thumbnail */}
            <div className="w-2/5 relative">
              {thumbnail ? (
                <div className="relative aspect-video w-full group cursor-pointer">
                  <img
                    src={thumbnail}
                    alt="Video thumbnail"
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // If image fails to load, replace with a placeholder
                      const target = e.target as HTMLImageElement;
                      target.onerror = null; // Prevent infinite loop
                      target.src = `https://placehold.co/480x360/333333/FFFFFF?text=Video+Preview`;
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="w-12 h-12 rounded-full bg-white bg-opacity-80 flex items-center justify-center">
                      <div className="w-0 h-0 border-t-6 border-t-transparent border-l-12 border-l-lingstream-accent border-b-6 border-b-transparent ml-1"></div>
                    </div>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-1 py-0.5 rounded">
                    5:30
                  </div>
                </div>
              ) : (
                <div className="aspect-video w-full">
                  <iframe
                    src={videoPreviewUrl}
                    className="h-full w-full"
                    allowFullScreen
                    title="Video preview"
                  ></iframe>
                </div>
              )}
            </div>

            {/* Right side - Video information */}
            <div className="w-3/5 p-4 flex flex-col justify-between">
              <div>
                <h3 className="font-medium text-lg line-clamp-2">{title || 'Mission: Impossible - Dead Reckoning | The Biggest Stunt...'}</h3>
                <div className="flex items-center mt-2">
                  <span className="text-sm">Paramount Pictures</span>
                  <span className="ml-1 text-xs bg-gray-200 dark:bg-gray-700 rounded-full px-1">✓</span>
                </div>
                <div className="flex items-center mt-2">
                  <span className="text-sm">20M views</span>
                </div>
              </div>
              <div className="text-sm">
                <span className="text-muted-foreground">Category:</span> <span className="font-medium">{category || 'Entertainment'}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Title */}
      <div className="space-y-2">
        <label htmlFor="title" className="text-sm font-medium">Video Title</label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter video title"
        />
      </div>

      {/* Description */}
      <div className="space-y-2">
        <label htmlFor="description" className="text-sm font-medium">Description</label>
        <Textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Enter video description"
          rows={4}
        />
      </div>

      {/* Thumbnail */}
      <div className="space-y-2">
        <label htmlFor="thumbnail" className="text-sm font-medium">Thumbnail</label>
        <div className="flex flex-col gap-2">
          {thumbnail && (
            <div className="relative aspect-video w-full overflow-hidden rounded-md border border-border">
              <img
                src={thumbnail}
                alt="Video thumbnail"
                className="h-full w-full object-cover"
                onError={(e) => {
                  // If image fails to load, replace with a placeholder
                  const target = e.target as HTMLImageElement;
                  target.onerror = null; // Prevent infinite loop
                  target.src = `https://placehold.co/480x360/333333/FFFFFF?text=Video+Thumbnail`;
                }}
              />
            </div>
          )}
          <Input
            id="thumbnail"
            type="file"
            accept="image/*"
            onChange={handleThumbnailUpload}
            className="cursor-pointer"
          />
          <p className="text-xs text-lingstream-muted">
            Upload a thumbnail for your video
          </p>
        </div>
      </div>

      {/* Category */}
      <div className="space-y-2">
        <label htmlFor="category" className="text-sm font-medium">Category</label>
        <Select value={category} onValueChange={setCategory}>
          <SelectTrigger id="category">
            <SelectValue placeholder="Select a category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((cat) => (
              <SelectItem key={cat} value={cat}>
                {cat}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Default Language */}
      <div className="space-y-2">
        <label htmlFor="defaultLanguage" className="text-sm font-medium">Default Language</label>
        <Select value={defaultLanguage} onValueChange={setDefaultLanguage}>
          <SelectTrigger id="defaultLanguage">
            <SelectValue placeholder="Select a language" />
          </SelectTrigger>
          <SelectContent>
            {languages.map((lang) => (
              <SelectItem key={lang.code} value={lang.code}>
                <div className="flex items-center">
                  <span className="mr-2">{lang.flag}</span>
                  {lang.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
