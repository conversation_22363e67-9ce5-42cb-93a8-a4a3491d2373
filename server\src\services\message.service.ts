import { MessageModel, IMessage, ConversationModel, UserModel } from '../models';
import { conversationService } from './conversation.service';
import { createNotFoundError, createBadRequestError } from '../utils/errors/AppError';
import { ErrorCodes } from '../utils/errors/errorCodes';

/**
 * Service for managing messages
 */
class MessageService {
  /**
   * Get messages for a conversation
   */
  async getMessagesByConversation(
    conversationId: string,
    userId: string,
    options: {
      page?: number;
      limit?: number;
      sort?: string;
      before?: Date;
      after?: Date;
    } = {}
  ) {
    // Verify user has access to conversation
    const conversation = await ConversationModel.findOne({
      id: conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    const {
      page = 1,
      limit = 20,
      sort = 'createdAt',
      before,
      after,
    } = options;

    // Build query
    const query: any = {
      conversationId,
      deletedAt: null,
    };

    // Add date filters if specified
    if (before) {
      query.createdAt = { ...query.createdAt, $lt: before };
    }

    if (after) {
      query.createdAt = { ...query.createdAt, $gt: after };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get total count
    const total = await MessageModel.countDocuments(query);

    // Get messages
    const messages = await MessageModel.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get sender details for each message
    const messagesWithSenders = await Promise.all(
      messages.map(async (message) => {
        const sender = await UserModel.findOne(
          { id: message.senderId, deletedAt: null },
          'id username displayName avatar'
        ).lean();

        return {
          ...message,
          sender: sender || null,
        };
      })
    );

    // Mark conversation as read if user is viewing it
    await conversationService.markConversationAsRead(conversationId, userId);

    return {
      data: messagesWithSenders,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Send a text message
   */
  async sendTextMessage(data: {
    conversationId: string;
    senderId: string;
    senderType: 'user' | 'creator' | 'system';
    content: string;
    clientId?: string;
    metadata?: {
      tags?: string[];
      isAnswer?: boolean;
      customFields?: Record<string, any>;
    };
  }) {
    // Verify conversation exists
    const conversation = await ConversationModel.findOne({
      id: data.conversationId,
      deletedAt: null,
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Verify sender has access to conversation
    if (
      data.senderType === 'user' && conversation.userId !== data.senderId ||
      data.senderType === 'creator' && conversation.creatorId !== data.senderId
    ) {
      throw createBadRequestError('Sender does not have access to this conversation', ErrorCodes.UNAUTHORIZED);
    }

    // Create message with all required fields
    const message = new MessageModel({
      conversationId: data.conversationId,
      senderId: data.senderId,
      senderType: data.senderType,
      content: data.content,
      contentType: 'text',
      isDelivered: true,
      deliveredAt: new Date(),
      metadata: {
        clientId: data.clientId,
        tags: data.metadata?.tags || [],
        isAnswer: data.metadata?.isAnswer || false,
        customFields: data.metadata?.customFields || {},
      },
      // Add required fields from base schema
      createdBy: data.senderId,
      updatedBy: data.senderId,
    });

    // Save message
    await message.save();

    // Update conversation last message timestamp
    await conversationService.updateLastMessageTimestamp(data.conversationId);

    // Increment unread count for recipient
    const recipientId = data.senderType === 'user' ? conversation.creatorId : conversation.userId;
    await conversationService.incrementUnreadCount(data.conversationId, recipientId);

    return message;
  }

  /**
   * Send a media message (voice, image, video, file)
   */
  async sendMediaMessage(data: {
    conversationId: string;
    senderId: string;
    senderType: 'user' | 'creator' | 'system';
    contentType: 'voice' | 'image' | 'video' | 'file';
    mediaUrl: string;
    mediaMimeType: string;
    content?: string;
    clientId?: string;
    metadata?: {
      tags?: string[];
      isAnswer?: boolean;
      customFields?: Record<string, any>;
    };
  }) {
    // Verify conversation exists
    const conversation = await ConversationModel.findOne({
      id: data.conversationId,
      deletedAt: null,
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Verify sender has access to conversation
    if (
      data.senderType === 'user' && conversation.userId !== data.senderId ||
      data.senderType === 'creator' && conversation.creatorId !== data.senderId
    ) {
      throw createBadRequestError('Sender does not have access to this conversation', ErrorCodes.UNAUTHORIZED);
    }

    // Create message with all required fields
    const message = new MessageModel({
      conversationId: data.conversationId,
      senderId: data.senderId,
      senderType: data.senderType,
      content: data.content || '',
      contentType: data.contentType,
      mediaUrl: data.mediaUrl,
      mediaMimeType: data.mediaMimeType,
      isDelivered: true,
      deliveredAt: new Date(),
      metadata: {
        clientId: data.clientId,
        tags: data.metadata?.tags || [],
        isAnswer: data.metadata?.isAnswer || false,
        customFields: data.metadata?.customFields || {},
      },
      // Add required fields from base schema
      createdBy: data.senderId,
      updatedBy: data.senderId,
    });

    // Save message
    await message.save();

    // Update conversation last message timestamp
    await conversationService.updateLastMessageTimestamp(data.conversationId);

    // Increment unread count for recipient
    const recipientId = data.senderType === 'user' ? conversation.creatorId : conversation.userId;
    await conversationService.incrementUnreadCount(data.conversationId, recipientId);

    return message;
  }

  /**
   * Mark a message as read
   */
  async markMessageAsRead(messageId: string, userId: string) {
    // Get message
    const message = await MessageModel.findOne({
      id: messageId,
      deletedAt: null,
    });

    if (!message) {
      throw createNotFoundError('Message not found', ErrorCodes.MESSAGE_NOT_FOUND);
    }

    // Verify user has access to message
    const conversation = await ConversationModel.findOne({
      id: message.conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Only mark as read if user is the recipient
    if (
      (message.senderType === 'user' && conversation.creatorId === userId) ||
      (message.senderType === 'creator' && conversation.userId === userId)
    ) {
      message.isRead = true;
      message.readAt = new Date();
      await message.save();
    }

    return { success: true };
  }

  /**
   * Delete a message (soft delete)
   */
  async deleteMessage(messageId: string, userId: string) {
    // Get message
    const message = await MessageModel.findOne({
      id: messageId,
      deletedAt: null,
    });

    if (!message) {
      throw createNotFoundError('Message not found', ErrorCodes.MESSAGE_NOT_FOUND);
    }

    // Verify user has access to message
    const conversation = await ConversationModel.findOne({
      id: message.conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // Only allow deletion if user is the sender
    if (message.senderId !== userId) {
      throw createBadRequestError('You can only delete your own messages', ErrorCodes.UNAUTHORIZED);
    }

    // Soft delete
    message.deletedAt = new Date();
    await message.save();

    return { success: true };
  }

  /**
   * Translate a message
   */
  async translateMessage(messageId: string, userId: string, targetLanguage: string) {
    // Get message
    const message = await MessageModel.findOne({
      id: messageId,
      deletedAt: null,
    });

    if (!message) {
      throw createNotFoundError('Message not found', ErrorCodes.MESSAGE_NOT_FOUND);
    }

    // Verify user has access to message
    const conversation = await ConversationModel.findOne({
      id: message.conversationId,
      deletedAt: null,
      $or: [{ userId }, { creatorId: userId }],
    });

    if (!conversation) {
      throw createNotFoundError('Conversation not found', ErrorCodes.CONVERSATION_NOT_FOUND);
    }

    // In a real implementation, we would call a translation service here
    // For now, we'll just simulate translation by adding a prefix
    const translatedContent = `[Translated to ${targetLanguage}] ${message.content}`;

    // Store original content if this is the first translation
    if (!message.originalContent) {
      message.originalContent = message.content;
      message.originalLanguage = conversation.userId === userId ? conversation.userLanguage : conversation.creatorLanguage;
    }

    // Update message with translated content
    message.content = translatedContent;
    await message.save();

    return message;
  }
}

export const messageService = new MessageService();
