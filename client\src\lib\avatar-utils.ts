/**
 * Utility functions for avatar handling
 */

/**
 * Get initials from a name for avatar fallback
 * @param name The name to get initials from
 * @param maxLength Maximum number of characters to return (default: 2)
 * @returns Uppercase initials from the name
 */
export function getInitials(name: string, maxLength: number = 2): string {
  if (!name) return '?';
  
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, maxLength);
}

/**
 * Standard error handler for avatar images
 * @param e Error event
 * @param entityName Name of the entity (user, channel, etc.)
 */
export function handleAvatarError(e: React.SyntheticEvent<HTMLImageElement, Event>, entityName: string): void {
  console.warn(`Failed to load avatar for ${entityName}`);
  e.currentTarget.style.display = 'none'; // Hide the image to show fallback
}
