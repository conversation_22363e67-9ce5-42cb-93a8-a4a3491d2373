import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, ChevronRight, ArrowUpDown, Search, Trash2, AlertCircle, Plus, Activity, Calendar, User, Filter, Save, X } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';

// Mock data for activities
const mockActivities = [
  { id: '1', title: 'Morning Yoga', addedBy: 'John Doe', date: '2023-07-15', category: 'Fitness' },
  { id: '2', title: 'Meditation Session', addedBy: 'Jane Smith', date: '2023-07-10', category: 'Wellness' },
  { id: '3', title: 'Team Building Exercise', addedBy: 'Mike Johnson', date: '2023-07-05', category: 'Team' },
];

export default function ActivitiesPage() {
  const [activities, setActivities] = useState(mockActivities);
  const [selectedActivities, setSelectedActivities] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [newActivity, setNewActivity] = useState({
    title: '',
    category: 'Fitness',
    description: ''
  });

  // Show success alert
  const displaySuccessAlert = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);
  };

  // Filter activities based on search term and category
  const filteredActivities = activities.filter(activity => {
    const matchesSearch =
      activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.addedBy.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = filterCategory === 'all' || activity.category === filterCategory;

    return matchesSearch && matchesCategory;
  });

  // Sort activities
  const sortedActivities = [...filteredActivities].sort((a, b) => {
    if (!sortField) return 0;

    const fieldA = a[sortField as keyof typeof a];
    const fieldB = b[sortField as keyof typeof b];

    if (fieldA < fieldB) return sortDirection === 'asc' ? -1 : 1;
    if (fieldA > fieldB) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle activity selection
  const handleSelectActivity = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedActivities([...selectedActivities, id]);
    } else {
      setSelectedActivities(selectedActivities.filter(activityId => activityId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedActivities(sortedActivities.map(activity => activity.id));
    } else {
      setSelectedActivities([]);
    }
  };

  // Handle delete selected
  const handleDeleteSelected = () => {
    if (selectedActivities.length === 0) return;

    if (window.confirm('Are you sure you want to delete the selected activities?')) {
      setActivities(activities.filter(activity => !selectedActivities.includes(activity.id)));
      setSelectedActivities([]);
      displaySuccessAlert(`Successfully deleted ${selectedActivities.length} activities`);
    }
  };

  // Get category badge color
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Fitness':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'Wellness':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-100';
      case 'Team':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  // Get active filters count
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filterCategory !== 'all') count++;
    return count;
  };

  // Handle adding a new activity
  const handleAddActivity = () => {
    if (!newActivity.title.trim()) {
      alert('Please enter a title for the activity');
      return;
    }

    const newActivityItem = {
      id: (Math.max(...activities.map(a => parseInt(a.id)), 0) + 1).toString(),
      title: newActivity.title,
      category: newActivity.category,
      addedBy: 'Current User', // In a real app, this would be the current user's name
      date: new Date().toISOString().split('T')[0]
    };

    setActivities([...activities, newActivityItem]);
    setIsAddDialogOpen(false);
    displaySuccessAlert(`Successfully added new activity: ${newActivity.title}`);

    // Reset form
    setNewActivity({
      title: '',
      category: 'Fitness',
      description: ''
    });
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Activities</h1>
              <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Activity
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle>Add New Activity</DialogTitle>
                    <DialogDescription>
                      Create a new activity for your platform. Fill in the details below.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="title">Activity Title</Label>
                      <Input
                        id="title"
                        value={newActivity.title}
                        onChange={(e) => setNewActivity({...newActivity, title: e.target.value})}
                        placeholder="Enter activity title"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select
                        value={newActivity.category}
                        onValueChange={(value) => setNewActivity({...newActivity, category: value})}
                      >
                        <SelectTrigger id="category">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Fitness">Fitness</SelectItem>
                          <SelectItem value="Wellness">Wellness</SelectItem>
                          <SelectItem value="Team">Team</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="description">Description (Optional)</Label>
                      <Textarea
                        id="description"
                        value={newActivity.description}
                        onChange={(e) => setNewActivity({...newActivity, description: e.target.value})}
                        placeholder="Enter activity description"
                        className="min-h-[100px]"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                      <X className="mr-2 h-4 w-4" />
                      Cancel
                    </Button>
                    <Button onClick={handleAddActivity} className="gap-2">
                      <Save className="h-4 w-4" />
                      Save Activity
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Tools
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Manage Activities</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Activities</CardTitle>
                    <CardDescription>View and manage activities for your platform</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search activities..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant={getActiveFiltersCount() > 0 ? "default" : "outline"}
                          className="min-w-[120px]"
                        >
                          <Filter className="mr-2 h-4 w-4" />
                          Filter {getActiveFiltersCount() > 0 && (
                            <Badge variant="secondary" className="ml-2 bg-white text-primary">{getActiveFiltersCount()}</Badge>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[240px] p-0" align="end">
                        <div className="bg-primary text-primary-foreground px-4 py-3 font-medium">
                          Filter Activities
                        </div>
                        <div className="p-4 space-y-4">
                          <div className="space-y-2">
                            <Label htmlFor="category" className="text-sm font-medium">Category</Label>
                            <Select value={filterCategory} onValueChange={setFilterCategory}>
                              <SelectTrigger id="category">
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All Categories</SelectItem>
                                <SelectItem value="Fitness">Fitness</SelectItem>
                                <SelectItem value="Wellness">Wellness</SelectItem>
                                <SelectItem value="Team">Team</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-muted/30 border-t">
                          {getActiveFiltersCount() > 0 && (
                            <Button variant="ghost" size="sm" onClick={() => setFilterCategory('all')}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              Clear
                            </Button>
                          )}
                          {!getActiveFiltersCount() && <div></div>}
                          <Button
                            className="bg-primary text-primary-foreground hover:bg-primary/90"
                            onClick={() => setIsFilterOpen(false)}
                            size="sm"
                          >
                            Apply
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {getActiveFiltersCount() > 0 && (
                  <div className="bg-muted/30 border rounded-md p-3 mb-6 flex items-center">
                    <div className="text-sm font-medium mr-3">Filters:</div>
                    <div className="flex flex-wrap gap-2 flex-1">
                      {filterCategory !== 'all' && (
                        <Badge variant="outline" className="px-3 py-1 bg-background flex items-center gap-1">
                          <span className="font-medium">Category:</span>
                          <span>{filterCategory}</span>
                          <Trash2
                            className="ml-1 h-3 w-3 cursor-pointer hover:text-destructive"
                            onClick={() => setFilterCategory('all')}
                          />
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedActivities.length === sortedActivities.length && sortedActivities.length > 0}
                            onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                          />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('id')}>
                          <div className="flex items-center">
                            ID
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('title')}>
                          <div className="flex items-center">
                            TITLE
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('category')}>
                          <div className="flex items-center">
                            CATEGORY
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('addedBy')}>
                          <div className="flex items-center">
                            ADDED BY
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('date')}>
                          <div className="flex items-center">
                            DATE
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead className="text-right">ACTION</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sortedActivities.length > 0 ? (
                        sortedActivities.map((activity) => (
                          <TableRow key={activity.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedActivities.includes(activity.id)}
                                onCheckedChange={(checked) => handleSelectActivity(activity.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{activity.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Activity className="h-4 w-4 text-muted-foreground" />
                                {activity.title}
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge className={getCategoryColor(activity.category)}>
                                {activity.category}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-muted-foreground" />
                                {activity.addedBy}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                {activity.date}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                                onClick={() => {
                                  if (window.confirm('Are you sure you want to delete this activity?')) {
                                    setActivities(activities.filter(a => a.id !== activity.id));
                                    displaySuccessAlert(`Successfully deleted activity: ${activity.title}`);
                                  }
                                }}
                              >
                                <Trash2 className="h-4 w-4" />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                            <div className="flex flex-col items-center justify-center py-4">
                              <Activity className="h-10 w-10 text-muted-foreground mb-2 opacity-20" />
                              <p>No activities found</p>
                              <p className="text-sm text-muted-foreground">Try adjusting your filters or search term</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                {selectedActivities.length > 0 && (
                  <div className="flex items-center justify-between bg-muted/30 p-3 rounded-md mt-4">
                    <div className="text-sm">
                      <span className="font-medium">{selectedActivities.length}</span> {selectedActivities.length === 1 ? 'activity' : 'activities'} selected
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={handleDeleteSelected}
                      className="gap-2"
                    >
                      <Trash2 className="h-4 w-4" />
                      Delete Selected
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
