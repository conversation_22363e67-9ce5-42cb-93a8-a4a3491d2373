import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Info, Languages, Check, AlertTriangle, Zap, Server } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslation, TranslationProvider } from '@/context/TranslationContext';
import { toast } from 'sonner';
import testBhashiniPipeline from '@/utils/bhashiniApiTest';
import { testConnection, translateText as apiTranslate } from '@/services/translationApi';

// Available languages
const availableLanguages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
  { code: 'mr', name: 'Marathi', flag: '🇮🇳' },
  { code: 'gu', name: 'Gujarati', flag: '🇮🇳' },
  { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
  { code: 'te', name: 'Telugu', flag: '🇮🇳' },
  { code: 'bn', name: 'Bengali', flag: '🇮🇳' },
  { code: 'kn', name: 'Kannada', flag: '🇮🇳' },
  { code: 'ml', name: 'Malayalam', flag: '🇮🇳' },
  { code: 'pa', name: 'Punjabi', flag: '🇮🇳' },
  { code: 'ur', name: 'Urdu', flag: '🇮🇳' },
  { code: 'or', name: 'Odia', flag: '🇮🇳' },
  { code: 'as', name: 'Assamese', flag: '🇮🇳' },
];

export default function TranslationSettings() {
  const {
    isTranslationEnabled,
    enableTranslation,
    disableTranslation,
    translationProvider,
    setTranslationProvider,
    translationProviders,
    updateTranslationProvider,
    currentLanguage,
    setCurrentLanguage,
  } = useTranslation();

  const [activeTab, setActiveTab] = useState<TranslationProvider>('bhashini');
  const [testingTranslation, setTestingTranslation] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [testText, setTestText] = useState('Hello, how are you today?');
  const [testLanguage, setTestLanguage] = useState('hi');
  const [testingPipeline, setTestingPipeline] = useState(false);
  const [pipelineTestResult, setPipelineTestResult] = useState<any>(null);

  // Handle provider toggle
  const handleToggleProvider = (provider: TranslationProvider, enabled: boolean) => {
    updateTranslationProvider(provider, { enabled });
    if (enabled) {
      setTranslationProvider(provider);
      enableTranslation();
    }
  };

  // Handle API key change
  const handleApiKeyChange = (provider: TranslationProvider, apiKey: string) => {
    updateTranslationProvider(provider, { apiKey });
  };

  // Handle userId change for Bhashini
  const handleUserIdChange = (userId: string) => {
    updateTranslationProvider('bhashini', { userId });
  };

  // Handle ULCA API key change for Bhashini
  const handleUlcaApiKeyChange = (ulcaApiKey: string) => {
    updateTranslationProvider('bhashini', { ulcaApiKey });
  };

  // Test translation
  const handleTestTranslation = async () => {
    if (!isTranslationEnabled) {
      toast.error('Please enable translation first');
      return;
    }

    try {
      setTestingTranslation(true);
      setTestResult(null);

      // Import the translation function
      const { translateText } = await import('@/services/bhashiniTranslationService');

      // Get the current provider config
      const providerConfig = translationProviders[translationProvider];

      if (!providerConfig.apiKey) {
        setTestResult({
          success: false,
          message: 'API key is not set'
        });
        return;
      }

      // Perform the translation test
      const translatedText = await translateText(
        testText,
        'en',
        testLanguage,
        {
          apiKey: providerConfig.apiKey,
          userId: providerConfig.userId,
          ulcaApiKey: providerConfig.ulcaApiKey
        }
      );

      if (translatedText && !translatedText.includes('[Translation Error]')) {
        setTestResult({
          success: true,
          message: `Translation successful: "${translatedText}"`
        });
        toast.success('Translation test successful!');
      } else {
        setTestResult({
          success: false,
          message: 'Translation failed. Please check your API credentials.'
        });
        toast.error('Translation test failed');
      }
    } catch (error) {
      console.error('Error testing translation:', error);
      setTestResult({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      toast.error('Translation test failed');
    } finally {
      setTestingTranslation(false);
    }
  };

  // Test Bhashini API pipeline
  const handleTestPipeline = async () => {
    if (!isTranslationEnabled) {
      toast.error('Please enable translation first');
      return;
    }

    try {
      setTestingPipeline(true);
      setPipelineTestResult(null);

      // Get the current provider config
      const providerConfig = translationProviders[translationProvider];

      if (!providerConfig.apiKey) {
        setPipelineTestResult({
          success: false,
          message: 'API key is not set'
        });
        return;
      }

      // Test the Bhashini API pipeline
      const results = await testBhashiniPipeline({
        apiKey: providerConfig.apiKey,
        userId: providerConfig.userId,
        ulcaApiKey: providerConfig.ulcaApiKey
      });

      setPipelineTestResult(results);

      if (results.success) {
        toast.success('Bhashini API pipeline test successful!');
      } else {
        toast.error('Bhashini API pipeline test failed');
      }
    } catch (error) {
      console.error('Error testing Bhashini API pipeline:', error);
      setPipelineTestResult({
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      });
      toast.error('Bhashini API pipeline test failed');
    } finally {
      setTestingPipeline(false);
    }
  };

  // Set Bhashini as default provider on component mount
  useEffect(() => {
    console.log("TranslationSettings: Setting up Bhashini as default provider");

    // Force enable Bhashini translation
    if (!isTranslationEnabled) {
      enableTranslation();
    }

    // Set Bhashini as the active provider
    setTranslationProvider('bhashini');

    // Make sure Bhashini is enabled with correct credentials
    updateTranslationProvider('bhashini', {
      enabled: true,
      apiKey: 'cee60134c6bb4d179efd3fda48ff32fe',
      userId: 'cee60134c6bb4d179efd3fda48ff32fe',
      ulcaApiKey: '13a647c84b-2747-4f0c-afcd-2ac8235f5318'
    });

    // Set active tab to Bhashini
    setActiveTab('bhashini');

    console.log("TranslationSettings: Bhashini provider setup complete");
  }, []);

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-bold">Translation Settings</CardTitle>
            <CardDescription>
              Configure automatic translation for chat messages
            </CardDescription>
          </div>
          <Switch
            checked={isTranslationEnabled}
            onCheckedChange={(checked) => checked ? enableTranslation() : disableTranslation()}
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Enable automatic translation to allow users to view messages in their preferred language.
          </p>

          <Tabs defaultValue="bhashini" value={activeTab} onValueChange={(value) => setActiveTab(value as TranslationProvider)}>
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="simple">Simple</TabsTrigger>
              <TabsTrigger value="bhashini">Bhashini</TabsTrigger>
              <TabsTrigger value="google">Google</TabsTrigger>
              <TabsTrigger value="custom">Custom</TabsTrigger>
            </TabsList>

            {/* Simple Tab */}
            <TabsContent value="simple" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="simple-enabled" className="font-medium">Enable Simple Translation</Label>
                <Switch
                  id="simple-enabled"
                  checked={translationProviders.simple.enabled}
                  onCheckedChange={(checked) => handleToggleProvider('simple', checked)}
                />
              </div>

              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-500" />
                <AlertDescription className="text-blue-800">
                  Simple translation uses a basic dictionary-based approach with no API key required.
                  It's suitable for testing but has limited accuracy.
                </AlertDescription>
              </Alert>
            </TabsContent>

            {/* Bhashini Tab */}
            <TabsContent value="bhashini" className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="bhashini-enabled" className="font-medium">Enable Bhashini Translation</Label>
                <Switch
                  id="bhashini-enabled"
                  checked={translationProviders.bhashini.enabled}
                  onCheckedChange={(checked) => handleToggleProvider('bhashini', checked)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="bhashini-api-key" className="font-medium">API Key</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your Bhashini API key</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="bhashini-api-key"
                  type="password"
                  placeholder="Enter your Bhashini API key"
                  value={translationProviders.bhashini.apiKey}
                  onChange={(e) => handleApiKeyChange('bhashini', e.target.value)}
                  disabled={!translationProviders.bhashini.enabled}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="bhashini-user-id" className="font-medium">User ID</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your Bhashini User ID</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="bhashini-user-id"
                  type="text"
                  placeholder="Enter your Bhashini User ID"
                  value={translationProviders.bhashini.userId || ''}
                  onChange={(e) => handleUserIdChange(e.target.value)}
                  disabled={!translationProviders.bhashini.enabled}
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="bhashini-ulca-key" className="font-medium">ULCA API Key</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-4 w-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your Bhashini ULCA API Key (Udyat Key)</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="bhashini-ulca-key"
                  type="password"
                  placeholder="Enter your Bhashini ULCA API Key"
                  value={translationProviders.bhashini.ulcaApiKey || ''}
                  onChange={(e) => handleUlcaApiKeyChange(e.target.value)}
                  disabled={!translationProviders.bhashini.enabled}
                />
              </div>

              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-500" />
                <AlertDescription className="text-blue-800">
                  Bhashini is an AI-based translation service developed by the Government of India
                  that specializes in Indian languages.
                </AlertDescription>
              </Alert>

              <div className="mt-4 space-y-2">
                <Button
                  onClick={handleTestPipeline}
                  disabled={testingPipeline || !translationProviders.bhashini.enabled}
                  variant="outline"
                  className="w-full"
                >
                  <Zap className="mr-2 h-4 w-4" />
                  {testingPipeline ? 'Testing Pipeline...' : 'Test Bhashini API Pipeline'}
                </Button>

                <Button
                  onClick={async () => {
                    try {
                      setTestingPipeline(true);
                      const result = await testConnection(
                        translationProviders.bhashini.userId,
                        translationProviders.bhashini.ulcaApiKey
                      );

                      if (result.success) {
                        toast.success('Server API connection test successful!');
                      } else {
                        toast.error('Server API connection test failed');
                      }

                      setPipelineTestResult({
                        success: result.success,
                        message: result.message,
                        details: { serverTest: true }
                      });
                    } catch (error) {
                      console.error('Error testing server API connection:', error);
                      setPipelineTestResult({
                        success: false,
                        message: error instanceof Error ? error.message : 'Unknown error occurred',
                        details: { serverTest: true, error: error instanceof Error ? error.message : 'Unknown error' }
                      });
                      toast.error('Server API connection test failed');
                    } finally {
                      setTestingPipeline(false);
                    }
                  }}
                  disabled={testingPipeline || !translationProviders.bhashini.enabled}
                  variant="outline"
                  className="w-full"
                >
                  <Server className="mr-2 h-4 w-4" />
                  {testingPipeline ? 'Testing Server API...' : 'Test Server API Connection'}
                </Button>

                {pipelineTestResult && (
                  <div className="mt-4 space-y-2">
                    <Alert className={pipelineTestResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
                      {pipelineTestResult.success ? (
                        <Check className="h-4 w-4 text-green-500" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                      )}
                      <AlertDescription className={pipelineTestResult.success ? "text-green-800" : "text-red-800"}>
                        {pipelineTestResult.message}
                      </AlertDescription>
                    </Alert>

                    {pipelineTestResult.details && (
                      <div className="text-sm space-y-2">
                        {pipelineTestResult.details.authToken && (
                          <div>
                            <p className="font-medium">Authentication:</p>
                            <p className="text-green-600">
                              Token received: {pipelineTestResult.details.authToken}
                            </p>
                          </div>
                        )}

                        {pipelineTestResult.details.translationResult && (
                          <div>
                            <p className="font-medium">Translation Result:</p>
                            <p className="text-green-600">
                              {pipelineTestResult.details.translationResult}
                            </p>
                          </div>
                        )}

                        {pipelineTestResult.details.error && (
                          <div>
                            <p className="font-medium">Error:</p>
                            <p className="text-red-600">
                              {pipelineTestResult.details.error}
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Test Translation Section */}
            <div className="mt-8 p-4 border rounded-md">
              <h3 className="text-md font-medium mb-4">Test Translation</h3>

              <div className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Label htmlFor="test-text">Text to translate</Label>
                    <Input
                      id="test-text"
                      value={testText}
                      onChange={(e) => setTestText(e.target.value)}
                      placeholder="Enter text to translate"
                    />
                  </div>

                  <div className="w-40">
                    <Label htmlFor="test-language">Target Language</Label>
                    <Select value={testLanguage} onValueChange={setTestLanguage}>
                      <SelectTrigger id="test-language">
                        <SelectValue placeholder="Select language" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableLanguages.filter(lang => lang.code !== 'en').map((lang) => (
                          <SelectItem key={lang.code} value={lang.code}>
                            {lang.flag} {lang.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={handleTestTranslation}
                    disabled={testingTranslation || !isTranslationEnabled}
                    className="flex-1"
                  >
                    {testingTranslation ? 'Testing...' : 'Test Client Translation'}
                  </Button>

                  <Button
                    onClick={async () => {
                      try {
                        setTestingTranslation(true);
                        setTestResult(null);

                        // Get the current provider config
                        const providerConfig = translationProviders[translationProvider];

                        // Test the server API translation
                        const translatedText = await apiTranslate(
                          testText,
                          'en',
                          testLanguage,
                          providerConfig.userId,
                          providerConfig.ulcaApiKey
                        );

                        if (translatedText && !translatedText.includes('[Translation Error]')) {
                          setTestResult({
                            success: true,
                            message: `Server API Translation successful: "${translatedText}"`
                          });
                          toast.success('Server API Translation test successful!');
                        } else {
                          setTestResult({
                            success: false,
                            message: 'Server API Translation failed. Please check your API credentials.'
                          });
                          toast.error('Server API Translation test failed');
                        }
                      } catch (error) {
                        console.error('Error testing server API translation:', error);
                        setTestResult({
                          success: false,
                          message: error instanceof Error ? error.message : 'Unknown error occurred'
                        });
                        toast.error('Server API Translation test failed');
                      } finally {
                        setTestingTranslation(false);
                      }
                    }}
                    disabled={testingTranslation || !isTranslationEnabled}
                    className="flex-1"
                    variant="outline"
                  >
                    <Server className="mr-2 h-4 w-4" />
                    {testingTranslation ? 'Testing...' : 'Test Server API'}
                  </Button>
                </div>

                {testResult && (
                  <Alert className={testResult.success ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}>
                    {testResult.success ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                    )}
                    <AlertDescription className={testResult.success ? "text-green-800" : "text-red-800"}>
                      {testResult.message}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </div>
          </Tabs>
        </div>
      </CardContent>
    </Card>
  );
}
