import { config } from 'dotenv';
import axios from 'axios';

// Load environment variables
config();

// API base URL
const API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';

/**
 * Test API error handling
 */
async function testApiErrors() {
  try {
    console.log('🔍 Testing API error handling...');
    
    // Test 404 error
    console.log('\n1. Testing 404 error (route not found)...');
    try {
      await axios.get(`${API_URL}/non-existent-route`);
    } catch (error) {
      console.log('✅ 404 error handled correctly:');
      console.log(error.response.data);
    }
    
    // Test validation error
    console.log('\n2. Testing validation error...');
    try {
      await axios.post(`${API_URL}/auth/login`, {
        // Missing required fields
      });
    } catch (error) {
      console.log('✅ Validation error handled correctly:');
      console.log(error.response.data);
    }
    
    // Test unauthorized error
    console.log('\n3. Testing unauthorized error...');
    try {
      await axios.get(`${API_URL}/auth/me`, {
        headers: {
          Authorization: 'Bearer invalid-token',
        },
      });
    } catch (error) {
      console.log('✅ Unauthorized error handled correctly:');
      console.log(error.response.data);
    }
    
    // Test not found error
    console.log('\n4. Testing not found error (resource not found)...');
    try {
      await axios.get(`${API_URL}/users/999999999`);
    } catch (error) {
      console.log('✅ Not found error handled correctly:');
      console.log(error.response.data);
    }
    
    // Test conflict error
    console.log('\n5. Testing conflict error...');
    try {
      // First, register a user
      await axios.post(`${API_URL}/users/register`, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Test',
        lastName: 'User',
        displayName: 'Test User',
      });
      
      // Then, try to register the same user again
      await axios.post(`${API_URL}/users/register`, {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'Password123!',
        firstName: 'Test',
        lastName: 'User',
        displayName: 'Test User',
      });
    } catch (error) {
      console.log('✅ Conflict error handled correctly:');
      console.log(error.response.data);
    }
    
    console.log('\n✅ API error handling test completed successfully!');
  } catch (error) {
    console.error('❌ Error testing API error handling:', error.message);
  }
}

// Run the test
testApiErrors();
