import { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { useToast } from '@/hooks/use-toast';
import { videoAPI } from '@/services/api';
import VideoCard from '@/components/video/VideoCard';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { Video } from '@/types';

export default function SavedVideos() {
  const [savedVideos, setSavedVideos] = useState<Video[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { t } = useLanguage();
  const { toast } = useToast();

  useEffect(() => {
    const fetchSavedVideos = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (!user) {
          // Load from local storage for non-authenticated users
          const localSavedVideos = JSON.parse(localStorage.getItem('savedVideos') || '[]');
          setSavedVideos(localSavedVideos);
          setIsLoading(false);
          return;
        }

        // For authenticated users, get videos from their channels
        const channels = await videoAPI.getUserChannels();

        if (!channels || channels.length === 0) {
          setIsLoading(false);
          return;
        }

        // Get videos from the first channel
        const channelId = channels[0].id;
        const response = await videoAPI.getChannelVideos(channelId);

        if (response && response.videos) {
          setSavedVideos(response.videos);
        }
      } catch (err) {
        console.error('Error fetching saved videos:', err);
        setError('Failed to load saved videos');
        toast({
          title: "Error",
          description: "Failed to load your saved videos",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSavedVideos();

    // Listen for storage changes to refresh saved videos when new videos are saved
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'savedVideos' && !user) {
        const localSavedVideos = JSON.parse(e.newValue || '[]');
        setSavedVideos(localSavedVideos);
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also listen for custom events for same-tab updates
    const handleLocalSave = () => {
      if (!user) {
        const localSavedVideos = JSON.parse(localStorage.getItem('savedVideos') || '[]');
        setSavedVideos(localSavedVideos);
      }
    };

    const handleLocalUnsave = () => {
      if (!user) {
        const localSavedVideos = JSON.parse(localStorage.getItem('savedVideos') || '[]');
        setSavedVideos(localSavedVideos);
      }
    };

    window.addEventListener('videoSaved', handleLocalSave);
    window.addEventListener('videoUnsaved', handleLocalUnsave);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('videoSaved', handleLocalSave);
      window.removeEventListener('videoUnsaved', handleLocalUnsave);
    };
  }, [user, toast]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8 border border-dashed rounded-lg">
        <h3 className="font-medium mb-2 text-red-500">{error}</h3>
        <p className="text-sm text-lingstream-muted mb-4">
          {t('profile.error_loading_saved')}
        </p>
        <Button onClick={() => {
          setError(null);
          setIsLoading(true);
          // Re-trigger the useEffect by changing a dependency
          window.location.hash = Date.now().toString();
        }}>
          {t('common.try_again')}
        </Button>
      </div>
    );
  }

  if (savedVideos.length === 0) {
    return (
      <div className="text-center p-8 border border-dashed rounded-lg">
        <h3 className="font-medium mb-2">{t('profile.no_saved_videos')}</h3>
        <p className="text-sm text-lingstream-muted mb-4">
          {t('profile.save_videos_message')}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="font-medium">{t('profile.saved_videos')} ({savedVideos.length})</h3>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {savedVideos.map((video) => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>
    </div>
  );
}
