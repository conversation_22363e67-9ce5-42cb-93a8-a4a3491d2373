import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import {
  Plus,
  Save,
  X,
  Search,
  Globe,
  Languages,
  Key,
  Flag,
  Code,
  ArrowRightLeft,
  Check,
  Trash2,
  RotateCcw,
  AlertCircle,
  Settings
} from 'lucide-react';

// Import shadcn UI components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import {
  Alert,
  AlertDescription,
  AlertTitle
} from '@/components/ui/alert';

export default function AddNewLanguageKeyPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();

  // State for language form
  const [languageName, setLanguageName] = useState('');
  const [languageCode, setLanguageCode] = useState('');
  const [direction, setDirection] = useState('ltr');
  const [isDefault, setIsDefault] = useState(false);
  const [isActive, setIsActive] = useState(true);

  // State for key form
  const [keyName, setKeyName] = useState('');
  const [keyValue, setKeyValue] = useState('');

  // State for keys table
  const [keys, setKeys] = useState([
    { id: '1', key: 'welcome_message', value: 'Welcome to our platform' },
    { id: '2', key: 'login_button', value: 'Log In' },
    { id: '3', key: 'signup_button', value: 'Sign Up' }
  ]);

  // State for search
  const [searchTerm, setSearchTerm] = useState('');

  // Filtered keys based on search term
  const filteredKeys = keys.filter(item =>
    item.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle adding a new language
  const handleAddLanguage = () => {
    // Validation and API call would go here
    console.log('Adding language:', { languageName, languageCode, direction, isDefault, isActive });

    // Reset form
    setLanguageName('');
    setLanguageCode('');
    setDirection('ltr');
    setIsDefault(false);
    setIsActive(true);
  };

  // Handle adding a new key
  const handleAddKey = () => {
    // Validation and API call would go here
    if (keyName && keyValue) {
      const newKey = {
        id: Date.now().toString(),
        key: keyName,
        value: keyValue
      };

      setKeys([...keys, newKey]);

      // Reset form
      setKeyName('');
      setKeyValue('');
    }
  };

  // Handle deleting a key
  const handleDeleteKey = (id: string) => {
    setKeys(keys.filter(key => key.id !== id));
  };

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <main className="flex-1 bg-background p-6 overflow-y-auto">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Language & Translation Management</h1>
              <Breadcrumb className="mt-1">
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/admin" className="flex items-center gap-1">
                      <Globe className="h-3.5 w-3.5" />
                      Admin Panel
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/settings" className="flex items-center gap-1">
                      <Settings className="h-3.5 w-3.5" />
                      Settings
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage className="flex items-center gap-1">
                      <Key className="h-3.5 w-3.5" />
                      Add New Language & Key
                    </BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            <Button className="gap-2">
              <Save className="h-4 w-4" />
              Save Changes
            </Button>
          </div>

          <div className="bg-card rounded-lg shadow-sm p-6">
            <Tabs defaultValue="languages" className="w-full">
              <TabsList className="grid grid-cols-3 mb-6">
                <TabsTrigger value="languages" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Languages
                </TabsTrigger>
                <TabsTrigger value="keys" className="flex items-center gap-2">
                  <Key className="h-4 w-4" />
                  Translation Keys
                </TabsTrigger>
                <TabsTrigger value="import" className="flex items-center gap-2">
                  <RotateCcw className="h-4 w-4" />
                  Import/Export
                </TabsTrigger>
              </TabsList>

              {/* Languages Tab */}
              <TabsContent value="languages">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Languages className="h-5 w-5 text-primary" />
                        Add New Language
                      </CardTitle>
                      <CardDescription>
                        Add a new language to your application
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Flag className="h-4 w-4 text-muted-foreground" />
                            <Label htmlFor="language-name" className="text-base">Language Name</Label>
                          </div>
                          <p className="text-sm text-muted-foreground">Use only english letters, no spaces allowed. E.g: russian</p>
                        </div>
                        <Input
                          id="language-name"
                          placeholder="e.g. French"
                          value={languageName}
                          onChange={(e) => setLanguageName(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2 pt-4 border-t">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Code className="h-4 w-4 text-muted-foreground" />
                            <Label htmlFor="language-code" className="text-base">Language Code</Label>
                          </div>
                          <p className="text-sm text-muted-foreground">Write the language ISO code. E.g for russian: ru</p>
                        </div>
                        <Input
                          id="language-code"
                          placeholder="e.g. fr"
                          value={languageCode}
                          onChange={(e) => setLanguageCode(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2 pt-4 border-t">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <ArrowRightLeft className="h-4 w-4 text-muted-foreground" />
                            <Label htmlFor="direction" className="text-base">Direction</Label>
                          </div>
                          <p className="text-sm text-muted-foreground">Text direction for this language</p>
                        </div>
                        <Select value={direction} onValueChange={setDirection}>
                          <SelectTrigger id="direction">
                            <SelectValue placeholder="Select direction" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="ltr">Left to Right (LTR)</SelectItem>
                            <SelectItem value="rtl">Right to Left (RTL)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2 pt-4 border-t">
                        <Checkbox
                          id="is-default"
                          checked={isDefault}
                          onCheckedChange={(checked) => setIsDefault(checked as boolean)}
                        />
                        <Label htmlFor="is-default" className="text-base">Set as default language</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="is-active"
                          checked={isActive}
                          onCheckedChange={(checked) => setIsActive(checked as boolean)}
                        />
                        <Label htmlFor="is-active" className="text-base">Active</Label>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button onClick={handleAddLanguage} className="gap-2">
                        <Plus size={16} />
                        Add Language
                      </Button>
                    </CardFooter>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Globe className="h-5 w-5 text-primary" />
                        Available Languages
                      </CardTitle>
                      <CardDescription>
                        Currently available languages in your application
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Language</TableHead>
                              <TableHead>Code</TableHead>
                              <TableHead>Direction</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            <TableRow>
                              <TableCell className="font-medium">English</TableCell>
                              <TableCell>en</TableCell>
                              <TableCell>LTR</TableCell>
                              <TableCell>
                                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                                  <Check className="mr-1 h-3 w-3" />
                                  Default
                                </span>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <span className="sr-only">Edit</span>
                                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="font-medium">French</TableCell>
                              <TableCell>fr</TableCell>
                              <TableCell>LTR</TableCell>
                              <TableCell>
                                <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                  Active
                                </span>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <span className="sr-only">Edit</span>
                                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className="font-medium">Spanish</TableCell>
                              <TableCell>es</TableCell>
                              <TableCell>LTR</TableCell>
                              <TableCell>
                                <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                                  Active
                                </span>
                              </TableCell>
                              <TableCell className="text-right">
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <span className="sr-only">Edit</span>
                                  <Trash2 className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Translation Keys Tab */}
              <TabsContent value="keys">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="md:col-span-1">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Key className="h-5 w-5 text-primary" />
                        Add New Key
                      </CardTitle>
                      <CardDescription>
                        Add a new translation key to your application
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <div className="space-y-0.5">
                          <Label htmlFor="key-name" className="text-base">Key Name</Label>
                          <p className="text-sm text-muted-foreground">Use only english letters, no spaces allowed, example: this_is_a_key</p>
                        </div>
                        <Input
                          id="key-name"
                          placeholder="e.g. welcome_message"
                          value={keyName}
                          onChange={(e) => setKeyName(e.target.value)}
                        />
                      </div>

                      <div className="space-y-2 pt-4 border-t">
                        <div className="space-y-0.5">
                          <Label htmlFor="key-value" className="text-base">English Value</Label>
                          <p className="text-sm text-muted-foreground">The default English translation for this key</p>
                        </div>
                        <Textarea
                          id="key-value"
                          placeholder="e.g. Welcome to our platform"
                          className="min-h-[100px]"
                          value={keyValue}
                          onChange={(e) => setKeyValue(e.target.value)}
                        />
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button onClick={handleAddKey} className="gap-2">
                        <Plus size={16} />
                        Add Key
                      </Button>
                    </CardFooter>
                  </Card>

                  <Card className="md:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Key className="h-5 w-5 text-primary" />
                        Translation Keys
                      </CardTitle>
                      <CardDescription>
                        Manage your application's translation keys
                      </CardDescription>

                      <div className="relative mt-4">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={16} />
                        <Input
                          placeholder="Search keys..."
                          className="pl-9"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Key Name</TableHead>
                              <TableHead>English Value</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {filteredKeys.length === 0 ? (
                              <TableRow>
                                <TableCell colSpan={3} className="text-center h-24 text-muted-foreground">
                                  No translation keys found matching your search criteria.
                                </TableCell>
                              </TableRow>
                            ) : (
                              filteredKeys.map((item) => (
                                <TableRow key={item.id}>
                                  <TableCell className="font-medium">{item.key}</TableCell>
                                  <TableCell>{item.value}</TableCell>
                                  <TableCell className="text-right">
                                    <TooltipProvider>
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-8 p-0"
                                            onClick={() => handleDeleteKey(item.id)}
                                          >
                                            <span className="sr-only">Delete</span>
                                            <Trash2 className="h-4 w-4 text-destructive" />
                                          </Button>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p>Delete key</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    </TooltipProvider>
                                  </TableCell>
                                </TableRow>
                              ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Import/Export Tab */}
              <TabsContent value="import">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <RotateCcw className="h-5 w-5 text-primary" />
                        Import Translations
                      </CardTitle>
                      <CardDescription>
                        Import translations from a JSON file
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Important</AlertTitle>
                        <AlertDescription>
                          The imported file should be in JSON format with the correct structure.
                          Existing translations with the same keys will be overwritten.
                        </AlertDescription>
                      </Alert>

                      <div className="flex items-center justify-center w-full">
                        <label htmlFor="dropzone-file" className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer bg-muted/50 hover:bg-muted/70 transition-colors">
                          <div className="flex flex-col items-center justify-center pt-5 pb-6">
                            <RotateCcw className="w-10 h-10 mb-3 text-muted-foreground" />
                            <p className="mb-2 text-sm text-muted-foreground">Click to upload or drag and drop</p>
                            <p className="text-xs text-muted-foreground">JSON file (MAX. 10MB)</p>
                          </div>
                          <input id="dropzone-file" type="file" className="hidden" accept=".json" />
                        </label>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button className="gap-2">
                        <RotateCcw size={16} />
                        Import Translations
                      </Button>
                    </CardFooter>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <RotateCcw className="h-5 w-5 text-primary rotate-180" />
                        Export Translations
                      </CardTitle>
                      <CardDescription>
                        Export your translations to a JSON file
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <div className="space-y-0.5">
                          <Label htmlFor="export-language" className="text-base">Language to Export</Label>
                          <p className="text-sm text-muted-foreground">Select which language translations to export</p>
                        </div>
                        <Select defaultValue="all">
                          <SelectTrigger id="export-language">
                            <SelectValue placeholder="Select language" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Languages</SelectItem>
                            <SelectItem value="en">English</SelectItem>
                            <SelectItem value="fr">French</SelectItem>
                            <SelectItem value="es">Spanish</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2 pt-4 border-t">
                        <div className="space-y-0.5">
                          <Label htmlFor="export-format" className="text-base">Export Format</Label>
                          <p className="text-sm text-muted-foreground">Choose the format for the exported file</p>
                        </div>
                        <Select defaultValue="json">
                          <SelectTrigger id="export-format">
                            <SelectValue placeholder="Select format" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="json">JSON</SelectItem>
                            <SelectItem value="csv">CSV</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button variant="outline" className="gap-2">
                        <RotateCcw size={16} className="rotate-180" />
                        Export Translations
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  );
}
