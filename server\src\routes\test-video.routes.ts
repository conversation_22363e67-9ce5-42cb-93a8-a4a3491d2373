import { FastifyInstance } from 'fastify';

/**
 * Routes for testing video playback
 */
export default async function testVideoRoutes(fastify: FastifyInstance) {
  // Test endpoint for UUID format video IDs
  fastify.get('/test-uuid-video', async (request, reply) => {
    // Return a simple HTML page with our UuidVideoPlayer embedded
    const html = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>UUID Video Test</title>
        <style>
          body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
          .video-container { width: 100%; max-width: 800px; margin: 0 auto; }
          .video-player { aspect-ratio: 16/9; background: #000; }
          h1 { text-align: center; }
          .controls { margin-top: 20px; text-align: center; }
          button { padding: 10px 20px; margin: 0 10px; cursor: pointer; }
          input { padding: 10px; width: 300px; margin-bottom: 10px; }
        </style>
      </head>
      <body>
        <h1>UUID Video Test</h1>
        <div class="video-container">
          <div id="video-player" class="video-player"></div>
          <div class="controls">
            <input type="text" id="video-id" placeholder="Enter video ID (UUID format)" value="8f23eb35-db65-44cb-906d-24356f2edaf2">
            <br>
            <button id="play-button">Play Video</button>
          </div>
        </div>

        <script>
          // Simple script to test video playback
          document.getElementById('play-button').addEventListener('click', function() {
            const videoId = document.getElementById('video-id').value.trim();
            const playerContainer = document.getElementById('video-player');
            
            // Clear the container
            playerContainer.innerHTML = '';
            
            // Create an iframe with the video ID
            const iframe = document.createElement('iframe');
            iframe.src = \`https://engaxe.com/embed/\${videoId}\`;
            iframe.width = '100%';
            iframe.height = '100%';
            iframe.style.border = 'none';
            iframe.allowFullscreen = true;
            iframe.allow = 'encrypted-media; picture-in-picture; autoplay';
            iframe.setAttribute('frameborder', '0');
            
            // Append the iframe to the container
            playerContainer.appendChild(iframe);
            
            console.log('Playing video with ID:', videoId);
          });
          
          // Auto-play the default video
          document.getElementById('play-button').click();
        </script>
      </body>
      </html>
    `;
    
    reply.type('text/html').send(html);
  });
}
