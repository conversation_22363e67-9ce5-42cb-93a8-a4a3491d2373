import { FastifyInstance, FastifyPluginAsync } from 'fastify';
import { VideoJsonStorageService } from '../services/video-json-storage.service';
import { authenticate } from '../middleware/auth';

const videoJsonRoutes: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  const jsonStorageService = new VideoJsonStorageService();

  // Get all videos from JSON storage
  fastify.get('/json/videos', async (request, reply) => {
    try {
      const videos = await jsonStorageService.getAllVideos();
      return reply.code(200).send({
        success: true,
        data: videos,
        count: videos.length
      });
    } catch (error) {
      console.error('Error getting videos from JSON storage:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to get videos from JSON storage'
      });
    }
  });

  // Search videos in JSON storage
  fastify.get<{
    Querystring: { q: string }
  }>('/json/videos/search', async (request, reply) => {
    try {
      const { q } = request.query;
      
      if (!q || q.trim().length === 0) {
        return reply.code(400).send({
          success: false,
          message: 'Search query is required'
        });
      }

      const videos = await jsonStorageService.searchVideos(q);
      return reply.code(200).send({
        success: true,
        data: videos,
        count: videos.length,
        query: q
      });
    } catch (error) {
      console.error('Error searching videos in JSON storage:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to search videos in JSON storage'
      });
    }
  });

  // Get video by ID from JSON storage
  fastify.get<{
    Params: { id: string }
  }>('/json/videos/:id', async (request, reply) => {
    try {
      const { id } = request.params;

      if (!id) {
        return reply.code(400).send({
          success: false,
          message: 'Video ID is required'
        });
      }

      console.log(`🎥 Looking for video with ID: ${id} in JSON storage`);
      const video = await jsonStorageService.getVideoById(id);

      if (!video) {
        console.log(`🎥 Video not found in JSON storage: ${id}`);
        return reply.code(404).send({
          success: false,
          message: 'Video not found in JSON storage'
        });
      }

      console.log(`🎥 Found video in JSON storage: ${video.title}`);
      return reply.code(200).send({
        success: true,
        data: video
      });
    } catch (error) {
      console.error('Error getting video by ID from JSON storage:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to get video from JSON storage',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get JSON storage statistics
  fastify.get('/json/stats', async (request, reply) => {
    try {
      const stats = await jsonStorageService.getStorageStats();
      return reply.code(200).send({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('Error getting JSON storage stats:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to get JSON storage statistics'
      });
    }
  });

  // Sync all videos from database to JSON storage (authenticated)
  fastify.post('/json/sync', {
    preHandler: authenticate
  }, async (request, reply) => {
    try {
      const { VideoModel } = await import('../models');
      
      // Get all videos from database
      const videos = await VideoModel.find({ deletedAt: null }).lean();
      
      // Sync to JSON storage
      await jsonStorageService.syncAllVideosFromDatabase(videos);
      
      const stats = await jsonStorageService.getStorageStats();
      
      return reply.code(200).send({
        success: true,
        message: `Successfully synced ${videos.length} videos to JSON storage`,
        data: stats
      });
    } catch (error) {
      console.error('Error syncing videos to JSON storage:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to sync videos to JSON storage'
      });
    }
  });

  // Download JSON file (authenticated)
  fastify.get('/json/download', {
    preHandler: authenticate
  }, async (request, reply) => {
    try {
      const videosData = await jsonStorageService.getAllVideos();
      const jsonData = {
        metadata: {
          totalVideos: videosData.length,
          exportedAt: new Date().toISOString(),
          version: '1.0.0'
        },
        videos: videosData
      };

      reply.header('Content-Type', 'application/json');
      reply.header('Content-Disposition', `attachment; filename="lawengaxe-videos-${new Date().toISOString().split('T')[0]}.json"`);
      
      return reply.code(200).send(JSON.stringify(jsonData, null, 2));
    } catch (error) {
      console.error('Error downloading JSON file:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to download JSON file'
      });
    }
  });
};

export default videoJsonRoutes;
