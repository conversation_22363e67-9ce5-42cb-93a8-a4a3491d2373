import { FastifyRequest, FastifyReply } from 'fastify';
import { aiAssistantService } from '../services/ai-assistant.service';
import { AuthenticatedUser } from '../types/user';

/**
 * AI Assistant controller for handling AI-related requests
 */
export class AIAssistantController {
  /**
   * Send a message to the AI Assistant
   */
  async sendMessage(
    request: FastifyRequest<{
      Body: {
        message: string;
        provider: string;
        apiKey: string;
        endpoint?: string;
        model?: string; // Added model parameter for providers that support multiple models
        conversationId?: string;
        creatorId: string;
        creatorName?: string; // Added creator name for personalized responses
        videoId?: string;
        chatHistory?: Array<{
          role: 'user' | 'assistant';
          content: string;
        }>;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      // Check authentication status
      const isAuthenticated = !!request.user;
      console.log('Authentication status:', isAuthenticated ? 'Authenticated' : 'Not authenticated');
      if (isAuthenticated) {
        console.log('Authenticated user:', (request.user as AuthenticatedUser).username || (request.user as AuthenticatedUser).email || 'Unknown user');
      }

      const { message, provider, apiKey, endpoint, model, conversationId, creatorId, creatorName, videoId, chatHistory } = request.body;

      // Log the request details for debugging (mask the API key for security)
      console.log('AI Assistant request:');
      console.log('- Provider:', provider);
      console.log('- Model:', model);
      console.log('- API Key (masked):', apiKey ? `${apiKey.substring(0, 5)}...${apiKey.substring(apiKey.length - 5)}` : 'none');
      console.log('- Endpoint:', endpoint);
      console.log('- Message:', message);
      console.log('- Creator ID:', creatorId);
      console.log('- Creator Name:', creatorName || 'Not provided');
      console.log('- Video ID:', videoId || 'Not provided');
      console.log('- Chat History Length:', chatHistory?.length || 0);

      // Validate required fields
      if (!message || !provider || !apiKey || !creatorId) {
        console.error('Missing required fields in AI Assistant request');
        return reply.code(400).send({
          success: false,
          message: 'Missing required fields'
        });
      }

      // Send message to AI Assistant service
      const response = await aiAssistantService.sendMessage({
        message,
        provider,
        apiKey,
        endpoint,
        model,
        conversationId,
        creatorId,
        creatorName, // Pass the creator name for personalized responses
        videoId,
        chatHistory
      });

      return reply.code(200).send({
        success: true,
        data: response
      });
    } catch (error: any) {
      console.error('Error in chat controller:', error);
      console.error('Error stack:', error.stack);

      // Check if we're in development mode
      const isDevelopment = process.env.NODE_ENV === 'development';

      // In development mode, provide a fallback response instead of an error
      if (isDevelopment) {
        console.log('Development mode: Providing fallback response for error');

        // Generate a fallback response
        return reply.code(200).send({
          success: true,
          data: {
            content: "I'm here to help! What would you like to know? (Note: This is a fallback response in development mode)",
            conversationId: `fallback-${Date.now()}`,
            timestamp: new Date().toISOString(),
            response: "I'm here to help! What would you like to know? (Note: This is a fallback response in development mode)"
          }
        });
      }

      // In production, handle specific error types
      if (error.statusCode === 401 || error.message?.includes('auth')) {
        // Authentication error
        console.log('Handling authentication error:', error.message);
        return reply.code(401).send({
          success: false,
          message: error.message || 'Authentication failed. Please check your API key.',
          errorType: 'auth_error'
        });
      } else if (error.statusCode === 403) {
        // Authorization error
        console.log('Handling authorization error:', error.message);
        return reply.code(403).send({
          success: false,
          message: error.message || 'Access denied. Your API key may not have permission to use this service.',
          errorType: 'permission_error'
        });
      } else if (error.statusCode === 429) {
        // Rate limit error
        console.log('Handling rate limit error:', error.message);
        return reply.code(429).send({
          success: false,
          message: error.message || 'Rate limit exceeded. Please try again later.',
          errorType: 'rate_limit_error'
        });
      } else if (error.message && error.message.includes('OpenRouter API')) {
        // Other OpenRouter-specific errors
        console.log('Handling OpenRouter API error:', error.message);
        return reply.code(400).send({
          success: false,
          message: error.message,
          errorType: 'openrouter_error'
        });
      }

      // Generic error fallback
      console.log('Handling generic error:', error.message);
      return reply.code(500).send({
        success: false,
        message: 'Failed to process chat message',
        errorDetails: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
}

export const aiAssistantController = new AIAssistantController();
