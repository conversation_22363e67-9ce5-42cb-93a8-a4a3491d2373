import fs from 'fs';
import path from 'path';
import { format } from 'date-fns';

/**
 * Logger utility for the application
 */
export class Logger {
  private logDir: string;
  private appName: string;

  /**
   * Create a new Logger
   * @param options Logger options
   */
  constructor(options: {
    logDir?: string;
    appName?: string;
  } = {}) {
    this.logDir = options.logDir || path.join(process.cwd(), 'logs');
    this.appName = options.appName || 'app';

    // Create log directory if it doesn't exist
    this.ensureLogDirectory();
  }

  /**
   * Ensure the log directory exists
   */
  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  /**
   * Log a message
   * @param level Log level
   * @param message Message to log
   * @param meta Additional metadata
   */
  public log(level: 'info' | 'warn' | 'error' | 'debug', message: string, meta?: any): void {
    const timestamp = new Date();
    const formattedDate = format(timestamp, 'yyyy-MM-dd');
    const formattedTime = format(timestamp, 'HH:mm:ss.SSS');

    // Create log entry
    const logEntry = {
      timestamp: timestamp.toISOString(),
      level,
      message,
      ...(meta && { meta }),
    };

    // Log to console
    const consoleMessage = `[${formattedTime}] ${level.toUpperCase()}: ${message}`;
    
    switch (level) {
      case 'info':
        console.info(consoleMessage);
        break;
      case 'warn':
        console.warn(consoleMessage);
        break;
      case 'error':
        console.error(consoleMessage);
        break;
      case 'debug':
        console.debug(consoleMessage);
        break;
    }

    if (meta) {
      console.log(meta);
    }

    // Log to file
    const logFilePath = path.join(this.logDir, `${this.appName}-${level}-${formattedDate}.log`);
    const logMessage = JSON.stringify(logEntry) + '\n';
    
    fs.appendFile(logFilePath, logMessage, (err) => {
      if (err) {
        console.error('Failed to write to log file:', err);
      }
    });
  }

  /**
   * Log an info message
   * @param message Message to log
   * @param meta Additional metadata
   */
  public info(message: string, meta?: any): void {
    this.log('info', message, meta);
  }

  /**
   * Log a warning message
   * @param message Message to log
   * @param meta Additional metadata
   */
  public warn(message: string, meta?: any): void {
    this.log('warn', message, meta);
  }

  /**
   * Log an error message
   * @param message Message to log
   * @param meta Additional metadata
   */
  public error(message: string, meta?: any): void {
    this.log('error', message, meta);
  }

  /**
   * Log a debug message
   * @param message Message to log
   * @param meta Additional metadata
   */
  public debug(message: string, meta?: any): void {
    this.log('debug', message, meta);
  }
}

// Create a singleton instance
export const logger = new Logger();
