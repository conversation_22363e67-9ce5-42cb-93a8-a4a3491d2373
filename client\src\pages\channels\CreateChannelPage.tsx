import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { Navigate, Link } from 'react-router-dom';
import CreateChannelForm from '@/components/channels/CreateChannelForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import Layout from '@/components/layout/Layout';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';

const CreateChannelPage: React.FC = () => {
  const { currentUser } = useAuth();

  // Redirect to login if not authenticated
  if (!currentUser) {
    return <Navigate to="/login?redirect=/create-channel" replace />;
  }

  return (
    <Layout>
      <div className="container py-8">
        <div className="max-w-3xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Create a Channel</h1>

        <Card>
          <CardHeader>
            <CardTitle>Channel Details</CardTitle>
            <CardDescription>
              Create your own channel to share videos and build a community
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CreateChannelForm />
          </CardContent>
        </Card>

        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Channel Guidelines</h2>
          <div className="bg-muted p-4 rounded-lg">
            <ul className="list-disc list-inside space-y-2 text-sm">
              <li>Choose a unique channel name that represents your content</li>
              <li>Write a clear description that helps viewers understand what your channel is about</li>
              <li>Add relevant tags to make your channel discoverable</li>
              <li>Follow our community guidelines when uploading content</li>
              <li>Engage with your audience by responding to comments</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    </Layout>
  );
};

export default CreateChannelPage;
