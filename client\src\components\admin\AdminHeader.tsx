import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import ProfileDropdown from './ProfileDropdown';
import ThemeSelector from './ThemeSelector';

export default function AdminHeader() {
  const { currentUser } = useAuth();
  const { theme, setTheme } = useTheme();

  return (
    <div className={`${theme === 'dark' ? 'bg-[#1A1A1A] border-gray-800' : 'bg-white border-gray-200'} border-b py-3 px-6 flex justify-between items-center`}>
      <div className="flex-1">
        <div className="relative max-w-md">
          <input
            type="text"
            placeholder="Search"
            className={`w-full bg-white border border-black text-black rounded-md py-2 pl-10 pr-4 focus:outline-none`}
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg className={`h-5 w-5 text-black`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <ThemeSelector />
        <ProfileDropdown />
      </div>
    </div>
  );
}
