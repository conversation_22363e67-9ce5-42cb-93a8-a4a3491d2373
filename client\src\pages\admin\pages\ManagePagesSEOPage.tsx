import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Home,
  Search,
  Globe,
  Edit,
  Calendar
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
// import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';

// Enhanced SEO page model
interface SEOPage {
  id: string;
  name: string;
  title: string;
  description: string;
  keywords: string;
  lastUpdated: string;
  status: 'optimized' | 'needs-review' | 'not-set';
}

// Mock data for SEO pages
const mockSeoPages: SEOPage[] = [
  {
    id: '1',
    name: '404',
    title: 'Page Not Found | Legal Aid',
    description: 'The page you are looking for does not exist or has been moved.',
    keywords: 'error, 404, page not found',
    lastUpdated: '2023-10-15T14:30:00Z',
    status: 'optimized'
  },
  {
    id: '2',
    name: 'ADS',
    title: 'Advertisements | Legal Aid',
    description: 'View and manage your advertisements on Legal Aid platform.',
    keywords: 'ads, advertisements, marketing',
    lastUpdated: '2023-09-22T11:15:00Z',
    status: 'optimized'
  },
  {
    id: '3',
    name: 'ADS ANALYTICS',
    title: 'Advertisement Analytics | Legal Aid',
    description: 'View detailed analytics for your advertisements.',
    keywords: 'ads analytics, advertisement performance, marketing metrics',
    lastUpdated: '2023-08-10T09:45:00Z',
    status: 'needs-review'
  },
  {
    id: '4',
    name: 'AGE BLOCK',
    title: 'Age Restriction | Legal Aid',
    description: 'This content is age-restricted.',
    keywords: 'age restriction, age block, content restriction',
    lastUpdated: '2023-11-05T16:20:00Z',
    status: 'optimized'
  },
  {
    id: '5',
    name: 'ARTICLES',
    title: 'Articles | Legal Aid',
    description: 'Browse legal articles and resources.',
    keywords: 'legal articles, resources, legal aid',
    lastUpdated: '2023-07-18T13:25:00Z',
    status: 'optimized'
  },
  {
    id: '6',
    name: 'COMMENTS',
    title: 'Comments | Legal Aid',
    description: 'View and manage comments on Legal Aid platform.',
    keywords: 'comments, discussion, feedback',
    lastUpdated: '2023-06-30T10:15:00Z',
    status: 'needs-review'
  },
  {
    id: '7',
    name: 'CONFIRM',
    title: 'Confirm Action | Legal Aid',
    description: 'Confirm your action on Legal Aid platform.',
    keywords: 'confirm, verification, action',
    lastUpdated: '2023-05-12T09:40:00Z',
    status: 'not-set'
  },
  {
    id: '8',
    name: 'CONTACT',
    title: 'Contact Us | Legal Aid',
    description: 'Get in touch with our team for support and inquiries.',
    keywords: 'contact, support, help, inquiries',
    lastUpdated: '2023-04-25T14:50:00Z',
    status: 'optimized'
  },
  {
    id: '9',
    name: 'CREATE ARTICLE',
    title: 'Create Article | Legal Aid',
    description: 'Create and publish legal articles on our platform.',
    keywords: 'create article, publish, content creation',
    lastUpdated: '2023-03-18T11:30:00Z',
    status: 'needs-review'
  },
  {
    id: '10',
    name: 'CREATE POST',
    title: 'Create Post | Legal Aid',
    description: 'Create and share posts with the Legal Aid community.',
    keywords: 'create post, share, community',
    lastUpdated: '2023-02-05T16:45:00Z',
    status: 'not-set'
  },
  {
    id: '11',
    name: 'DASHBOARD',
    title: 'Dashboard | Legal Aid',
    description: 'View your personalized dashboard on Legal Aid platform.',
    keywords: 'dashboard, overview, analytics',
    lastUpdated: '2023-01-20T13:15:00Z',
    status: 'optimized'
  },
  {
    id: '12',
    name: 'EDIT VIDEO',
    title: 'Edit Video | Legal Aid',
    description: 'Edit your videos on Legal Aid platform.',
    keywords: 'edit video, video editing, content creation',
    lastUpdated: '2023-12-10T10:30:00Z',
    status: 'needs-review'
  }
];

export default function ManagePagesSEOPage() {
  const [seoPages, setSeoPages] = useState<SEOPage[]>(mockSeoPages);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentSeoPage, setCurrentSeoPage] = useState<SEOPage | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const itemsPerPage = 6;

  // Filter pages based on search term and status
  const filteredPages = seoPages.filter(page => {
    const matchesSearch =
      page.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      page.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || page.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredPages.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = filteredPages.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle edit SEO
  const handleEditSeo = (page: SEOPage) => {
    setCurrentSeoPage(page);
    setShowEditDialog(true);
  };

  // Handle save SEO
  const handleSaveSeo = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!currentSeoPage) return;

    const formData = new FormData(e.currentTarget);

    const updatedPage: SEOPage = {
      ...currentSeoPage,
      title: formData.get('title') as string,
      description: formData.get('description') as string,
      keywords: formData.get('keywords') as string,
      lastUpdated: new Date().toISOString(),
      status: 'optimized'
    };

    setSeoPages(seoPages.map(page => page.id === currentSeoPage.id ? updatedPage : page));
    setShowEditDialog(false);
    setCurrentSeoPage(null);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'optimized':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Optimized</Badge>;
      case 'needs-review':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Needs Review</Badge>;
      case 'not-set':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-200">Not Set</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Manage Pages SEO</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Content</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Pages</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Manage Pages SEO</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Main Content */}
            <Card>
              <CardHeader>
                <CardTitle>SEO Management</CardTitle>
                <CardDescription>
                  Optimize your website's SEO settings for better search engine visibility
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex-1 relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search pages..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant={statusFilter === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStatusFilter('all')}
                    >
                      All
                    </Button>
                    <Button
                      variant={statusFilter === 'optimized' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStatusFilter('optimized')}
                      className="bg-green-100 text-green-800 hover:bg-green-200 border-green-200"
                    >
                      Optimized
                    </Button>
                    <Button
                      variant={statusFilter === 'needs-review' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStatusFilter('needs-review')}
                      className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-200"
                    >
                      Needs Review
                    </Button>
                    <Button
                      variant={statusFilter === 'not-set' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setStatusFilter('not-set')}
                      className="bg-gray-100 text-gray-800 hover:bg-gray-200 border-gray-200"
                    >
                      Not Set
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {currentItems.map((page) => (
                    <Card key={page.id} className="overflow-hidden">
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-base">{page.name}</CardTitle>
                            <CardDescription className="text-xs truncate max-w-[250px]">
                              {page.title}
                            </CardDescription>
                          </div>
                          {getStatusBadge(page.status)}
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-2">
                        <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                          {page.description || 'No description set'}
                        </p>
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3 mr-1" />
                          Last updated: {formatDate(page.lastUpdated)}
                        </div>
                      </CardContent>
                      <CardFooter className="p-4 pt-0 flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                          onClick={() => handleEditSeo(page)}
                        >
                          <Edit className="h-3.5 w-3.5 mr-1" /> Edit SEO
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>

                {filteredPages.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No pages found matching your search.
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-6">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              if (currentPage > 1) handlePageChange(currentPage - 1);
                            }}
                            className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
                          />
                        </PaginationItem>

                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                handlePageChange(page);
                              }}
                              isActive={currentPage === page}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}

                        <PaginationItem>
                          <PaginationNext
                            href="#"
                            onClick={(e) => {
                              e.preventDefault();
                              if (currentPage < totalPages) handlePageChange(currentPage + 1);
                            }}
                            className={currentPage === totalPages ? 'pointer-events-none opacity-50' : ''}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <div className="text-sm text-muted-foreground">
                  Showing {currentItems.length} of {filteredPages.length} pages
                </div>
                <Button variant="outline" size="sm">
                  <Globe className="h-4 w-4 mr-2" /> Generate Sitemap
                </Button>
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>

      {/* Edit SEO Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit SEO for {currentSeoPage?.name}</DialogTitle>
            <DialogDescription>
              Optimize your page's SEO settings for better search engine visibility.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSaveSeo}>
            <Tabs defaultValue="basic">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="basic">Basic SEO</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>
              <TabsContent value="basic" className="space-y-4 py-4">
                <div className="grid gap-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="title" className="text-right">
                      Page Title
                    </Label>
                    <Input
                      id="title"
                      name="title"
                      defaultValue={currentSeoPage?.title || ''}
                      className="col-span-3"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="description" className="text-right pt-2">
                      Meta Description
                    </Label>
                    <Textarea
                      id="description"
                      name="description"
                      defaultValue={currentSeoPage?.description || ''}
                      className="col-span-3 min-h-[100px]"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="keywords" className="text-right pt-2">
                      Keywords
                    </Label>
                    <Textarea
                      id="keywords"
                      name="keywords"
                      placeholder="Comma-separated keywords"
                      defaultValue={currentSeoPage?.keywords || ''}
                      className="col-span-3"
                    />
                  </div>
                </div>
              </TabsContent>
              <TabsContent value="advanced" className="space-y-4 py-4">
                <div className="grid gap-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="canonical" className="text-right">
                      Canonical URL
                    </Label>
                    <Input
                      id="canonical"
                      name="canonical"
                      placeholder="https://example.com/page"
                      className="col-span-3"
                    />
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="robots" className="text-right">
                      Robots
                    </Label>
                    <select
                      id="robots"
                      name="robots"
                      className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="index,follow">index, follow</option>
                      <option value="noindex,follow">noindex, follow</option>
                      <option value="index,nofollow">index, nofollow</option>
                      <option value="noindex,nofollow">noindex, nofollow</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="og-title" className="text-right">
                      OG Title
                    </Label>
                    <Input
                      id="og-title"
                      name="og-title"
                      placeholder="Open Graph Title"
                      className="col-span-3"
                    />
                  </div>

                  <div className="grid grid-cols-4 items-start gap-4">
                    <Label htmlFor="og-description" className="text-right pt-2">
                      OG Description
                    </Label>
                    <Textarea
                      id="og-description"
                      name="og-description"
                      placeholder="Open Graph Description"
                      className="col-span-3"
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter className="mt-6">
              <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Save SEO Settings
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
