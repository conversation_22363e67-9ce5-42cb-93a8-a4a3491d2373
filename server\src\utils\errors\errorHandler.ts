import { FastifyError, FastifyReply, FastifyRequest } from 'fastify';
import { AppError } from './AppError';
import { ErrorCodes } from './errorCodes';
import mongoose from 'mongoose';
import { TypeCompiler } from '@sinclair/typebox/compiler';
import { handleDatabaseError, handleValidationError, handleDuplicateKeyError, handleCastError } from './databaseErrorHandler';

/**
 * Central error handler for the application
 * Handles different types of errors and formats them consistently
 */
export function errorHandler(
  error: Error | FastifyError | AppError,
  request: FastifyRequest,
  reply: FastifyReply
) {
  // Log the error
  request.log.error({
    error: {
      message: error.message,
      stack: error.stack,
      name: error.name,
      ...(error instanceof AppError && {
        statusCode: error.statusCode,
        errorCode: error.errorCode,
        isOperational: error.isOperational,
        data: error.data,
      }),
    },
    request: {
      method: request.method,
      url: request.url,
      params: request.params,
      query: request.query,
      headers: request.headers,
      body: request.body,
    },
  }, 'Error occurred');

  // Handle AppError (our custom error)
  if (error instanceof AppError) {
    return reply.code(error.statusCode).send({
      success: false,
      message: error.message,
      error: {
        message: error.message,
        code: error.errorCode,
        ...(error.data && { data: error.data }),
      },
    });
  }

  // Handle Fastify validation errors
  if ((error as any).validation) {
    const validationErrors = Array.isArray((error as any).validation)
      ? (error as any).validation
      : [(error as any).validation];

    return reply.code(400).send({
      success: false,
      message: 'Validation error',
      error: {
        message: 'Validation error',
        code: ErrorCodes.VALIDATION_ERROR,
        data: validationErrors.map((err: any) => ({
          field: err.params?.missingProperty || err.dataPath?.substring(1) || '',
          message: err.message,
        })),
      },
    });
  }

  // Handle TypeBox validation errors
  if (error.name === 'ValidationError' || error.message === 'Validation failed' || error.message.includes('validation')) {
    // Check if we have detailed validation errors
    const validationErrors = (error as any).validationErrors || [];

    // Extract validation details from the error
    let errorDetails = validationErrors.length > 0 ? validationErrors : error.message;

    // Try to extract more detailed information if available
    if ((error as any).validation) {
      const validation = (error as any).validation;
      errorDetails = Array.isArray(validation)
        ? validation.map((v: any) => ({
            field: v.params?.missingProperty || v.dataPath?.substring(1) || '',
            message: v.message,
          }))
        : [{
            field: validation.params?.missingProperty || validation.dataPath?.substring(1) || '',
            message: validation.message,
          }];
    }

    console.log('Validation error details:', JSON.stringify(errorDetails, null, 2));
    console.log('Original error:', error);

    return reply.code(400).send({
      success: false,
      message: 'Validation error',
      error: {
        code: ErrorCodes.VALIDATION_ERROR,
        message: 'One or more fields failed validation',
        data: errorDetails,
      },
    });
  }

  // Handle Mongoose/MongoDB errors
  if (error instanceof mongoose.Error.ValidationError) {
    const appError = handleValidationError(error);
    return reply.code(appError.statusCode).send({
      success: false,
      message: appError.message,
      error: {
        message: appError.message,
        code: appError.errorCode,
        data: appError.data,
      },
    });
  }

  // Handle Mongoose duplicate key errors
  if (error.name === 'MongoError' && (error as any).code === 11000) {
    const appError = handleDuplicateKeyError(error as any);
    return reply.code(appError.statusCode).send({
      success: false,
      message: appError.message,
      error: {
        message: appError.message,
        code: appError.errorCode,
        data: appError.data,
      },
    });
  }

  // Handle Mongoose cast errors
  if (error instanceof mongoose.Error.CastError) {
    const appError = handleCastError(error);
    return reply.code(appError.statusCode).send({
      success: false,
      message: appError.message,
      error: {
        message: appError.message,
        code: appError.errorCode,
        data: appError.data,
      },
    });
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    return reply.code(401).send({
      success: false,
      message: 'Invalid token',
      error: {
        message: 'Invalid token',
        code: ErrorCodes.TOKEN_INVALID,
      },
    });
  }

  if (error.name === 'TokenExpiredError') {
    return reply.code(401).send({
      success: false,
      message: 'Token expired',
      error: {
        message: 'Token expired',
        code: ErrorCodes.TOKEN_EXPIRED,
      },
    });
  }

  // Handle other errors
  // In production, don't expose the error details
  const isDev = process.env.NODE_ENV === 'development';

  return reply.code(500).send({
    success: false,
    message: isDev ? error.message : 'Internal server error',
    error: {
      message: isDev ? error.message : 'Internal server error',
      code: ErrorCodes.INTERNAL_SERVER_ERROR,
      ...(isDev && { stack: error.stack }),
    },
  });
}
