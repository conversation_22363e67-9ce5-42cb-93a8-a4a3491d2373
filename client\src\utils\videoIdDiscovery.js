/**
 * Utility for discovering working Engaxe IDs for videos
 * This helps build the mapping table automatically
 */
import { addMapping, isValidEngaxeId } from './videoIdConverter';

/**
 * Check if an Engaxe ID works by trying to load it in an iframe
 * @param {string} engaxeId - The Engaxe ID to check
 * @returns {Promise<boolean>} - Whether the ID works
 */
export const checkEngaxeId = (engaxeId) => {
  return new Promise((resolve) => {
    // Create a hidden iframe to test the ID
    const iframe = document.createElement('iframe');
    iframe.style.position = 'absolute';
    iframe.style.left = '-9999px';
    iframe.style.top = '-9999px';
    iframe.style.width = '1px';
    iframe.style.height = '1px';
    iframe.src = `https://engaxe.com/e/${engaxeId}`;
    
    // Set a timeout to consider the ID invalid if it takes too long
    const timeout = setTimeout(() => {
      document.body.removeChild(iframe);
      console.log(`Engaxe ID ${engaxeId} timed out, considering invalid`);
      resolve(false);
    }, 5000);
    
    // If the iframe loads successfully, consider the ID valid
    iframe.onload = () => {
      clearTimeout(timeout);
      document.body.removeChild(iframe);
      console.log(`Engaxe ID ${engaxeId} loaded successfully, considering valid`);
      resolve(true);
    };
    
    // If the iframe fails to load, consider the ID invalid
    iframe.onerror = () => {
      clearTimeout(timeout);
      document.body.removeChild(iframe);
      console.log(`Engaxe ID ${engaxeId} failed to load, considering invalid`);
      resolve(false);
    };
    
    // Add the iframe to the document
    document.body.appendChild(iframe);
  });
};

/**
 * Try to discover a working Engaxe ID for a hash ID
 * @param {string} hashId - The hash ID to discover an Engaxe ID for
 * @returns {Promise<string|null>} - The discovered Engaxe ID, or null if none found
 */
export const discoverEngaxeId = async (hashId) => {
  console.log(`Attempting to discover Engaxe ID for hash ID ${hashId}`);
  
  // Try some common patterns based on the hash ID
  const patterns = [
    // Try using the first 6 characters of the hash ID
    hashId.substring(0, 6),
    // Try using the last 6 characters of the hash ID
    hashId.substring(hashId.length - 6),
    // Try using characters from the middle of the hash ID
    hashId.substring(Math.floor(hashId.length / 2) - 3, Math.floor(hashId.length / 2) + 3),
  ];
  
  // Try each pattern
  for (const pattern of patterns) {
    // Only try if the pattern is a valid Engaxe ID
    if (isValidEngaxeId(pattern)) {
      console.log(`Trying pattern ${pattern} for hash ID ${hashId}`);
      const works = await checkEngaxeId(pattern);
      
      if (works) {
        console.log(`Pattern ${pattern} works for hash ID ${hashId}`);
        // Add the mapping
        addMapping(hashId, pattern);
        return pattern;
      }
    }
  }
  
  // If no patterns work, return null
  console.log(`No patterns worked for hash ID ${hashId}`);
  return null;
};

/**
 * Try to discover working Engaxe IDs for all videos in an array
 * @param {Array} videos - The array of videos to discover Engaxe IDs for
 * @returns {Promise<void>}
 */
export const discoverEngaxeIdsForVideos = async (videos) => {
  if (!videos || !Array.isArray(videos)) return;
  
  console.log(`Discovering Engaxe IDs for ${videos.length} videos`);
  
  // Process videos one by one to avoid overwhelming the browser
  for (const video of videos) {
    if (video.id && !video.url) {
      console.log(`Video ${video.id} has no URL, attempting to discover one`);
      const engaxeId = await discoverEngaxeId(video.id);
      
      if (engaxeId) {
        console.log(`Discovered Engaxe ID ${engaxeId} for video ${video.id}`);
        video.url = engaxeId;
      }
    }
  }
};
