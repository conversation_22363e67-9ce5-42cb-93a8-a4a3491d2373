import React from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Upload, Youtube, Play, FileVideo, Home, ChevronRight, Database } from 'lucide-react';

export default function ImportVideosPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex overflow-hidden">
      <AdminSidebar />

      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />

        <div className="flex-1 bg-gray-100 p-6 overflow-y-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-800">Import Videos</h1>
            <p className="text-gray-600">Import videos from various sources</p>
          </div>

          {/* Breadcrumb */}
          <div className="flex items-center text-sm mb-6">
            <Link to="/admin" className="text-blue-600 hover:underline flex items-center">
              <Home size={14} className="mr-1" />
              Admin Panel
            </Link>
            <ChevronRight size={14} className="mx-2 text-gray-500" />
            <Link to="/admin/videos" className="text-blue-600 hover:underline">
              Videos
            </Link>
            <ChevronRight size={14} className="mx-2 text-gray-500" />
            <span className="text-gray-600">Import Videos</span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Upload from Computer */}
            <div className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center justify-center text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Upload className="text-blue-600" size={24} />
              </div>
              <h2 className="text-lg font-medium text-gray-800 mb-2">Upload from Computer</h2>
              <p className="text-sm text-gray-600 mb-4">Upload video files directly from your computer</p>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                <Upload size={16} className="mr-2" />
                Upload Files
              </button>
            </div>



            {/* Import from Dailymotion */}
            <div
              className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center justify-center text-center cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/admin/videos/import/dailymotion')}
            >
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <Play className="text-blue-600" size={24} />
              </div>
              <h2 className="text-lg font-medium text-gray-800 mb-2">Import from Dailymotion</h2>
              <p className="text-sm text-gray-600 mb-4">Import videos from Dailymotion by URL or channel</p>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                <Play size={16} className="mr-2" />
                Import from Dailymotion
              </button>
            </div>

            {/* Import from Twitch */}
            <div
              className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center justify-center text-center cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/admin/videos/import/twitch')}
            >
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <FileVideo className="text-purple-600" size={24} />
              </div>
              <h2 className="text-lg font-medium text-gray-800 mb-2">Import from Twitch</h2>
              <p className="text-sm text-gray-600 mb-4">Import videos and streams from Twitch</p>
              <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 flex items-center">
                <FileVideo size={16} className="mr-2" />
                Import from Twitch
              </button>
            </div>

            {/* Import from Engaxe */}
            <div
              className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center justify-center text-center cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => navigate('/admin/videos/import/engaxe')}
            >
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <Database className="text-green-600" size={24} />
              </div>
              <h2 className="text-lg font-medium text-gray-800 mb-2">Import from LegalAid</h2>
              <p className="text-sm text-gray-600 mb-4">Import videos from LegalAid platform</p>
              <button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center">
                <Database size={16} className="mr-2" />
                Import from LegalAid
              </button>
            </div>
          </div>

          <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">Recent Imports</h2>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Video
                    </th>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Source
                    </th>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-16 bg-gray-200 rounded flex items-center justify-center text-gray-500">
                          <Play size={16} />
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">Getting Started with React</div>
                          <div className="text-xs text-gray-500">Duration: 12:45</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      Engaxe
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      2023-05-15
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Completed
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-16 bg-gray-200 rounded flex items-center justify-center text-gray-500">
                          <Play size={16} />
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">Advanced CSS Techniques</div>
                          <div className="text-xs text-gray-500">Duration: 18:30</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      Dailymotion
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      2023-05-20
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Completed
                      </span>
                    </td>
                  </tr>
                  <tr className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="h-10 w-16 bg-gray-200 rounded flex items-center justify-center text-gray-500">
                          <Play size={16} />
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">JavaScript for Beginners</div>
                          <div className="text-xs text-gray-500">Duration: 22:15</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      Twitch
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      2023-05-22
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                        Completed
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
