import mongoose, { Schema, Document } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

/**
 * Base interface for all entities in the system
 */
export interface IBaseEntity extends Document {
  /** Unique identifier for the entity, typically a UUID */
  id: string;

  /** Timestamp when the entity was created */
  createdAt: Date;

  /** Timestamp when the entity was last updated */
  updatedAt: Date;

  /**
   * Timestamp when the entity was soft deleted
   * When set, the entity is considered deleted but data is preserved in the database
   */
  deletedAt?: Date;

  /**
   * Version number for optimistic concurrency control
   * Incremented on each update to prevent conflicts when multiple users edit simultaneously
   */
  version: number;

  /**
   * Flexible storage for additional data that doesn't fit the schema
   * Allows for extensibility without schema changes
   */
  metadata: Record<string, any>;

  /** User ID of the person who created this entity - for audit trail */
  createdBy: string;

  /** User ID of the person who last updated this entity - for audit trail */
  updatedBy: string;

  /** User ID of the person who soft deleted this entity - for audit trail */
  deletedBy?: string;
}

/**
 * Base schema for all entities in the system
 * This schema is extended by all other schemas
 */
export const BaseSchema = new Schema(
  {
    id: {
      type: String,
      default: uuidv4,
      required: true,
      unique: true,
      index: true,
    },
    version: {
      type: Number,
      default: 1,
      required: true,
    },
    metadata: {
      type: Schema.Types.Mixed,
      default: {},
    },
    createdBy: {
      type: String,
      required: true,
      default: 'system', // Default to 'system' if not provided
    },
    updatedBy: {
      type: String,
      required: true,
      default: 'system', // Default to 'system' if not provided
    },
    deletedBy: {
      type: String,
    },
    deletedAt: {
      type: Date,
    },
  },
  {
    timestamps: true,
    toJSON: {
      virtuals: true,
      transform: (_, ret) => {
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      virtuals: true,
      transform: (_, ret) => {
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Add a compound index for soft delete functionality
BaseSchema.index({ id: 1, deletedAt: 1 });

// Add middleware to handle version increments on updates
BaseSchema.pre('findOneAndUpdate', function (next) {
  // @ts-ignore
  this.update({}, { $inc: { version: 1 } });
  next();
});

export default BaseSchema;
