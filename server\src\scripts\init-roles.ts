import mongoose from 'mongoose';
import { connectDB } from '../config/database';
import { RoleModel, PermissionModel } from '../models';
import { logger } from '../utils/logger';

/**
 * Initialize default roles and permissions
 */
async function initRoles() {
  try {
    // Connect to database
    await connectDB();
    logger.info('Connected to database');

    // Check if roles already exist
    const existingRoles = await RoleModel.find({});
    if (existingRoles.length > 0) {
      logger.info(`Found ${existingRoles.length} existing roles. Skipping initialization.`);
      return;
    }

    // Create default roles
    const roles = [
      {
        name: 'Administrator',
        description: 'Full system access with all permissions',
        code: 'admin',
        permissions: ['*'], // Wildcard for all permissions
        isSystem: true,
        isActive: true,
        priority: 0,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Moderator',
        description: 'Content moderation capabilities',
        code: 'moderator',
        permissions: [
          'content:read',
          'content:update',
          'content:delete',
          'comment:read',
          'comment:update',
          'comment:delete',
          'user:read',
        ],
        isSystem: true,
        isActive: true,
        priority: 10,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Creator',
        description: 'Content creation capabilities',
        code: 'creator',
        permissions: [
          'content:read',
          'content:create',
          'content:update',
          'content:delete',
          'comment:read',
          'comment:create',
          'comment:update',
          'comment:delete',
        ],
        isSystem: true,
        isActive: true,
        priority: 20,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'User',
        description: 'Regular user with basic permissions',
        code: 'user',
        permissions: [
          'content:read',
          'comment:read',
          'comment:create',
          'profile:read',
          'profile:update',
        ],
        isSystem: true,
        isActive: true,
        priority: 100,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
    ];

    // Insert roles
    await RoleModel.insertMany(roles);
    logger.info(`Created ${roles.length} default roles`);

    // Disconnect from database
    await mongoose.disconnect();
    logger.info('Disconnected from database');
    
    console.log('Default roles initialized successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error initializing roles:', error);
    process.exit(1);
  }
}

// Run the initialization
initRoles();
