import { FastifyInstance, FastifyPluginOptions, FastifyRequest } from 'fastify';
import { Type } from '@sinclair/typebox';
import userService from '../services/user.service';
import { createUnauthorizedError, ErrorCodes } from '../utils/errors';

// Extend FastifyInstance to include hasPermission method
declare module 'fastify' {
  interface FastifyInstance {
    hasPermission: (userId: string, permission: string) => Promise<boolean>;
  }
}

// Define query interface for login page
interface LoginQuerystring {
  error?: string;
}

/**
 * Documentation routes
 */
export default async function docsRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Login page for documentation
  fastify.get('/login-docs', async (request: FastifyRequest<{ Querystring: LoginQuerystring }>, reply) => {
    // Simple HTML login form
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>LawEngaxe API Documentation - Login</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              background-color: #f5f5f5;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 100vh;
              margin: 0;
            }
            .login-container {
              background-color: white;
              padding: 2rem;
              border-radius: 5px;
              box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
              width: 350px;
            }
            h1 {
              margin-top: 0;
              color: #333;
              text-align: center;
            }
            .form-group {
              margin-bottom: 1rem;
            }
            label {
              display: block;
              margin-bottom: 0.5rem;
              font-weight: bold;
            }
            input {
              width: 100%;
              padding: 0.5rem;
              border: 1px solid #ddd;
              border-radius: 3px;
              font-size: 1rem;
            }
            button {
              background-color: #4CAF50;
              color: white;
              border: none;
              padding: 0.75rem 1rem;
              border-radius: 3px;
              cursor: pointer;
              font-size: 1rem;
              width: 100%;
            }
            button:hover {
              background-color: #45a049;
            }
            .error {
              color: red;
              margin-bottom: 1rem;
            }
          </style>
        </head>
        <body>
          <div class="login-container">
            <h1>API Documentation</h1>
            <p>Please log in to access the API documentation.</p>
            ${request.query.error ? `<div class="error">${request.query.error}</div>` : ''}
            <form action="/api/v1/auth/login-docs" method="post">
              <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" required>
              </div>
              <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required>
              </div>
              <button type="submit">Login</button>
            </form>
          </div>
        </body>
      </html>
    `;

    reply.type('text/html').send(html);
  });

  // Login endpoint for documentation
  fastify.post('/login-docs', {
    schema: {
      body: Type.Object({
        email: Type.String({ format: 'email' }),
        password: Type.String(),
      }),
    },
  }, async (request, reply) => {
    try {
      const { email, password } = request.body as { email: string; password: string };

      // Authenticate user
      const { user, isValid } = await userService.loginUser(email, password);

      if (!user || !isValid) {
        return reply.redirect('/api/v1/auth/login-docs?error=Invalid email or password');
      }

      // Check if user has permission to access documentation
      const hasPermission = await request.server.hasPermission(user.id, 'docs:read');

      if (!hasPermission) {
        return reply.redirect('/api/v1/auth/login-docs?error=You do not have permission to access the documentation');
      }

      // Generate token
      const token = fastify.jwt.sign({
        id: user.id,
        email: user.email,
        roles: user.roles,
      });

      // Set cookie with token
      reply.setCookie('documentation_token', token, {
        path: '/',
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 60 * 60, // 1 hour
      });

      // Redirect to documentation
      return reply.redirect('/documentation');
    } catch (error: any) {
      return reply.redirect(`/api/v1/auth/login-docs?error=${encodeURIComponent(error.message)}`);
    }
  });
}
