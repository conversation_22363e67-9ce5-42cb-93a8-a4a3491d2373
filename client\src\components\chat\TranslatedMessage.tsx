import React, { useState, useEffect } from 'react';
import { useTranslation } from '@/context/TranslationContext';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Languages, RotateCcw } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface TranslatedMessageProps {
  message: string;
  sourceLanguage?: string;
  className?: string;
  showOriginal?: boolean;
}

/**
 * TranslatedMessage component
 *
 * This component displays a message with automatic translation based on the user's
 * selected language preference. It also provides options to view the original message
 * and to retry translation if needed.
 */
export default function TranslatedMessage({
  message,
  sourceLanguage = 'en',
  className = '',
  showOriginal = false
}: TranslatedMessageProps) {
  const {
    isTranslationEnabled,
    translateText,
    currentLanguage
  } = useTranslation();

  const [translatedMessage, setTranslatedMessage] = useState<string>('');
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const [showOriginalText, setShowOriginalText] = useState<boolean>(showOriginal);
  const [translationError, setTranslationError] = useState<boolean>(false);
  const [errorDetails, setErrorDetails] = useState<string>('');

  // Translate the message when it changes or when the language changes
  useEffect(() => {
    async function performTranslation() {
      // Don't translate if translation is disabled or if the message is empty
      if (!isTranslationEnabled || !message || currentLanguage === sourceLanguage) {
        setTranslatedMessage(message);
        return;
      }

      try {
        setIsTranslating(true);
        setTranslationError(false);

        const translated = await translateText(message, currentLanguage, sourceLanguage);

        // Check if the translation failed - now handles both simple and detailed error formats
        if (translated.includes('[Translation Error')) {
          console.log('Translation error detected:', translated);
          setTranslationError(true);
          setTranslatedMessage(message);

          // Extract error details from the error message
          const errorMatch = translated.match(/\[Translation Error[^\]]*\]/);
          if (errorMatch) {
            setErrorDetails(errorMatch[0]);
          } else {
            setErrorDetails('[Translation Error]');
          }
        } else {
          setTranslatedMessage(translated);
          setErrorDetails('');
        }
      } catch (error) {
        console.error('Error translating message:', error);
        setTranslationError(true);
        setTranslatedMessage(message);
        setErrorDetails(error instanceof Error ? `[Translation Error: ${error.message}]` : '[Translation Error: Unknown error]');
      } finally {
        setIsTranslating(false);
      }
    }

    performTranslation();
  }, [message, currentLanguage, isTranslationEnabled, sourceLanguage, translateText]);

  // Handle retry translation
  const handleRetryTranslation = async () => {
    try {
      setIsTranslating(true);
      setTranslationError(false);

      const translated = await translateText(message, currentLanguage, sourceLanguage);

      if (translated.includes('[Translation Error')) {
        console.log('Translation error detected during retry:', translated);
        setTranslationError(true);
        setTranslatedMessage(message);

        // Extract error details from the error message
        const errorMatch = translated.match(/\[Translation Error[^\]]*\]/);
        if (errorMatch) {
          setErrorDetails(errorMatch[0]);
        } else {
          setErrorDetails('[Translation Error]');
        }
      } else {
        setTranslatedMessage(translated);
        setErrorDetails('');
      }
    } catch (error) {
      console.error('Error retrying translation:', error);
      setTranslationError(true);
      setTranslatedMessage(message);
      setErrorDetails(error instanceof Error ? `[Translation Error: ${error.message}]` : '[Translation Error: Unknown error]');
    } finally {
      setIsTranslating(false);
    }
  };

  // Toggle between original and translated text
  const toggleOriginalText = () => {
    setShowOriginalText(!showOriginalText);
  };

  // If translation is disabled or source language is the same as target language
  if (!isTranslationEnabled || currentLanguage === sourceLanguage) {
    return <span className={className}>{message}</span>;
  }

  // If still translating, show a skeleton loader
  if (isTranslating) {
    return (
      <div className={`${className} space-y-1`}>
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    );
  }

  // If showing original text or there was a translation error
  if (showOriginalText || translationError) {
    return (
      <div className={`${className} space-y-1`}>
        <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
          {translationError ? (
            <div>
              <span className="text-red-500">Translation failed</span>
              {errorDetails && (
                <span className="text-red-400 text-xs ml-1 max-w-[200px] truncate" title={errorDetails}>
                  ({errorDetails})
                </span>
              )}
            </div>
          ) : (
            <span>Original text</span>
          )}

          <div className="flex gap-1">
            {!showOriginalText && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5"
                      onClick={toggleOriginalText}
                    >
                      <Languages className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Show translated text</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}

            {translationError && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5"
                      onClick={handleRetryTranslation}
                    >
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Retry translation</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
        <span>{message}</span>
      </div>
    );
  }

  // Show translated text with option to view original
  return (
    <div className={`${className} space-y-1`}>
      <div className="flex items-center gap-2 text-xs text-muted-foreground mb-1">
        <span>Translated from {sourceLanguage === 'en' ? 'English' : sourceLanguage}</span>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5"
                onClick={toggleOriginalText}
              >
                <Languages className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Show original text</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <span>{translatedMessage}</span>
    </div>
  );
}
