import { useWatchlist } from '@/context/WatchlistContext';
import { useLanguage } from '@/context/LanguageContext';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, Trash2, Play } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function Watchlist() {
  const { watchlist, removeFromWatchlist, clearWatchlist } = useWatchlist();
  const { t } = useLanguage();

  if (watchlist.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            {t('profile.watchlist')}
          </CardTitle>
          <CardDescription>
            {t('profile.watchlist_empty')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-lingstream-muted">{t('profile.watchlist_empty_desc')}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Clock className="h-5 w-5" />
          {t('profile.watchlist')}
        </h2>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={clearWatchlist}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          {t('profile.clear_watchlist')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {watchlist.map((video) => (
          <Card key={video.id} className="overflow-hidden bg-lingstream-card border-gray-700">
            <div className="relative aspect-video">
              <img 
                src={video.thumbnail} 
                alt={video.title} 
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                <Link to={`/video/${video.id}`}>
                  <Button size="icon" variant="secondary" className="rounded-full">
                    <Play className="h-6 w-6" />
                  </Button>
                </Link>
              </div>
              <div className="absolute bottom-2 right-2 bg-black/70 px-2 py-1 rounded text-xs">
                {video.duration}
              </div>
            </div>
            
            <CardContent className="p-4">
              <div className="flex justify-between">
                <Link to={`/video/${video.id}`} className="font-medium line-clamp-2 hover:text-lingstream-accent">
                  {video.title}
                </Link>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-8 w-8 text-destructive hover:text-destructive"
                  onClick={() => removeFromWatchlist(video.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center mt-2 text-sm text-muted-foreground">
                <div className="flex items-center">
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src={video.creator.avatar} />
                    <AvatarFallback>{video.creator.name[0]}</AvatarFallback>
                  </Avatar>
                  <span>{video.creator.name}</span>
                </div>
                <div className="flex items-center ml-auto">
                  <span>{video.views} {t('video.views')}</span>
                </div>
              </div>
              
              <div className="mt-2 text-xs text-muted-foreground">
                {t('profile.added')} {formatDistanceToNow(new Date(video.addedAt), { addSuffix: true })}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
