import { FastifyRequest, FastifyReply } from 'fastify';
import { bhashiniApiService } from '../services/bhashini-api.service';

/**
 * Translation controller for handling translation-related requests
 */
export class TranslationController {
  /**
   * Translate text using Bhashini API
   */
  async translateText(
    request: FastifyRequest<{
      Body: {
        text: string;
        sourceLanguage: string;
        targetLanguage: string;
        userId?: string;
        ulcaApiKey?: string;
        usePipeline?: boolean;
        method?: 'bhashini' | 'google' | 'best';
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const {
        text,
        sourceLanguage,
        targetLanguage,
        userId,
        ulcaApiKey,
        usePipeline = true,
        method = 'best'
      } = request.body;

      // Use default userId if not provided
      const bhashiniUserId = userId || 'cee60134c6bb4d179efd3fda48ff32fe';
      const bhashiniUlcaApiKey = ulcaApiKey || '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

      // Translate the text
      let translatedText;

      switch (method) {
        case 'google':
          // Use Google Translate API
          translatedText = await bhashiniApiService.translateTextWithGoogle(
            text,
            sourceLanguage,
            targetLanguage
          );
          break;

        case 'bhashini':
          // Use Bhashini API
          if (usePipeline) {
            // Use the pipeline approach
            translatedText = await bhashiniApiService.translateTextWithPipeline(
              text,
              sourceLanguage,
              targetLanguage,
              bhashiniUserId,
              bhashiniUlcaApiKey
            );
          } else {
            // Use the direct translation approach
            translatedText = await bhashiniApiService.translateText(
              text,
              sourceLanguage,
              targetLanguage,
              bhashiniUserId,
              bhashiniUlcaApiKey
            );
          }
          break;

        case 'best':
        default:
          // Use the best available method
          translatedText = await bhashiniApiService.translateTextBest(
            text,
            sourceLanguage,
            targetLanguage,
            bhashiniUserId,
            bhashiniUlcaApiKey
          );
          break;
      }

      return reply.code(200).send({
        success: true,
        data: {
          translatedText,
          sourceLanguage,
          targetLanguage,
        },
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to translate text',
        error: 'TRANSLATION_ERROR',
      });
    }
  }

  /**
   * Detect language of text using Bhashini API
   */
  async detectLanguage(
    request: FastifyRequest<{
      Body: {
        text: string;
        userId?: string;
        ulcaApiKey?: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { text, userId, ulcaApiKey } = request.body;

      // Use default userId if not provided
      const bhashiniUserId = userId || 'cee60134c6bb4d179efd3fda48ff32fe';

      // For now, we'll use a simple language detection approach
      // The current Bhashini API doesn't have a dedicated language detection endpoint
      // We'll implement a basic detection based on character sets

      // Simple language detection function
      const detectLanguageSimple = (text: string): { detectedLanguage: string; confidence: number } => {
        // Define character ranges for different languages
        const langPatterns = {
          hi: /[\u0900-\u097F]/g,  // Hindi
          mr: /[\u0900-\u097F]/g,  // Marathi (shares Devanagari script with Hindi)
          gu: /[\u0A80-\u0AFF]/g,  // Gujarati
          ta: /[\u0B80-\u0BFF]/g,  // Tamil
          te: /[\u0C00-\u0C7F]/g,  // Telugu
          bn: /[\u0980-\u09FF]/g,  // Bengali
          kn: /[\u0C80-\u0CFF]/g,  // Kannada
          ml: /[\u0D00-\u0D7F]/g,  // Malayalam
          pa: /[\u0A00-\u0A7F]/g,  // Punjabi
          or: /[\u0B00-\u0B7F]/g,  // Odia
          en: /[a-zA-Z]/g,         // English
        };

        // Count characters for each language
        const counts: Record<string, number> = {};
        for (const [lang, pattern] of Object.entries(langPatterns)) {
          const matches = text.match(pattern);
          counts[lang] = matches ? matches.length : 0;
        }

        // Find the language with the most matches
        let maxLang = 'en';
        let maxCount = 0;
        for (const [lang, count] of Object.entries(counts)) {
          if (count > maxCount) {
            maxLang = lang;
            maxCount = count;
          }
        }

        // Calculate confidence (simple ratio of matched chars to total)
        const totalChars = text.length;
        const confidence = totalChars > 0 ? maxCount / totalChars : 0;

        return {
          detectedLanguage: maxLang,
          confidence: Math.min(confidence * 1.5, 1.0) // Adjust confidence and cap at 1.0
        };
      };

      // Detect language
      const { detectedLanguage, confidence } = detectLanguageSimple(text);

      return reply.code(200).send({
        success: true,
        data: {
          detectedLanguage,
          confidence,
        },
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to detect language',
        error: 'LANGUAGE_DETECTION_ERROR',
      });
    }
  }

  /**
   * Test connection to Bhashini API
   */
  async testConnection(
    request: FastifyRequest<{
      Body: {
        userId?: string;
        ulcaApiKey?: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { userId, ulcaApiKey } = request.body;

      // Use default userId if not provided
      const bhashiniUserId = userId || 'cee60134c6bb4d179efd3fda48ff32fe';

      // Test authentication
      const authToken = await bhashiniApiService.getAuthToken(bhashiniUserId, ulcaApiKey);

      if (authToken) {
        return reply.code(200).send({
          success: true,
          message: 'Successfully connected to Bhashini API',
        });
      }

      throw new Error('Failed to authenticate with Bhashini API');
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to connect to Bhashini API',
        error: 'CONNECTION_ERROR',
      });
    }
  }
}
