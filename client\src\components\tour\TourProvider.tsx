import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useLanguage } from '@/context/LanguageContext';

// Tour step interface
export interface TourStep {
  id: string;
  target: string; // CSS selector for the target element
  title: string;
  content: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  showSkip?: boolean;
  showPrevious?: boolean;
  showNext?: boolean;
  action?: () => void; // Optional action to perform when step is shown
}

// Tour configuration interface
export interface TourConfig {
  id: string;
  name: string;
  steps: TourStep[];
  autoStart?: boolean;
  showProgress?: boolean;
  allowSkip?: boolean;
}

// Tour context interface
interface TourContextType {
  currentTour: TourConfig | null;
  currentStepIndex: number;
  isActive: boolean;
  startTour: (tourId: string) => void;
  stopTour: () => void;
  nextStep: () => void;
  previousStep: () => void;
  skipTour: () => void;
  goToStep: (stepIndex: number) => void;
}

// Create tour context
const TourContext = createContext<TourContextType | undefined>(undefined);

// Available tours configuration
const TOURS: Record<string, TourConfig> = {
  welcome: {
    id: 'welcome',
    name: 'Welcome Tour',
    autoStart: true,
    showProgress: true,
    allowSkip: true,
    steps: [
      {
        id: 'welcome-start',
        target: 'body',
        title: 'Welcome to LawEngaxe!',
        content: 'Let us show you around this powerful multilingual legal assistance platform.',
        placement: 'bottom',
        showSkip: true,
        showNext: true,
      },
      {
        id: 'language-selector',
        target: '[data-tour="language-selector"]',
        title: 'Language Selection',
        content: 'Switch between English and Indian languages. All content will be automatically translated.',
        placement: 'bottom',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'search-bar',
        target: '[data-tour="search-bar"]',
        title: 'Search Videos',
        content: 'Search for legal videos in any supported language. Results will be shown in your preferred language.',
        placement: 'bottom',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'navigation',
        target: '[data-tour="navigation"]',
        title: 'Navigation Menu',
        content: 'Access different sections: Home, Trending videos, Creator Studio, and your Profile.',
        placement: 'right',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'creator-studio',
        target: '[data-tour="creator-studio"]',
        title: 'Creator Studio',
        content: 'Upload and manage your legal content. Add videos in multiple languages.',
        placement: 'bottom',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'theme-toggle',
        target: '[data-tour="theme-toggle"]',
        title: 'Theme Settings',
        content: 'Switch between light, dark, and system themes for comfortable viewing.',
        placement: 'left',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'tour-complete',
        target: 'body',
        title: 'Tour Complete!',
        content: 'You\'re all set! Start exploring legal content in your preferred language.',
        placement: 'bottom',
        showPrevious: true,
        showSkip: false,
      },
    ],
  },
  
  creatorStudio: {
    id: 'creatorStudio',
    name: 'Creator Studio Tour',
    showProgress: true,
    allowSkip: true,
    steps: [
      {
        id: 'upload-video',
        target: '[data-tour="upload-button"]',
        title: 'Upload Videos',
        content: 'Click here to upload your legal content. Supports multiple video formats.',
        placement: 'bottom',
        showNext: true,
      },
      {
        id: 'video-details',
        target: '[data-tour="video-form"]',
        title: 'Video Information',
        content: 'Add title, description, and tags. Content will be automatically translated to other languages.',
        placement: 'right',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'language-options',
        target: '[data-tour="language-options"]',
        title: 'Language Settings',
        content: 'Select the original language and choose which languages to translate to.',
        placement: 'top',
        showPrevious: true,
        showNext: true,
      },
      {
        id: 'publish-video',
        target: '[data-tour="publish-button"]',
        title: 'Publish Content',
        content: 'Review and publish your video. It will be available in all selected languages.',
        placement: 'top',
        showPrevious: true,
      },
    ],
  },
};

// Tour provider component
export function TourProvider({ children }: { children: ReactNode }) {
  const [currentTour, setCurrentTour] = useState<TourConfig | null>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const { t } = useLanguage();

  // Check if user has seen welcome tour
  useEffect(() => {
    const hasSeenWelcomeTour = localStorage.getItem('hasSeenWelcomeTour');
    if (!hasSeenWelcomeTour && TOURS.welcome.autoStart) {
      // Delay to ensure DOM is ready
      setTimeout(() => {
        startTour('welcome');
      }, 1000);
    }
  }, []);

  const startTour = (tourId: string) => {
    const tour = TOURS[tourId];
    if (tour) {
      setCurrentTour(tour);
      setCurrentStepIndex(0);
      setIsActive(true);
      
      // Execute first step action if any
      if (tour.steps[0]?.action) {
        tour.steps[0].action();
      }
    }
  };

  const stopTour = () => {
    setIsActive(false);
    setCurrentTour(null);
    setCurrentStepIndex(0);
    
    // Mark welcome tour as seen
    if (currentTour?.id === 'welcome') {
      localStorage.setItem('hasSeenWelcomeTour', 'true');
    }
  };

  const nextStep = () => {
    if (!currentTour) return;
    
    const nextIndex = currentStepIndex + 1;
    if (nextIndex < currentTour.steps.length) {
      setCurrentStepIndex(nextIndex);
      
      // Execute step action if any
      const nextStepAction = currentTour.steps[nextIndex]?.action;
      if (nextStepAction) {
        nextStepAction();
      }
    } else {
      stopTour();
    }
  };

  const previousStep = () => {
    if (currentStepIndex > 0) {
      const prevIndex = currentStepIndex - 1;
      setCurrentStepIndex(prevIndex);
      
      // Execute step action if any
      const prevStepAction = currentTour?.steps[prevIndex]?.action;
      if (prevStepAction) {
        prevStepAction();
      }
    }
  };

  const skipTour = () => {
    stopTour();
  };

  const goToStep = (stepIndex: number) => {
    if (currentTour && stepIndex >= 0 && stepIndex < currentTour.steps.length) {
      setCurrentStepIndex(stepIndex);
      
      // Execute step action if any
      const stepAction = currentTour.steps[stepIndex]?.action;
      if (stepAction) {
        stepAction();
      }
    }
  };

  const contextValue: TourContextType = {
    currentTour,
    currentStepIndex,
    isActive,
    startTour,
    stopTour,
    nextStep,
    previousStep,
    skipTour,
    goToStep,
  };

  return (
    <TourContext.Provider value={contextValue}>
      {children}
    </TourContext.Provider>
  );
}

// Hook to use tour context
export function useTour() {
  const context = useContext(TourContext);
  if (context === undefined) {
    throw new Error('useTour must be used within a TourProvider');
  }
  return context;
}

// Export tours for external access
export { TOURS };
