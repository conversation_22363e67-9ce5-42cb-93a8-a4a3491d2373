import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Plus } from 'lucide-react';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Language } from '@/types';
import AdditionalLanguageEntry, { AdditionalLanguageEntry as LanguageEntryType } from './AdditionalLanguageEntry';

interface ConfigureVideoStepProps {
  engaxeUrl: string;
  initialTitle: string;
  initialDescription: string;
  initialThumbnail: string;
  initialCategory: string;
  languages: Language[];
  onBack: () => void;
  onSave: (data: {
    title: string;
    description: string;
    thumbnail: string;
    thumbnailFile: File | null;
    category: string;
    defaultLanguage: string;
    defaultLanguageUrl: string;
    additionalLanguages: LanguageEntryType[];
  }) => void;
}

export default function ConfigureVideoStep({
  engaxeUrl,
  initialTitle,
  initialDescription,
  initialThumbnail,
  initialCategory,
  languages,
  onBack,
  onSave
}: ConfigureVideoStepProps) {
  // Video data
  const [title, setTitle] = useState(initialTitle);
  const [description, setDescription] = useState(initialDescription);
  const [thumbnail, setThumbnail] = useState(initialThumbnail);
  const [thumbnailFile, setThumbnailFile] = useState<File | null>(null);
  const [category, setCategory] = useState(initialCategory);
  const [defaultLanguage, setDefaultLanguage] = useState('en');
  const [defaultLanguageUrl] = useState(engaxeUrl);
  
  // Additional languages
  const [additionalLanguages, setAdditionalLanguages] = useState<LanguageEntryType[]>([]);
  
  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Categories
  const categories = [
    'Education', 'Business', 'Technology', 'Cooking', 'Travel',
    'Music', 'Sports', 'Gaming', 'Health', 'Science'
  ];

  // Function to handle thumbnail upload
  const handleThumbnailUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      alert("Please upload an image file");
      return;
    }

    // Set the thumbnail file
    setThumbnailFile(file);

    // Create a URL for the thumbnail preview
    const objectUrl = URL.createObjectURL(file);
    setThumbnail(objectUrl);
  };

  // Function to add a new language entry
  const addLanguageEntry = () => {
    const newEntry: LanguageEntryType = {
      id: `lang-${Date.now()}`,
      languageCode: '',
      url: ''
    };
    
    setAdditionalLanguages(prev => [...prev, newEntry]);
  };

  // Function to update a language entry
  const updateLanguageEntry = (id: string, field: 'languageCode' | 'url', value: string) => {
    setAdditionalLanguages(prev => 
      prev.map(entry => 
        entry.id === id ? { ...entry, [field]: value } : entry
      )
    );
  };

  // Function to remove a language entry
  const removeLanguageEntry = (id: string) => {
    setAdditionalLanguages(prev => prev.filter(entry => entry.id !== id));
  };

  // Get available languages (excluding the default language and already selected ones)
  const getAvailableLanguages = () => {
    const selectedCodes = [defaultLanguage, ...additionalLanguages.map(entry => entry.languageCode)];
    return languages.filter(lang => !selectedCodes.includes(lang.code));
  };

  // Handle save
  const handleSave = () => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onSave({
        title,
        description,
        thumbnail,
        thumbnailFile,
        category,
        defaultLanguage,
        defaultLanguageUrl,
        additionalLanguages
      });
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {/* Title */}
        <div className="space-y-2">
          <label htmlFor="title" className="text-sm font-medium">Video Title</label>
          <Input 
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter video title"
          />
        </div>
        
        {/* Thumbnail */}
        <div className="space-y-2">
          <label htmlFor="thumbnail" className="text-sm font-medium">Thumbnail</label>
          <div className="flex flex-col gap-2">
            <div className="relative aspect-video w-full overflow-hidden rounded-md border border-gray-700">
              <img 
                src={thumbnail} 
                alt="Video thumbnail" 
                className="h-full w-full object-cover"
              />
            </div>
            <Input
              id="thumbnail"
              type="file"
              accept="image/*"
              onChange={handleThumbnailUpload}
              className="cursor-pointer"
            />
            <p className="text-xs text-lingstream-muted">
              Upload a custom thumbnail or use the one fetched from Engaxe
            </p>
          </div>
        </div>
        
        {/* Description */}
        <div className="space-y-2">
          <label htmlFor="description" className="text-sm font-medium">Description</label>
          <Textarea 
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter video description"
            rows={3}
          />
        </div>
        
        {/* Category */}
        <div className="space-y-2">
          <label htmlFor="category" className="text-sm font-medium">Category</label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((cat) => (
                <SelectItem key={cat} value={cat}>{cat}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {/* Default Language Section */}
        <div className="space-y-2 border-t pt-4 mt-4">
          <label className="text-sm font-medium">Default Language</label>
          <div className="grid grid-cols-2 gap-2">
            <Select value={defaultLanguage} onValueChange={setDefaultLanguage}>
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    <div className="flex items-center gap-2">
                      <span>{lang.flag}</span>
                      <span>{lang.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Input
              value={defaultLanguageUrl}
              placeholder="Engaxe URL for this language"
              disabled
            />
          </div>
        </div>
        
        {/* Additional Languages Section */}
        <div className="space-y-2 mt-4">
          <div className="flex justify-between items-center">
            <label className="text-sm font-medium">Additional Languages</label>
            <Button 
              type="button" 
              variant="outline" 
              size="sm"
              onClick={addLanguageEntry}
              disabled={getAvailableLanguages().length === 0}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Language
            </Button>
          </div>
          
          {additionalLanguages.length === 0 ? (
            <p className="text-xs text-lingstream-muted">
              Add additional language versions of this video
            </p>
          ) : (
            <div className="space-y-3">
              {additionalLanguages.map((entry) => (
                <AdditionalLanguageEntry
                  key={entry.id}
                  entry={entry}
                  availableLanguages={getAvailableLanguages()}
                  onUpdate={updateLanguageEntry}
                  onRemove={removeLanguageEntry}
                />
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onBack}>
          Back
        </Button>
        <Button 
          onClick={handleSave}
          disabled={!title || !category || isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            'Save Video'
          )}
        </Button>
      </div>
    </div>
  );
}
