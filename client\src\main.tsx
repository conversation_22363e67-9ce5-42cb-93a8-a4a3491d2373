import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
import './styles/video-player.css'
import { initVideoPlayerFullscreen } from './utils/videoPlayerUtils'
import { initAutoHideScrollbars } from './utils/scrollUtils'

// Initialize video player fullscreen functionality
document.addEventListener('DOMContentLoaded', () => {
  // Initialize video player fullscreen functionality
  initVideoPlayerFullscreen();

  // Initialize auto-hide scrollbars
  setTimeout(() => {
    initAutoHideScrollbars();
  }, 500); // Small delay to ensure DOM is fully loaded
});

createRoot(document.getElementById("root")!).render(<App />);
