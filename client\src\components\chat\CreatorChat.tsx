import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Send, ArrowRight, Mic, MicOff, Volume2, VolumeX, Pause } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext';
import { useChatbot, AIAssistantProvider } from '@/context/ChatbotContext';
import { useMessages } from '@/context/MessageContext';
import { useToast } from '@/hooks/use-toast';
import { useLanguage } from '@/context/LanguageContext';
import { User, Video, Conversation, Message as MessageType } from '@/types';
import { AIAssistantErrorHandler } from './AIAssistantErrorHandler';

// Add Web Speech API type definitions
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}
import aiAssistantAPI from '@/services/aiAssistantApi';
import { useNavigate } from 'react-router-dom';
import * as speechSynthesis from '@/utils/speechSynthesis';

interface CreatorChatProps {
  isOpen?: boolean;
  onClose?: () => void;
  creator: User;
  video?: Video;
}

export default function CreatorChat({ isOpen = false, onClose, creator, video }: CreatorChatProps) {
  const { currentUser } = useAuth();
  const {
    isAIAssistantEnabled,
    aiAssistantProvider,
    aiAssistantApiKey,
    aiAssistantEndpoint,
    aiProviders,
    getEnabledProviders,
    isVoiceOutputEnabled
  } = useChatbot();
  const { currentLanguage } = useLanguage();

  // Get the first enabled provider or fall back to the legacy provider
  const enabledProviders = getEnabledProviders();
  // Prioritize OpenRouter if it's enabled
  const openRouterEnabled = aiProviders.openrouter.enabled;
  const activeProvider = openRouterEnabled ? 'openrouter' :
                         (enabledProviders.length > 0 ? enabledProviders[0] : aiAssistantProvider);
  const providerConfig = aiProviders[activeProvider];

  console.log('Active AI provider:', activeProvider);
  const { createConversation, conversations, setActiveConversation } = useMessages();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [existingConversation, setExistingConversation] = useState<Conversation | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [currentlySpeakingIndex, setCurrentlySpeakingIndex] = useState<number | null>(null);
  const [localVoiceEnabled, setLocalVoiceEnabled] = useState(isVoiceOutputEnabled);
  const [manuallyStoppedListening, setManuallyStoppedListening] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [errorType, setErrorType] = useState<string | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<BlobPart[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const recognitionRef = useRef<SpeechRecognition | null>(null);

  // Get the best name to display for the creator
  const creatorDisplayName = creator.displayName || creator.username || 'Video Creator';

  const [chatHistory, setChatHistory] = useState<Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
  }>>([
    {
      role: 'assistant',
      content: `Hello! Thanks for reaching out. How can I help you with questions about ${video?.title || 'this content'}?`,
      timestamp: new Date().toISOString()
    }
  ]);

  // Check for existing conversations with this creator
  useEffect(() => {
    const existingConv = conversations.find(c =>
      c.participants.some(p => p.id === creator.id)
    );

    if (existingConv) {
      console.log('Found existing conversation with creator:', existingConv.id);
      setExistingConversation(existingConv);
      setConversationId(existingConv.id);
    }
  }, [creator.id, conversations]);

  // Update expanded state when isOpen prop changes
  useEffect(() => {
    setIsExpanded(isOpen);
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Scroll to bottom when chat history changes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    // No automatic voice output - only speak when the user clicks the voice icon
  }, [chatHistory]);

  // Sync local voice enabled state with global setting
  useEffect(() => {
    setLocalVoiceEnabled(isVoiceOutputEnabled);
  }, [isVoiceOutputEnabled]);

  // Stop speaking when component unmounts
  useEffect(() => {
    return () => {
      speechSynthesis.stop();
    };
  }, []);

  const handleClose = () => {
    // Stop any ongoing speech when closing the chat
    speechSynthesis.stop();
    // Stop any ongoing voice recognition
    stopVoiceRecognition(false);
    setIsExpanded(false);
    if (onClose) onClose();
  };

  // Function to speak a message
  const speakMessage = (text: string, messageIndex?: number, autoStartMic: boolean = true) => {
    // Stop any ongoing speech
    speechSynthesis.stop();

    // Set speaking state
    setIsSpeaking(true);
    if (messageIndex !== undefined) {
      setCurrentlySpeakingIndex(messageIndex);
    } else {
      setCurrentlySpeakingIndex(null);
    }

    // Get language code for speech synthesis
    const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
    console.log(`Using speech synthesis language: ${langCode}`);

    // Speak the message
    speechSynthesis.speak(text, {
      lang: langCode,
      rate: 1.0,
      pitch: 1.0,
      onEnd: () => {
        setIsSpeaking(false);
        setCurrentlySpeakingIndex(null);

        // Only automatically start listening if autoStartMic is true and it wasn't manually stopped
        if (autoStartMic && !manuallyStoppedListening) {
          console.log('AI finished speaking, automatically starting voice recognition');
          // Use a small delay to ensure speech has fully stopped
          setTimeout(() => {
            if (!isListening && !manuallyStoppedListening) {
              startVoiceRecognition();
            }
          }, 300);
        }
      },
      onError: (error) => {
        console.error('Speech synthesis error:', error);
        setIsSpeaking(false);
        setCurrentlySpeakingIndex(null);
        // Silent error handling - no toast notification

        // Only try to start listening on error if autoStartMic is true and it wasn't manually stopped
        if (autoStartMic && !manuallyStoppedListening) {
          setTimeout(() => {
            if (!isListening && !manuallyStoppedListening) {
              startVoiceRecognition();
            }
          }, 300);
        }
      }
    });
  };

  const handleSendMessage = async () => {
    if (!message.trim() || isSending) return;

    if (!currentUser) {
      toast({
        title: "Login Required",
        description: "Please login to chat with creators",
        variant: "destructive"
      });
      return;
    }

    // Store the current message before clearing the input
    const currentMessage = message;

    // Add user message to chat
    const userMessage = {
      role: 'user' as const,
      content: currentMessage,
      timestamp: new Date().toISOString()
    };

    setChatHistory(prev => [...prev, userMessage]);
    setMessage('');
    setIsSending(true);

    try {
      // Clear any previous errors
      setError(null);
      setErrorType(null);

      // Check if OpenRouter is enabled and use it directly for better text responses
      const openRouterConfig = aiProviders.openrouter;
      const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

      if (isOpenRouterEnabled) {
        // Use OpenRouter directly for text messages (same as voice messages but without voice output)
        await processWithOpenRouterForText(currentMessage);
      } else if (isAIAssistantEnabled) {
        // Fallback to AI Assistant API
        // Format chat history for API
        const formattedHistory = chatHistory.map(msg => ({
          role: msg.role,
          content: msg.content
        }));

        // Log the request details for debugging
        console.log('Sending message to AI Assistant with provider:', activeProvider);
        console.log('Using model:', providerConfig.model);
        console.log('API Key (masked):', providerConfig.apiKey ? `${providerConfig.apiKey.substring(0, 5)}...${providerConfig.apiKey.substring(providerConfig.apiKey.length - 5)}` : 'none');

        // Send message to AI Assistant using the active provider
        const response = await aiAssistantAPI.sendMessage({
          message: currentMessage,
          provider: activeProvider,
          apiKey: providerConfig.apiKey || aiAssistantApiKey, // Fall back to legacy key if needed
          endpoint: activeProvider === 'custom' ? (providerConfig.endpoint || aiAssistantEndpoint) : undefined,
          model: providerConfig.model, // Pass the model for providers that support multiple models
          conversationId: conversationId || undefined,
          creatorId: creator.id,
          creatorName: creatorDisplayName, // Pass the creator's display name
          videoId: video?.id,
          chatHistory: formattedHistory
        });

        // Log the response for debugging
        console.log('AI Assistant response:', response);

        if (response.success) {
          // Add assistant response to chat
          setChatHistory(prev => [
            ...prev,
            {
              role: 'assistant',
              content: response.data.response,
              timestamp: response.data.timestamp
            }
          ]);

          // Save conversation ID for future messages
          if (!conversationId) {
            setConversationId(response.data.conversationId);
          }

          // Also store this in the messaging system
          try {
            // Get the formatted subject line
            const subject = video ? `Chat about: ${video.title}` : `Chat with ${creatorDisplayName}`;

            // If we have an existing conversation, use it, otherwise create a new one
            if (existingConversation) {
              console.log('Adding message to existing conversation:', existingConversation.id);
              // The message will be added to the existing conversation
              await createConversation({
                creatorId: creator.id,
                subject,
                initialMessage: message
              });
            } else {
              // Create a new conversation with the creator
              const result = await createConversation({
                creatorId: creator.id,
                subject,
                initialMessage: message
              });

              if (result?.success && result.data) {
                setExistingConversation(result.data);
                setConversationId(result.data.id);
              }
            }
          } catch (error) {
            console.error('Failed to save chat to messaging system:', error);
          }
        } else {
          // Handle error response
          setError(response.error || "Failed to send message. Please try again.");
          setErrorType(response.errorType || "unknown_error");

          // Show error message in toast
          toast({
            title: "Error",
            description: response.error || "Failed to send message. Please try again.",
            variant: "destructive"
          });

          // Add error message to chat
          setChatHistory(prev => [
            ...prev,
            {
              role: 'assistant',
              content: "I'm sorry, I couldn't process your request. Please try again later.",
              timestamp: new Date().toISOString()
            }
          ]);
        }
      } else {
        // If neither OpenRouter nor AI Assistant is enabled, show error message
        toast({
          title: "AI Assistant Not Configured",
          description: "Please enable OpenRouter or AI Assistant in settings to chat with AI.",
          variant: "destructive"
        });

        // Add error message to chat
        setChatHistory(prev => [
          ...prev,
          {
            role: 'assistant',
            content: "AI Assistant is not configured. Please enable OpenRouter or AI Assistant in settings to chat with me.",
            timestamp: new Date().toISOString()
          }
        ]);
      }
    } catch (error) {
      console.error('Error in chat:', error);

      // Show error message
      toast({
        title: "Error",
        description: "An error occurred while sending your message. Please try again.",
        variant: "destructive"
      });

      // Add error message to chat
      setChatHistory(prev => [
        ...prev,
        {
          role: 'assistant',
          content: "I'm sorry, there was an error processing your message. Please try again later.",
          timestamp: new Date().toISOString()
        }
      ]);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Speech recognition functions
  const startVoiceRecognition = () => {
    if (isListening) {
      stopVoiceRecognition(true); // Pass true to indicate manual stop
      return;
    }

    // Reset the manually stopped flag when starting voice recognition
    setManuallyStoppedListening(false);

    // Get OpenRouter configuration
    const openRouterConfig = aiProviders.openrouter;
    const isOpenRouterEnabled = openRouterConfig.enabled && openRouterConfig.apiKey;

    if (!isOpenRouterEnabled) {
      toast({
        title: "OpenRouter Not Configured",
        description: "Please enable OpenRouter and add your API key in settings.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Use the Web Speech API for voice recognition
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();

        // Store the recognition instance in the ref
        recognitionRef.current = recognition;

        // Set language based on current language selection
        const langCode = currentLanguage.code === 'hi' ? 'hi-IN' : 'en-US';
        recognition.lang = langCode;
        console.log(`Setting speech recognition language to: ${langCode}`);
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onstart = () => {
          console.log('Voice recognition started');
          setIsListening(true);
          toast({
            title: "Listening",
            description: "Speak clearly into your microphone for speech recognition",
            variant: "default"
          });
        };

        recognition.onresult = async (event) => {
          const transcript = event.results[0][0].transcript;
          console.log('Voice recognition result:', transcript);

          if (transcript && transcript.trim()) {
            // Add user message to chat
            const userMessage = {
              role: 'user' as const,
              content: transcript,
              timestamp: new Date()
            };
            setChatHistory(prev => [...prev, userMessage]);

            // Process with OpenRouter directly
            await processWithOpenRouter(transcript);
          }
        };

        recognition.onerror = (event) => {
          console.error('Voice recognition error:', event.error);
          setIsListening(false);
          recognitionRef.current = null;
          toast({
            title: "Voice Recognition Error",
            description: `Error: ${event.error}`,
            variant: "destructive"
          });
        };

        recognition.onend = () => {
          console.log('Voice recognition ended');
          setIsListening(false);
          recognitionRef.current = null;
        };

        recognition.start();
      } else {
        toast({
          title: "Voice Recognition Not Supported",
          description: "Your browser doesn't support voice recognition. Try Chrome or Edge.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      setIsListening(false);
      toast({
        title: "Voice Recognition Error",
        description: "Could not start voice recognition.",
        variant: "destructive"
      });
    }
  };

  // Process text with OpenRouter for text messages (no voice output)
  const processWithOpenRouterForText = async (text: string) => {
    const openRouterConfig = aiProviders.openrouter;

    setIsSending(true);

    // Clear any previous errors
    setError(null);
    setErrorType(null);

    try {
      // Validate API key
      if (!openRouterConfig.apiKey || openRouterConfig.apiKey.trim() === '') {
        setError('OpenRouter API key is missing. Please provide a valid API key in the settings.');
        setErrorType('auth_error');
        throw new Error('OpenRouter API key is missing');
      }

      // Format messages for the API
      const formattedMessages = chatHistory
        .filter(msg => msg.role === 'user' || msg.role === 'assistant')
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }));

      // Add the current message
      formattedMessages.push({
        role: 'user',
        content: text
      });

      // Create system prompt with video content context and restrictions
      let systemPrompt = `You are ${creatorDisplayName}, a content creator on LawEngaxe. Keep responses brief and concise (2-3 sentences maximum). `;

      if (video) {
        let videoContent = `Video Title: ${video.title}\n`;
        if (video.description) {
          videoContent += `Description: ${video.description}\n`;
        }
        if (video.category) {
          videoContent += `Category: ${video.category}\n`;
        }
        if (video.tags && video.tags.length > 0) {
          videoContent += `Tags: ${video.tags.join(', ')}\n`;
        }

        systemPrompt += `You are responding about your specific video with the following content:

${videoContent}

IMPORTANT INSTRUCTIONS:
1. ONLY answer questions that are directly related to the content of this specific video
2. If a user asks about anything NOT related to this video's content, respond EXACTLY with: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."
3. Base your answers only on the video information provided above
4. Be helpful and knowledgeable about YOUR video content
5. Do not mention that you are an AI
6. Keep responses very short and concise (maximum 2-3 sentences)
7. Provide direct, brief answers without lengthy explanations`;
      } else {
        systemPrompt += `You can only answer questions about the specific video content. If no video content is available or if the user asks about anything not related to the video, respond with: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."`;
      }

      // Add system message to the beginning of messages
      const messagesWithSystem = [
        { role: 'system', content: systemPrompt },
        ...formattedMessages
      ];

      // Call OpenRouter API
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openRouterConfig.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'LawEngaxe Text Assistant'
        },
        body: JSON.stringify({
          model: openRouterConfig.model || 'anthropic/claude-3-haiku',
          messages: messagesWithSystem,
          max_tokens: 150,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('OpenRouter API error:', errorData);

        // Set appropriate error type based on status code
        if (response.status === 401) {
          setError(errorData.error?.message || 'Authentication failed. Please check your API key.');
          setErrorType('auth_error');
        } else if (response.status === 403) {
          setError(errorData.error?.message || 'Access denied. Your API key may not have permission to use this model.');
          setErrorType('permission_error');
        } else if (response.status === 429) {
          setError(errorData.error?.message || 'Rate limit exceeded. Please try again later.');
          setErrorType('rate_limit_error');
        } else {
          setError(errorData.error?.message || 'Failed to get response from OpenRouter');
          setErrorType('openrouter_error');
        }

        throw new Error(errorData.error?.message || 'Failed to get response from OpenRouter');
      }

      const data = await response.json();
      console.log('OpenRouter response:', data);

      // Extract the response text
      const responseText = data.choices[0]?.message?.content || 'Sorry, I could not generate a response.';

      // Add assistant message to chat
      const assistantMessage = {
        role: 'assistant' as const,
        content: responseText,
        timestamp: new Date().toISOString()
      };
      setChatHistory(prev => [...prev, assistantMessage]);

      // No voice output for text messages - just show success toast
      toast({
        title: "Response Received",
        description: "AI has responded to your message",
        variant: "default"
      });
    } catch (error: any) {
      console.error('Error processing message:', error);

      // If error and errorType haven't been set yet, set them now
      if (!error) {
        setError(error.message || "Failed to process your message");
        setErrorType('unknown_error');
      }

      toast({
        title: "Error",
        description: error.message || "Failed to process your message",
        variant: "destructive"
      });

      // Add error message to chat
      const errorMessage = {
        role: 'assistant' as const,
        content: "Sorry, I encountered an error processing your request. Please try again.",
        timestamp: new Date().toISOString()
      };
      setChatHistory(prev => [...prev, errorMessage]);
    } finally {
      setIsSending(false);
    }
  };

  // Process text with OpenRouter and speak the response (for voice messages)
  const processWithOpenRouter = async (text: string) => {
    const openRouterConfig = aiProviders.openrouter;

    setIsSending(true);

    // Clear any previous errors
    setError(null);
    setErrorType(null);

    try {
      // Validate API key
      if (!openRouterConfig.apiKey || openRouterConfig.apiKey.trim() === '') {
        setError('OpenRouter API key is missing. Please provide a valid API key in the settings.');
        setErrorType('auth_error');
        throw new Error('OpenRouter API key is missing');
      }

      // toast({
      //   title: "Processing",
      //   description: `"${text}" - Sending to AI...`,
      // });

      // Format messages for the API
      const formattedMessages = chatHistory
        .filter(msg => msg.role === 'user' || msg.role === 'assistant')
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }));

      // Add the current message
      formattedMessages.push({
        role: 'user',
        content: text
      });

      // Create system prompt with video content context and restrictions
      let systemPrompt = `You are ${creatorDisplayName}, a content creator on LawEngaxe. Keep responses brief and concise (2-3 sentences maximum). `;

      if (video) {
        let videoContent = `Video Title: ${video.title}\n`;
        if (video.description) {
          videoContent += `Description: ${video.description}\n`;
        }
        if (video.category) {
          videoContent += `Category: ${video.category}\n`;
        }
        if (video.tags && video.tags.length > 0) {
          videoContent += `Tags: ${video.tags.join(', ')}\n`;
        }

        systemPrompt += `You are responding about your specific video with the following content:

${videoContent}

IMPORTANT INSTRUCTIONS:
1. ONLY answer questions that are directly related to the content of this specific video
2. If a user asks about anything NOT related to this video's content, respond EXACTLY with: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."
3. Base your answers only on the video information provided above
4. Be helpful and knowledgeable about YOUR video content
5. Do not mention that you are an AI
6. Keep responses very short and concise (maximum 2-3 sentences)
7. Provide direct, brief answers without lengthy explanations`;
      } else {
        systemPrompt += `You can only answer questions about the specific video content. If no video content is available or if the user asks about anything not related to the video, respond with: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."`;
      }

      // Add system message to the beginning of messages
      const messagesWithSystem = [
        { role: 'system', content: systemPrompt },
        ...formattedMessages
      ];

      // Call OpenRouter API
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openRouterConfig.apiKey}`,
          'HTTP-Referer': window.location.origin,
          'X-Title': 'LawEngaxe Voice Assistant'
        },
        body: JSON.stringify({
          model: openRouterConfig.model || 'anthropic/claude-3-haiku',
          messages: messagesWithSystem,
          max_tokens: 150,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('OpenRouter API error:', errorData);

        // Set appropriate error type based on status code
        if (response.status === 401) {
          setError(errorData.error?.message || 'Authentication failed. Please check your API key.');
          setErrorType('auth_error');
        } else if (response.status === 403) {
          setError(errorData.error?.message || 'Access denied. Your API key may not have permission to use this model.');
          setErrorType('permission_error');
        } else if (response.status === 429) {
          setError(errorData.error?.message || 'Rate limit exceeded. Please try again later.');
          setErrorType('rate_limit_error');
        } else {
          setError(errorData.error?.message || 'Failed to get response from OpenRouter');
          setErrorType('openrouter_error');
        }

        throw new Error(errorData.error?.message || 'Failed to get response from OpenRouter');
      }

      const data = await response.json();
      console.log('OpenRouter response:', data);

      // Extract the response text
      const responseText = data.choices[0]?.message?.content || 'Sorry, I could not generate a response.';

      // Add assistant message to chat
      const assistantMessage = {
        role: 'assistant' as const,
        content: responseText,
        timestamp: new Date()
      };
      setChatHistory(prev => [...prev, assistantMessage]);

      // Speak the response - only auto-start microphone if not manually stopped
      console.log('Speaking AI response. manuallyStoppedListening:', manuallyStoppedListening, 'autoStartMic:', !manuallyStoppedListening);
      speakMessage(responseText, undefined, !manuallyStoppedListening);
    } catch (error: any) {
      console.error('Error processing message:', error);

      // If error and errorType haven't been set yet, set them now
      if (!error) {
        setError(error.message || "Failed to process your message");
        setErrorType('unknown_error');
      }

      toast({
        title: "Error",
        description: error.message || "Failed to process your message",
        variant: "destructive"
      });

      // Add error message to chat
      const errorMessage = {
        role: 'assistant' as const,
        content: "Sorry, I encountered an error processing your request. Please try again.",
        timestamp: new Date()
      };
      setChatHistory(prev => [...prev, errorMessage]);
    } finally {
      setIsSending(false);
    }
  };

  // Stop voice recognition
  const stopVoiceRecognition = (isManualStop = false) => {
    setIsListening(false);

    // Stop the actual recognition instance
    try {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
        recognitionRef.current = null;
      }
    } catch (error) {
      console.error('Error stopping recognition:', error);
    }

    // If this is a manual stop (user clicked the button), set the flag
    if (isManualStop) {
      console.log('Microphone manually stopped by user');
      setManuallyStoppedListening(true);
    }
  };

  const transcribeAudio = async (audioBlob: Blob) => {
    try {
      setIsSending(true);

      // Log the API key (masked for security) to help with debugging
      const apiKey = aiProviders.openai.apiKey || aiAssistantApiKey;
      const maskedKey = apiKey ? `${apiKey.substring(0, 3)}...${apiKey.substring(apiKey.length - 4)}` : 'not set';
      console.log(`Using OpenAI API key: ${maskedKey}`);

      if (!apiKey || apiKey.includes('REPLACE_WITH_YOUR_ACTUAL_OPENAI_API_KEY')) {
        // If API key is not set or is still the placeholder, show a helpful error
        toast({
          title: "API Key Not Configured",
          description: "Please set your OpenAI API key in the settings",
          variant: "destructive"
        });
        throw new Error('OpenAI API key not properly configured');
      }

      // Create a FormData object to send the audio file
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.webm');
      formData.append('model', 'whisper-1');

      // Log the audio blob details to help with debugging
      console.log(`Audio blob size: ${audioBlob.size} bytes, type: ${audioBlob.type}`);

      // For debugging - convert small audio to base64 to check content
      if (audioBlob.size < 100000) { // Only for small files
        const reader = new FileReader();
        reader.readAsDataURL(audioBlob);
        reader.onloadend = () => {
          console.log('Audio sample (base64):', reader.result?.toString().substring(0, 50) + '...');
        };
      }

      // Send to OpenAI Whisper API
      console.log('Sending request to OpenAI Whisper API...');
      const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`
        },
        body: formData
      });

      if (!response.ok) {
        // Get detailed error information
        const errorText = await response.text();
        console.error(`Transcription failed: Status ${response.status}, Response:`, errorText);

        let errorMessage = `Error ${response.status}: ${response.statusText}`;
        try {
          // Try to parse the error as JSON for more details
          const errorJson = JSON.parse(errorText);
          if (errorJson.error && errorJson.error.message) {
            errorMessage = errorJson.error.message;
          }
        } catch (e) {
          // If parsing fails, use the raw text
          if (errorText) errorMessage += ` - ${errorText}`;
        }

        toast({
          title: "Transcription Error",
          description: errorMessage,
          variant: "destructive"
        });

        throw new Error(`Transcription failed: ${errorMessage}`);
      }

      const data = await response.json();
      console.log('Transcription successful:', data);

      // Set the transcribed text as the message
      if (data.text) {
        setMessage(data.text);
        // toast({
        //   title: "Speech Recognition Successful",
        //   description: "Your speech has been converted to text using AI speech recognition",
        //   variant: "default"
        // });
        // Automatically send the transcribed message
        setTimeout(() => {
          handleSendMessage();
        }, 500);
      } else {
        toast({
          title: "Speech Recognition Error",
          description: "Could not recognize your speech - no text returned",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error transcribing audio:', error);
      toast({
        title: "Speech Recognition Error",
        description: error instanceof Error ? error.message : "Failed to convert speech to text using AI speech recognition",
        variant: "destructive"
      });
    } finally {
      setIsSending(false);
    }
  };

  // Collapsed state (chat bubble)
  if (!isExpanded) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          className="h-14 w-14 rounded-full bg-gradient-to-r from-orange-500 to-orange-400 shadow-xl hover:shadow-2xl hover:from-orange-600 hover:to-orange-500 text-white border-2 border-white/20 transition-all duration-300 transform hover:scale-105"
          onClick={() => setIsExpanded(true)}
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  // Expanded state (chat window)
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className="w-80 h-96 shadow-2xl flex flex-col rounded-xl border-orange-100 overflow-hidden">
        <CardHeader className="p-3 border-b flex flex-row justify-between items-center bg-gradient-to-r from-orange-500 to-orange-400 text-white shadow-md">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Avatar className="h-10 w-10 ring-2 ring-white/40 shadow-lg border border-white/10">
                <AvatarImage src={creator.avatar} alt={creator.username} />
                <AvatarFallback className="bg-white text-black text-sm font-bold border border-black">
                  {creator.username ? creator.username[0].toUpperCase() : 'U'}
                </AvatarFallback>
              </Avatar>
              <span className="absolute bottom-0 right-0 h-3.5 w-3.5 bg-green-500 rounded-full border-2 border-orange-500 animate-pulse shadow-sm"></span>
            </div>
            <div>
              <h3 className="font-bold text-base tracking-tight">{creatorDisplayName}</h3>
              <p className="text-xs text-white/90 flex items-center gap-1">
                <span>Online now</span>
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full text-white hover:bg-white/20 p-0 transition-all duration-300 hover:rotate-90 focus:ring-0 focus:ring-offset-0"
            onClick={handleClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-white to-orange-50/30">
          {/* Display error message if there is one */}
          {error && errorType && (
            <AIAssistantErrorHandler
              error={error}
              errorType={errorType}
              onRetry={() => {
                setError(null);
                setErrorType(null);
              }}
            />
          )}

          {chatHistory.map((msg, index) => (
            <div
              key={index}
              className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'} mb-3`}
            >
              <div
                className={`max-w-[80%] p-3 ${
                  msg.role === 'user'
                    ? 'bg-gradient-to-r from-orange-500 to-orange-400 text-white rounded-2xl shadow-md'
                    : currentlySpeakingIndex === index
                    ? 'bg-orange-100 border border-orange-200 rounded-2xl shadow-md'
                    : 'bg-white border border-orange-100 rounded-2xl shadow-md'
                }`}
              >
                <div className="flex justify-between items-start gap-2">
                  <div className="text-sm leading-relaxed">{msg.content}</div>
                  {msg.role === 'assistant' && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`h-6 w-6 -mt-1 -mr-1 rounded-full ${
                        currentlySpeakingIndex === index
                          ? "text-orange-500 bg-orange-100 opacity-100"
                          : "opacity-60 hover:opacity-100 hover:bg-orange-100"
                      }`}
                      title={currentlySpeakingIndex === index ? "Stop speaking" : "Speak this message"}
                      onClick={() => {
                        if (currentlySpeakingIndex === index) {
                          // Stop speech if this message is being spoken
                          speechSynthesis.stop();
                          setIsSpeaking(false);
                          setCurrentlySpeakingIndex(null);

                          // Also stop any ongoing listening when manually stopping speech
                          if (isListening) {
                            stopVoiceRecognition(true);
                          }

                          // Mark as manually stopped to prevent auto-restart of listening
                          setManuallyStoppedListening(true);

                          console.log('Individual message voice button clicked: Stopped speech and listening, marked as manually stopped');
                        } else {
                          // Start speech for this message, but don't auto-start microphone when manually clicked
                          speakMessage(msg.content, index, false);
                        }
                      }}
                    >
                      {currentlySpeakingIndex === index ? <VolumeX className="h-3.5 w-3.5" /> : <Volume2 className="h-3.5 w-3.5" />}
                    </Button>
                  )}
                </div>
                <div className="mt-1 flex justify-end">
                  <span className={`text-[10px] ${msg.role === 'user' ? 'text-white/70' : 'text-gray-400'}`}>
                    {new Date(msg.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                  </span>
                </div>
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </CardContent>

        <CardFooter className="p-3 border-t bg-gradient-to-b from-orange-50 to-white">
          <div className="flex w-full gap-2">
            <Input
              ref={inputRef}
              placeholder={isListening ? "Listening..." : `Ask about this video...`}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              disabled={isSending || isListening}
              className="flex-1 rounded-full border-orange-200 focus-visible:ring-orange-400 shadow-sm"
            />
            <button
              className={`relative flex h-9 items-center justify-center rounded-full ${
                isSpeaking
                  ? "bg-red-500 text-white"
                  : isListening
                    ? "bg-green-500 text-white"
                    : "bg-orange-500 text-white"
              } transition-colors disabled:text-gray-50 disabled:opacity-30 can-hover:hover:opacity-70 w-9`}
              onClick={() => {
                // If AI is speaking, show pause functionality
                if (isSpeaking) {
                  // Stop AI speech
                  speechSynthesis.stop();
                  setIsSpeaking(false);
                  setCurrentlySpeakingIndex(null);

                  // Also stop any ongoing listening
                  if (isListening) {
                    stopVoiceRecognition(true);
                    setIsListening(false);
                  }

                  // Mark as manually stopped to prevent auto-restart of listening
                  setManuallyStoppedListening(true);

                  console.log('Pause button clicked: Stopped AI speech and listening, marked as manually stopped');
                } else if (isListening) {
                  // If listening, stop voice recognition
                  stopVoiceRecognition(true);
                  // Mark as manually stopped to prevent auto-restart
                  setManuallyStoppedListening(true);
                  setIsListening(false);
                } else {
                  // If not in voice conversation, start voice recognition and reset manual stop flag
                  startVoiceRecognition();
                }
              }}
              disabled={isSending}
              title={
                isSpeaking
                  ? "Click to pause AI response and stop continuous listening"
                  : isListening
                    ? "Click to stop voice input"
                    : "Click to start voice input"
              }
            >
              <div className="flex items-center justify-center">
                {isSpeaking ? (
                  <Pause className="h-4 w-4" />
                ) : isListening ? (
                  <MicOff className="h-4 w-4" />
                ) : (
                  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="icon-md">
                    <path d="M5.66699 14.4165V3.5835C5.66699 2.89314 6.22664 2.3335 6.91699 2.3335C7.6072 2.33367 8.16699 2.89325 8.16699 3.5835V14.4165C8.16699 15.1068 7.6072 15.6663 6.91699 15.6665C6.22664 15.6665 5.66699 15.1069 5.66699 14.4165ZM9.83301 11.9165V6.0835C9.83301 5.39325 10.3928 4.83367 11.083 4.8335C11.7734 4.8335 12.333 5.39314 12.333 6.0835V11.9165C12.333 12.6069 11.7734 13.1665 11.083 13.1665C10.3928 13.1663 9.83301 12.6068 9.83301 11.9165ZM1.5 10.2505V7.75049C1.5 7.06013 2.05964 6.50049 2.75 6.50049C3.44036 6.50049 4 7.06013 4 7.75049V10.2505C3.99982 10.9407 3.44025 11.5005 2.75 11.5005C2.05975 11.5005 1.50018 10.9407 1.5 10.2505ZM14 10.2505V7.75049C14 7.06013 14.5596 6.50049 15.25 6.50049C15.9404 6.50049 16.5 7.06013 16.5 7.75049V10.2505C16.4998 10.9407 15.9402 11.5005 15.25 11.5005C14.5598 11.5005 14.0002 10.9407 14 10.2505Z" fill="currentColor"/>
                  </svg>
                )}
              </div>
            </button>
            <Button
              size="icon"
              onClick={handleSendMessage}
              disabled={(!message.trim() || isSending) || isListening}
              className="bg-gradient-to-r from-orange-500 to-orange-400 hover:from-orange-600 hover:to-orange-500 text-white rounded-full shadow-md transition-all duration-300 hover:shadow-lg disabled:opacity-50"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

