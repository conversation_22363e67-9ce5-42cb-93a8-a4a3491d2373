import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Bookmark, ArrowUpDown, Download, Filter, Search, Calendar, User, Clock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Sam<PERSON> saved videos data
const savedVideosData = [
  {
    id: 1,
    videoId: 'h8SN9aB7J91Deb6',
    title: 'UNO: You Play! Interactive Video Game',
    saves: 24,
    savePercentage: '12%',
    category: 'Gaming',
    creator: 'Bhariya',
    duration: '18:45',
    date: '2023-12-15'
  },
  {
    id: 2,
    videoId: 'vPwWsQ4fBCT7hF3',
    title: 'PhD | All of Would in Detail..',
    saves: 18,
    savePercentage: '9%',
    category: 'Education',
    creator: 'Muskan Dindoriya',
    duration: '32:10',
    date: '2023-12-14'
  },
  {
    id: 3,
    videoId: 'kLm9pQrT8jD2nS5',
    title: 'Learn JavaScript in 30 Minutes',
    saves: 15,
    savePercentage: '11%',
    category: 'Education',
    creator: 'CodeMaster',
    duration: '30:22',
    date: '2023-12-12'
  },
  {
    id: 4,
    videoId: 'xStzlCwln6yMMnLM',
    title: 'PLAY UNO WITH YOUR KEYBOARD..',
    saves: 12,
    savePercentage: '8%',
    category: 'Gaming',
    creator: 'Bhariya',
    duration: '14:36',
    date: '2023-12-13'
  },
  {
    id: 5,
    videoId: 'qRt7vBnH3jK4mP9',
    title: 'How to Cook Perfect Pasta Every Time',
    saves: 7,
    savePercentage: '6%',
    category: 'Food',
    creator: 'CookingPro',
    duration: '8:52',
    date: '2023-12-11'
  }
];

// Summary stats
const summaryStats = {
  totalSaves: 76,
  avgSavesPerVideo: 15.2,
  mostSavedVideo: 'UNO: You Play! Interactive Video Game',
  mostSavedCategory: 'Gaming',
  savesGrowth: 9
};

export default function SavedVideosPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [sortColumn, setSortColumn] = useState('saves');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // Get unique categories for filter
  const categories = ['all', ...new Set(savedVideosData.map(video => video.category))];

  // Filter and sort data
  const filteredData = savedVideosData.filter(video => {
    const matchesSearch = 
      video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.creator.toLowerCase().includes(searchTerm.toLowerCase()) ||
      video.videoId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = 
      categoryFilter === 'all' || 
      video.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const sortedData = [...filteredData].sort((a, b) => {
    const aValue = a[sortColumn as keyof typeof a];
    const bValue = b[sortColumn as keyof typeof b];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      if (sortDirection === 'asc') {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    } else {
      if (sortDirection === 'asc') {
        return Number(aValue) - Number(bValue);
      } else {
        return Number(bValue) - Number(aValue);
      }
    }
  });

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('desc');
    }
  };

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <main className="flex-1 p-6 overflow-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">Total Saved Videos</h1>
              <p className="text-muted-foreground">Track and analyze videos saved by users for later viewing</p>
            </div>
            <Button className="flex items-center gap-2">
              <Download size={16} />
              Export Data
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Saves</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{summaryStats.totalSaves.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mt-1">Across all videos</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Avg. Saves Per Video</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{summaryStats.avgSavesPerVideo.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mt-1">Platform average</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Most Saved</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold truncate">{summaryStats.mostSavedVideo}</div>
                <p className="text-xs text-muted-foreground mt-1">Highest save rate</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Saves Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-500">+{summaryStats.savesGrowth}%</div>
                <p className="text-xs text-muted-foreground mt-1">Compared to last period</p>
              </CardContent>
            </Card>
          </div>

          <div className="bg-card rounded-lg shadow-sm border mb-6">
            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-4 mb-6 justify-between">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search videos..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-[180px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Filter by Category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>
                          {category === 'all' ? 'All Categories' : category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px]">Video ID</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('saves')}
                      >
                        <div className="flex items-center">
                          Saves
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('savePercentage')}
                      >
                        <div className="flex items-center">
                          Save Percentage
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('category')}
                      >
                        <div className="flex items-center">
                          Category
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('creator')}
                      >
                        <div className="flex items-center">
                          Creator
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('duration')}
                      >
                        <div className="flex items-center">
                          Duration
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('date')}
                      >
                        <div className="flex items-center">
                          Date
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedData.map((video) => (
                      <TableRow key={video.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{video.videoId.substring(0, 8)}...</TableCell>
                        <TableCell>
                          <div className="font-medium truncate max-w-[250px]">{video.title}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Bookmark className="mr-2 h-4 w-4 text-yellow-500" />
                            {video.saves.toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell>{video.savePercentage}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{video.category}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <User className="mr-2 h-4 w-4 text-muted-foreground" />
                            {video.creator}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
                            {video.duration}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                            {video.date}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
