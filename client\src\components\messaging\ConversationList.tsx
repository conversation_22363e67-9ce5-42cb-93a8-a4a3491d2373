
import { useMessages } from '@/context/MessageContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';

export default function ConversationList() {
  const { conversations, setActiveConversation, activeConversation } = useMessages();

  return (
    <div className="h-full overflow-auto">
      <div className="p-4">
        <h2 className="font-semibold">Messages</h2>
      </div>
      <div className="space-y-1">
        {conversations.map((conversation) => {
          const participant = conversation.participants[0];
          return (
            <div
              key={conversation.id}
              className={`flex items-center gap-3 p-3 cursor-pointer hover:bg-lingstream-hover transition-colors ${
                activeConversation?.id === conversation.id ? 'bg-lingstream-hover' : ''
              }`}
              onClick={() => setActiveConversation(conversation.id)}
            >
              <div className="relative">
                <Avatar>
                  <AvatarImage src={participant.avatar} alt={participant.username} />
                  <AvatarFallback>{participant.username.charAt(0)}</AvatarFallback>
                </Avatar>
                {participant.isOnline && (
                  <span className="absolute bottom-0 right-0 block h-2.5 w-2.5 rounded-full border-2 border-lingstream-card bg-green-500" />
                )}
              </div>
              <div className="flex-1 overflow-hidden">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <h3 className="font-medium text-sm truncate">{participant.username}</h3>
                    <span className="h-2 w-2 rounded-full bg-orange-500 flex-shrink-0"></span>
                  </div>
                  <span className="text-xs text-lingstream-muted">
                    {formatDistanceToNow(new Date(conversation.lastMessage.timestamp), { addSuffix: true })}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <p className="text-xs text-lingstream-muted truncate">
                    {conversation.lastMessage.content}
                  </p>
                  {participant.isOnline && (
                    <span className="text-xs text-green-500 flex-shrink-0">• Online</span>
                  )}
                </div>
              </div>
              {conversation.unreadCount > 0 && (
                <div className="flex h-5 w-5 items-center justify-center rounded-full bg-lingstream-accent">
                  <span className="text-xs text-white">{conversation.unreadCount}</span>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
