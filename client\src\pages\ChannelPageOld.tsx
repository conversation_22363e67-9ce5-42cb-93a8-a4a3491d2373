import { useParams } from 'react-router-dom';
import { useState, useEffect, useRef } from 'react';
import { useVideos } from '@/context/VideoContext';
import { useAuth } from '@/context/AuthContext';
import Navbar from '@/components/layout/Navbar';
import VideoCard from '@/components/video/VideoCard';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';
import { MessageSquare, Mic, MicOff, Languages, Send } from 'lucide-react';

export default function ChannelPageOld() {
  const { channelId } = useParams<{ channelId: string }>();
  const { videos } = useVideos();
  const { currentUser } = useAuth();
  const [selectedTab, setSelectedTab] = useState("videos");
  const [messageText, setMessageText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [audioRecorder, setAudioRecorder] = useState<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const [userLanguage, setUserLanguage] = useState('en');

  // Find creator info from videos (in a real app would come from API)
  const creator = videos.find(video => video.creator.id === channelId)?.creator;

  // Filter videos by creator
  const creatorVideos = videos.filter(video => video.creator.id === channelId);

  // Languages available for translation
  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'zh', name: 'Chinese', flag: '🇨🇳' },
    { code: 'ru', name: 'Russian', flag: '🇷🇺' }
  ];

  useEffect(() => {
    if (creator) {
      document.title = `${creator.username} | LegalAid`;
    }
  }, [creator]);

  const handleChangeUserLanguage = (langCode: string) => {
    setUserLanguage(langCode);
  };

  const handleSendMessage = () => {
    if (!messageText.trim()) return;
    // In a real app, would send message to API
    setMessageText('');
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const recorder = new MediaRecorder(stream);

      audioChunksRef.current = [];
      recorder.ondataavailable = (e) => {
        audioChunksRef.current.push(e.data);
      };

      recorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/mp3' });
        // In a real app, would send the audio blob to API

        // Stop all audio tracks
        stream.getTracks().forEach(track => track.stop());
      };

      setAudioRecorder(recorder);
      recorder.start();
      setIsRecording(true);
    } catch (err) {
      console.error("Error accessing microphone:", err);
    }
  };

  const stopRecording = () => {
    if (audioRecorder) {
      audioRecorder.stop();
      setIsRecording(false);
      setAudioRecorder(null);
    }
  };

  if (!creator) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="container py-8 flex-1 flex items-center justify-center">
          <p>Channel not found</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="container py-6 flex-1">
        <div className="max-w-6xl mx-auto">
          {/* Channel Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center gap-4">
              <Avatar className="h-24 w-24">
                <AvatarImage src={creator.avatar} alt={creator.username} />
                <AvatarFallback>{creator.username[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-2xl font-bold">{creator.username}</h1>
                <p className="text-lingstream-muted">{creatorVideos.length} videos</p>
              </div>
            </div>

            <div className="flex gap-3">
              <Button onClick={() => setSelectedTab("chat")}>
                <MessageSquare className="mr-2 h-4 w-4" />
                Chat with Creator
              </Button>
              <Button variant="outline">Subscribe</Button>
            </div>
          </div>

          {/* Channel Content Tabs */}
          <Tabs value={selectedTab} onValueChange={setSelectedTab} defaultValue="videos">
            <TabsList className="mb-6">
              <TabsTrigger value="videos">Videos</TabsTrigger>
              <TabsTrigger value="chat">Chat</TabsTrigger>
              <TabsTrigger value="about">About</TabsTrigger>
            </TabsList>

            <TabsContent value="videos">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {creatorVideos.map((video) => (
                  <VideoCard key={video.id} video={video} />
                ))}
                {creatorVideos.length === 0 && (
                  <p className="col-span-full text-center py-8 text-lingstream-muted">
                    This channel has no videos yet.
                  </p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="chat">
              <div className="border border-gray-700 rounded-lg p-4 h-[500px] flex flex-col">
                {/* Chat language selection */}
                <div className="flex justify-end mb-4">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm" className="gap-2">
                        <Languages className="h-4 w-4" />
                        {languages.find(lang => lang.code === userLanguage)?.flag || '🌐'}
                        {languages.find(lang => lang.code === userLanguage)?.name || 'Language'}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuLabel>Select Language</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {languages.map((lang) => (
                        <DropdownMenuItem
                          key={lang.code}
                          className={userLanguage === lang.code ? "bg-lingstream-hover" : ""}
                          onClick={() => handleChangeUserLanguage(lang.code)}
                        >
                          <span className="mr-2">{lang.flag}</span>
                          {lang.name}
                          {userLanguage === lang.code && (
                            <span className="ml-2 h-2 w-2 rounded-full bg-lingstream-accent" />
                          )}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Chat messages */}
                <div className="flex-1 overflow-y-auto mb-4 space-y-4">
                  <div className="flex items-start gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={creator.avatar} alt={creator.username} />
                      <AvatarFallback>{creator.username[0]}</AvatarFallback>
                    </Avatar>
                    <div className="bg-gray-800 rounded-lg p-3 max-w-[80%]">
                      <p className="text-sm">Hi there! Thanks for visiting my channel. Feel free to ask any questions about my multilingual content!</p>
                    </div>
                  </div>

                  {currentUser && (
                    <div className="flex items-start gap-2 justify-end">
                      <div className="bg-lingstream-accent bg-opacity-20 rounded-lg p-3 max-w-[80%]">
                        <p className="text-sm">Hi! I really enjoyed your videos. How do you create content in so many languages?</p>
                      </div>
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={currentUser.avatar} alt={currentUser.username} />
                        <AvatarFallback>{currentUser.username[0]}</AvatarFallback>
                      </Avatar>
                    </div>
                  )}
                </div>

                {/* Input box with voice recording option */}
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className={isRecording ? "text-red-500 bg-red-500 bg-opacity-20" : ""}
                    onClick={isRecording ? stopRecording : startRecording}
                    title={isRecording ? "Stop recording" : "Start voice recording"}
                  >
                    {isRecording ? <MicOff className="h-5 w-5" /> : <Mic className="h-5 w-5" />}
                  </Button>

                  <Input
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    placeholder={isRecording ? "Recording..." : `Type your message in ${languages.find(lang => lang.code === userLanguage)?.name || "your language"}...`}
                    disabled={isRecording}
                    className="flex-1"
                  />

                  <Button onClick={handleSendMessage} disabled={!messageText.trim() || isRecording}>
                    <Send className="h-4 w-4 mr-2" />
                    Send
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="about">
              <div className="bg-lingstream-card p-6 rounded-lg">
                <h3 className="text-xl font-medium mb-4">About {creator.username}</h3>
                <p className="mb-4">
                  Multilingual content creator specializing in educational videos available in multiple languages.
                  Join me on this language learning journey and expand your horizons!
                </p>
                <div className="flex gap-4">
                  <div>
                    <h4 className="font-medium">Languages</h4>
                    <p className="text-lingstream-muted">English, Spanish, French, German, Japanese</p>
                  </div>
                  <div>
                    <h4 className="font-medium">Joined</h4>
                    <p className="text-lingstream-muted">January 2025</p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}
