import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify';
import { videoIdMappingService } from '../services';

/**
 * Middleware to transform video IDs in API responses
 * This intercepts all JSON responses and transforms any hash IDs to Engaxe IDs
 *
 * UPDATED: Now ensures that all videos use Engaxe IDs consistently as both ID and URL
 */
export function registerResponseTransformer(fastify: FastifyInstance) {
  fastify.addHook('onSend', async (request: FastifyRequest, reply: FastifyReply, payload: any) => {
    // Only process JSON responses
    if (reply.getHeader('content-type')?.toString().includes('application/json')) {
      try {
        // Skip if payload is empty
        if (!payload) {
          return payload;
        }

        // Parse the payload
        let data;
        try {
          data = JSON.parse(payload);
        } catch (parseError) {
          console.error('Error parsing JSON payload:', parseError);
          return payload;
        }

        // Check if the response contains video data
        let modified = false;

        console.log('Response transformer: Checking payload for video data');
        console.log('Response URL:', request.url);

        // Transform single video
        if (data.video && typeof data.video === 'object') {
          console.log('Found single video in response:', data.video.id, 'URL before:', data.video.url);
          data.video = await videoIdMappingService.transformVideoForClient(data.video);
          console.log('URL after transformation:', data.video.url);
          modified = true;
        }

        // Transform video array
        if (data.videos && Array.isArray(data.videos)) {
          console.log('Found video array in response with', data.videos.length, 'videos');
          if (data.videos.length > 0) {
            console.log('First video before:', data.videos[0].id, 'URL:', data.videos[0].url);
          }
          data.videos = await videoIdMappingService.transformVideosForClient(data.videos);
          if (data.videos.length > 0) {
            console.log('First video after:', data.videos[0].id, 'URL:', data.videos[0].url);
          }
          modified = true;
        }

        // Check for array of videos directly in the response (not in a 'videos' property)
        if (Array.isArray(data) && data.length > 0 &&
            typeof data[0] === 'object' && data[0] !== null &&
            'id' in data[0] && 'title' in data[0]) {
          console.log('Found direct array of videos in response with', data.length, 'videos');
          if (data.length > 0) {
            console.log('First video before:', data[0].id, 'URL:', data[0].url);
          }
          data = await videoIdMappingService.transformVideosForClient(data);
          if (data.length > 0) {
            console.log('First video after:', data[0].id, 'URL:', data[0].url);
          }
          modified = true;
        }

        // Return the transformed data if modified
        if (modified) {
          console.log('Transformed video IDs in response');
          return JSON.stringify(data);
        } else {
          console.log('No video data found in response or no transformation needed');
        }
      } catch (error) {
        // If there's an error, log it but don't block the response
        console.error('Error transforming video IDs in response:', error);
      }
    }

    // Return the original payload if no transformation was needed or if an error occurred
    return payload;
  });
}
