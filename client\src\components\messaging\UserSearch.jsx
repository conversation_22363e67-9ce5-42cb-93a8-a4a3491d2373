import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../../context/AuthContext';
import { debounce } from 'lodash';
import axios from 'axios';
import { Search } from 'lucide-react';
import { API_BASE_URL, SEARCH_DEBOUNCE_DELAY, DEFAULT_AVATAR_URL } from '../../config';

// For debugging
const DEBUG = true;

const UserSearch = ({ onSelectUser, className }) => {
  const { token, currentUser } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [recentUsers, setRecentUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchRef = useRef(null);

  if (DEBUG) console.log('UserSearch: Initialized with token:', token ? 'Token exists' : 'No token', 'User:', currentUser?.username);

  // Fetch recent chat users on component mount
  useEffect(() => {
    if (token) {
      fetchRecentUsers();
      if (DEBUG) console.log('UserSearch: Token available, fetching recent users');
    } else {
      if (DEBUG) console.log('UserSearch: No token available');
    }
  }, [token]);

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (searchRef.current && !searchRef.current.contains(event.target)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch recent chat users
  const fetchRecentUsers = async () => {
    try {
      setLoading(true);
      if (DEBUG) console.log('UserSearch: Fetching recent users with token:', token ? 'Token exists' : 'No token');

      const response = await axios.get(`${API_BASE_URL}/users/recent`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (DEBUG) console.log('UserSearch: Recent users response:', response.data);

      if (response.data.success) {
        setRecentUsers(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching recent users:', error);
      if (DEBUG) console.log('UserSearch: Error details:', error.response?.data || error.message);
    } finally {
      setLoading(false);
    }
  };

  // Debounced search function
  const debouncedSearch = useRef(
    debounce(async (query) => {
      if (!query || query.length < 2) {
        setSearchResults([]);
        return;
      }

      try {
        setLoading(true);
        if (DEBUG) console.log('UserSearch: Searching users with query:', query, 'token:', token ? 'Token exists' : 'No token');

        const response = await axios.get(`${API_BASE_URL}/users/search`, {
          params: { query },
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (DEBUG) console.log('UserSearch: Search response:', response.data);

        if (response.data.success) {
          setSearchResults(response.data.data);
        }
      } catch (error) {
        console.error('Error searching users:', error);
        if (DEBUG) console.log('UserSearch: Error details:', error.response?.data || error.message);
        setSearchResults([]);
      } finally {
        setLoading(false);
      }
    }, SEARCH_DEBOUNCE_DELAY)
  ).current;

  // Handle search input change
  const handleSearchChange = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    debouncedSearch(query);
  };

  // Handle search button click
  const handleSearchClick = () => {
    debouncedSearch(searchQuery);
  };

  // Handle key down in search input
  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      debouncedSearch(searchQuery);
    }
  };

  // Handle user selection
  const handleSelectUser = (user) => {
    setSearchQuery('');
    setShowResults(false);
    onSelectUser(user);
  };

  // Format time for display
  const formatTime = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now - date) / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Get user display name
  const getUserDisplayName = (user) => {
    return user.displayName || user.username || `${user.firstName || ''} ${user.lastName || ''}`.trim();
  };

  // Get user avatar or placeholder
  const getUserAvatar = (user) => {
    return user.avatar || `${DEFAULT_AVATAR_URL}?name=${encodeURIComponent(getUserDisplayName(user))}`;
  };

  return (
    <div className={`user-search ${className || ''}`} ref={searchRef}>
      <div className="search-input-container">
        <input
          type="text"
          className="search-input"
          placeholder="Search users..."
          value={searchQuery}
          onChange={handleSearchChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowResults(true)}
        />
        <button
          className="search-button"
          onClick={handleSearchClick}
          aria-label="Search"
        >
          <Search className="search-icon" size={18} />
        </button>
        {loading && <div className="search-loading">Loading...</div>}
      </div>

      {showResults && (
        <div className="search-results">
          {searchQuery.length >= 2 ? (
            <>
              <div className="search-results-header">Search Results</div>
              {searchResults.length > 0 ? (
                searchResults.map((user) => (
                  <div
                    key={user.id}
                    className="user-item"
                    onClick={() => handleSelectUser(user)}
                  >
                    <div className="user-avatar">
                      <img src={getUserAvatar(user)} alt={getUserDisplayName(user)} />
                      <span className={`status-indicator ${user.presence?.isOnline ? 'online' : 'offline'}`}></span>
                    </div>
                    <div className="user-info">
                      <div className="user-name">{getUserDisplayName(user)}</div>
                      <div className="user-username">@{user.username}</div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-results">No users found</div>
              )}
            </>
          ) : (
            <>
              <div className="search-results-header">Recent Chats</div>
              {recentUsers.length > 0 ? (
                recentUsers.map((user) => (
                  <div
                    key={user.id}
                    className="user-item"
                    onClick={() => handleSelectUser(user)}
                  >
                    <div className="user-avatar">
                      <img src={getUserAvatar(user)} alt={getUserDisplayName(user)} />
                      <span className={`status-indicator ${user.presence?.isOnline ? 'online' : 'offline'}`}></span>
                    </div>
                    <div className="user-info">
                      <div className="user-name">{getUserDisplayName(user)}</div>
                      <div className="user-username">@{user.username}</div>
                    </div>
                    <div className="last-message-time">
                      {formatTime(user.lastMessageAt)}
                    </div>
                  </div>
                ))
              ) : (
                <div className="no-results">No recent chats</div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default UserSearch;
