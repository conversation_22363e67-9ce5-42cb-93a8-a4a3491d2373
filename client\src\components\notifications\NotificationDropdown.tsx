
import { useEffect, useRef } from 'react';
import { useNotifications } from '@/context/NotificationContext';
import { Bell, MessageCircle, Award, Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface NotificationDropdownProps {
  onClose: () => void;
}

export default function NotificationDropdown({ onClose }: NotificationDropdownProps) {
  const { notifications, markAllAsRead } = useNotifications();
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'mention':
        return <MessageCircle className="h-4 w-4 text-blue-400" />;
      case 'like':
        return <Heart className="h-4 w-4 text-red-400" />;
      case 'comment':
        return <MessageCircle className="h-4 w-4 text-green-400" />;
      case 'achievement':
        return <Award className="h-4 w-4 text-yellow-400" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  return (
    <div 
      ref={dropdownRef}
      className="absolute right-0 w-80 mt-2 bg-lingstream-card border border-gray-700 rounded-md shadow-lg z-50 animate-fade-in"
    >
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Notifications</h3>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={markAllAsRead}
            className="text-xs hover:bg-gray-700"
          >
            Mark all as read
          </Button>
        </div>
      </div>
      <div className="max-h-[350px] overflow-auto">
        {notifications.length === 0 ? (
          <div className="p-4 text-center text-lingstream-muted">
            No notifications yet
          </div>
        ) : (
          <ul>
            {notifications.map((notification) => (
              <li 
                key={notification.id} 
                className={`p-3 border-b border-gray-700 hover:bg-lingstream-hover ${!notification.read ? 'bg-gray-800/50' : ''}`}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    {notification.user ? (
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={notification.user.avatar} />
                        <AvatarFallback>{notification.user.username[0]}</AvatarFallback>
                      </Avatar>
                    ) : (
                      <div className="h-8 w-8 flex items-center justify-center rounded-full bg-gray-700">
                        {getNotificationIcon(notification.type)}
                      </div>
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm">
                      <span className="font-semibold">{notification.title}</span>
                    </p>
                    <p className="text-xs text-lingstream-muted">{notification.content}</p>
                    <p className="text-xs text-lingstream-muted mt-1">
                      {formatDistanceToNow(new Date(notification.timestamp), { addSuffix: true })}
                    </p>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
