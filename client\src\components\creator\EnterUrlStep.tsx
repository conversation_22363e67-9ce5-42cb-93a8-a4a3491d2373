import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Code, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import api from '@/services/api';

interface EnterUrlStepProps {
  onNext: (url: string, title: string, description: string, thumbnail: string, category: string) => void;
  onCancel: () => void;
}

export default function EnterUrlStep({ onNext, onCancel }: EnterUrlStepProps) {
  const { toast } = useToast();
  const [engaxeUrl, setEngaxeUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Function to extract Engaxe video ID from URL
  const extractEngaxeVideoId = (url: string): string | null => {
    if (!url) return null;

    // Trim the input
    const trimmedUrl = url.trim();

    // If it's just a video ID (no slashes, dots, or protocol)
    if (!trimmedUrl.includes('/') && !trimmedUrl.includes('.') && !trimmedUrl.includes(':')) {
      if (trimmedUrl.length > 3 && trimmedUrl.length < 20 && /^[a-zA-Z0-9]+$/.test(trimmedUrl)) {
        console.log(`Input appears to be a direct video ID: ${trimmedUrl}`);
        return trimmedUrl;
      }
    }

    // Engaxe URL patterns
    const patterns = [
      // Format: engaxe.com/videos/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/videos\/([^/?]+)/i,
      // Format: engaxe.com/v/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([^/?]+)/i,
      // Format: engaxe.com/watch/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/watch\/([^/?]+)/i,
      // Format: engaxe.com/embed/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/embed\/([^/?]+)/i
    ];

    // Try each pattern
    for (const pattern of patterns) {
      const match = trimmedUrl.match(pattern);
      if (match && match[1]) {
        console.log(`Successfully extracted video ID: ${match[1]} using pattern: ${pattern}`);
        return match[1];
      }
    }

    // If no pattern matches, try a simple extraction as a fallback
    try {
      // Try to parse as URL first
      const urlObj = new URL(trimmedUrl.startsWith('http') ? trimmedUrl : `https://${trimmedUrl}`);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
          console.log(`Using URL path extraction, got video ID: ${lastPart}`);
          return lastPart;
        }
      }
    } catch (error) {
      // If URL parsing fails, try simple string splitting
      const parts = trimmedUrl.split('/');
      const lastPart = parts[parts.length - 1];
      if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
        console.log(`Using fallback string extraction, got video ID: ${lastPart}`);
        return lastPart;
      }
    }

    console.log('Failed to extract video ID from input');
    return null;
  };

  // Function to normalize Engaxe URL
  const normalizeEngaxeUrl = (url: string): string => {
    // Trim the input
    const trimmedUrl = url.trim();

    // Extract the video ID
    const videoId = extractEngaxeVideoId(trimmedUrl);

    if (videoId) {
      console.log(`Normalized URL: Using extracted video ID ${videoId}`);
      // Just return the video ID - we'll store only the ID in the database
      return videoId;
    }

    // If we couldn't extract a video ID, return the original URL
    console.log(`Couldn't extract video ID, using original URL: ${trimmedUrl}`);
    return trimmedUrl;
  };

  const handleFetchMetadata = async () => {
    if (!engaxeUrl.trim()) {
      toast({
        title: "URL Required",
        description: "Please enter an Engaxe URL to continue",
        variant: "destructive"
      });
      return;
    }

    // Normalize the URL
    const normalizedUrl = normalizeEngaxeUrl(engaxeUrl);
    console.log('Normalized URL:', normalizedUrl);

    // Update the URL field with the normalized version
    setEngaxeUrl(normalizedUrl);

    setIsLoading(true);

    try {
      console.log('Fetching metadata for URL:', normalizedUrl);

      // Call the real metadata API
      const response = await api.get(`/metadata/video?url=${encodeURIComponent(normalizedUrl)}`);
      console.log('Metadata API response:', response.data);

      if (!response.data.success) {
        throw new Error(response.data.message || 'Failed to fetch metadata');
      }

      const metadata = response.data.metadata;
      console.log('Parsed metadata:', metadata);

      // Validate that we have the required fields
      if (!metadata.title) {
        console.warn('Missing title in metadata, using default');
      }
      if (!metadata.thumbnailUrl) {
        console.warn('Missing thumbnailUrl in metadata, using default');
      }

      // Extract video ID for fallback purposes
      const urlParts = normalizedUrl.split('/');
      const videoId = urlParts[urlParts.length - 1] || 'unknown';

      // Ensure we have a valid thumbnail URL
      const thumbnailUrl = metadata.thumbnailUrl || `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`;

      // Call the onNext callback with the fetched data
      onNext(
        normalizedUrl,
        metadata.title || `Engaxe Video ${videoId}`,
        metadata.description || '',
        thumbnailUrl,
        metadata.category || 'Education'
      );

      // Show success message
      toast({
        title: "Metadata Fetched",
        description: `Successfully fetched metadata for "${metadata.title || 'Untitled Video'}"`,
        variant: "default"
      });
    } catch (error: any) {
      console.error('Error fetching metadata:', error);
      toast({
        title: "Error Fetching Metadata",
        description: error.message || "Could not fetch video details from the provided URL",
        variant: "destructive"
      });

      // Extract video ID from URL
      const urlParts = normalizedUrl.split('/');
      const videoId = urlParts[urlParts.length - 1] || 'unknown';

      // Fallback to default values if metadata fetch fails
      onNext(
        normalizedUrl,
        `Engaxe Video ${videoId}`,
        'No description available',
        `https://placehold.co/480x360/333333/FFFFFF?text=Engaxe+Video+${videoId}`,
        'Education'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="engaxeUrl" className="text-sm font-medium">Engaxe URL</label>
          <Input
            id="engaxeUrl"
            value={engaxeUrl}
            onChange={(e) => setEngaxeUrl(e.target.value)}
            placeholder="https://engaxe.com/v/XLcMq2 or just XLcMq2"
            disabled={isLoading}
          />
        </div>

        <div className="flex items-center text-xs text-lingstream-muted mt-2">
          <Code className="h-4 w-4 mr-1" />
          <span>Enter an Engaxe URL or just the video ID (e.g., XLcMq2) to fetch video metadata</span>
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          onClick={handleFetchMetadata}
          disabled={!engaxeUrl.trim() || isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Fetching...
            </>
          ) : (
            'Next'
          )}
        </Button>
      </div>
    </div>
  );
}
