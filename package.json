{"name": "lawengaxe-main", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "npm run install:server && npm run install:client && npm run setup:env", "install:server": "cd server && npm install && cd..", "install:client": "cd client && npm install && cd..", "install:python": "pip install -r server/requirements.txt", "setup:env": "node scripts/setup-environment.js", "dev": "concurrently \"cd server && npm run dev\" \"cd client && npm run dev\"", "dev:server": "cd server && npm run dev", "dev:client": "cd client && npm run dev", "dev:server:port": "cd server && npm run dev -- --port", "dev:client:port": "cd client && npm run dev -- --port", "start": "concurrently \"cd server && npm run start\" \"cd client && npm run build && npm run preview\"", "start:server": "cd server && npm run start", "start:client": "cd client && npm run build && npm run preview", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "clean": "npm run clean:client && npm run clean:server", "clean:client": "cd client && rm -rf node_modules dist", "clean:server": "cd server && rm -rf node_modules dist", "organize": "node scripts/organize-data.js", "postinstall": "npm run setup:env"}, "keywords": ["legal", "ai", "translation", "<PERSON><PERSON><PERSON>", "multilingual"], "author": "LawEngaxe Team", "license": "ISC", "description": "LawEngaxe - AI-powered multilingual legal assistance platform", "dependencies": {"axios": "^1.9.0", "react-intersection-observer": "^9.16.0"}, "devDependencies": {"concurrently": "^9.1.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}