import { FastifyRequest, FastifyReply } from 'fastify';
import logService from '../services/log.service';
import { ILog } from '../models/log.model';
import { AuthenticatedUser } from '../types/user';

/**
 * Log controller for handling log-related requests
 */
export class LogController {
  /**
   * Create a new log entry
   */
  async createLog(
    request: FastifyRequest<{
      Body: Partial<ILog>;
    }>,
    reply: FastifyReply
  ) {
    try {
      const logData = request.body;
      const user = request.user as AuthenticatedUser;
      const userIp = request.ip;
      const userAgent = request.headers['user-agent'] || 'Unknown';

      const log = await logService.createLog(logData, user.id, userIp, userAgent);

      return reply.code(201).send({
        success: true,
        message: 'Log created successfully',
        log: {
          id: log.id,
          action: log.action,
          category: log.category,
          userId: log.userId,
          userIp: log.userIp,
          userAgent: log.userAgent,
          resourceType: log.resourceType,
          resourceId: log.resourceId,
          status: log.status,
          severity: log.severity,
          createdAt: log.createdAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      throw error;
    }
  }

  /**
   * Get log by ID
   */
  async getLogById(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const log = await logService.getLogById(id);

      return reply.code(200).send({
        success: true,
        log: {
          id: log.id,
          action: log.action,
          category: log.category,
          userId: log.userId,
          userIp: log.userIp,
          userAgent: log.userAgent,
          resourceType: log.resourceType,
          resourceId: log.resourceId,
          previousState: log.previousState,
          newState: log.newState,
          status: log.status,
          details: log.details,
          severity: log.severity,
          createdAt: log.createdAt,
          updatedAt: log.updatedAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      throw error;
    }
  }

  /**
   * Get all logs with filtering and pagination
   */
  async getAllLogs(
    request: FastifyRequest<{
      Querystring: {
        page?: number;
        limit?: number;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
        action?: string;
        category?: string;
        userId?: string;
        resourceType?: string;
        resourceId?: string;
        status?: 'success' | 'failure' | 'warning' | 'info';
        severity?: 'low' | 'medium' | 'high' | 'critical';
        startDate?: string;
        endDate?: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const {
        page,
        limit,
        sortBy,
        sortOrder,
        action,
        category,
        userId,
        resourceType,
        resourceId,
        status,
        severity,
        startDate,
        endDate,
      } = request.query;

      const result = await logService.getAllLogs({
        page,
        limit,
        sortBy,
        sortOrder,
        action,
        category,
        userId,
        resourceType,
        resourceId,
        status,
        severity,
        startDate,
        endDate,
      });

      return reply.code(200).send({
        success: true,
        logs: result.logs.map(log => ({
          id: log.id,
          action: log.action,
          category: log.category,
          userId: log.userId,
          userIp: log.userIp,
          userAgent: log.userAgent,
          resourceType: log.resourceType,
          resourceId: log.resourceId,
          status: log.status,
          severity: log.severity,
          details: log.details,
          createdAt: log.createdAt,
        })),
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          pages: result.pages,
        },
      });
    } catch (error) {
      request.log.error(error);
      throw error;
    }
  }

  /**
   * Delete a log by ID
   */
  async deleteLog(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const user = request.user as AuthenticatedUser;

      const result = await logService.deleteLog(id, user.id);

      return reply.code(200).send(result);
    } catch (error) {
      request.log.error(error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const logController = new LogController();
export default logController;
