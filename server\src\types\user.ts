/**
 * User-related type definitions
 */

// Authentication
export interface AuthenticatedUser {
  id: string;
  username?: string;
  email?: string;
  roles?: string[];
  permissions?: string[];
}

// Request bodies
export interface RegisterUserBody {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  displayName?: string;
}

export interface LoginUserBody {
  email: string;
  password: string;
}

export interface ChangePasswordBody {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateUserStatusBody {
  status: 'active' | 'pending' | 'suspended' | 'banned';
  statusReason?: string;
}

// Request params
export interface UserIdParams {
  id: string;
}

// Query parameters
export interface GetAllUsersQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  role?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Extended Fastify types
export interface FastifyAuthInstance {
  generateAccessToken: (payload: any) => string;
  generateRefreshToken: (payload: any) => string;
  hasPermission: (userId: string, permission: string) => Promise<boolean>;
}
