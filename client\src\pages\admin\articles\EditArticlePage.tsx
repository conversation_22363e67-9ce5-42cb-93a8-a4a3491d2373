import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Home, ChevronRight, Save, ArrowLeft, Image, FileText, Tag, Calendar, Clock, Eye, EyeOff, Trash2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Mock data for a single article
const mockArticle = {
  id: '1',
  title: 'Getting Started with React',
  slug: 'getting-started-with-react',
  content: `
# Getting Started with React

React is a JavaScript library for building user interfaces. It's maintained by Facebook and a community of individual developers and companies.

## Why React?

React makes it painless to create interactive UIs. Design simple views for each state in your application, and React will efficiently update and render just the right components when your data changes.

## Component-Based

Build encapsulated components that manage their own state, then compose them to make complex UIs.

## Learn Once, Write Anywhere

You can develop new features in React without rewriting existing code. React can also render on the server using Node and power mobile apps using React Native.
  `,
  excerpt: 'Learn the basics of React, a JavaScript library for building user interfaces maintained by Facebook.',
  category: 'Education',
  tags: ['react', 'javascript', 'frontend', 'web development'],
  author: 'John Doe',
  date: '2023-05-15',
  status: 'Published',
  featuredImage: '/placeholder-image.jpg',
  seo: {
    title: 'Getting Started with React - A Beginner\'s Guide',
    description: 'Learn the basics of React, a JavaScript library for building user interfaces maintained by Facebook.',
    keywords: 'react, javascript, frontend, web development, beginner'
  },
  isCommentingEnabled: true,
  isFeatured: true
};

// Mock categories
const categories = [
  { id: '1', name: 'Education' },
  { id: '2', name: 'Programming' },
  { id: '3', name: 'Design' },
  { id: '4', name: 'Performance' },
  { id: '5', name: 'Technology' }
];

export default function EditArticlePage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);

  // Form state
  const [article, setArticle] = useState(mockArticle);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch article data
  useEffect(() => {
    // In a real app, you would fetch the article data from an API
    // For this example, we'll use the mock data
    setIsLoading(false);
  }, [id]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setArticle(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSelectChange = (name: string, value: string) => {
    setArticle(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setArticle(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleSeoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setArticle(prev => ({
      ...prev,
      seo: {
        ...prev.seo,
        [name]: value
      }
    }));
  };

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const tagsString = e.target.value;
    const tagsArray = tagsString.split(',').map(tag => tag.trim());
    setArticle(prev => ({
      ...prev,
      tags: tagsArray
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!article.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!article.content.trim()) {
      newErrors.content = 'Content is required';
    }

    if (!article.category.trim()) {
      newErrors.category = 'Category is required';
    }

    if (!article.excerpt.trim()) {
      newErrors.excerpt = 'Excerpt is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      // In a real app, you would send the data to an API
      // For this example, we'll simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setShowSuccessAlert(true);
      setTimeout(() => {
        setShowSuccessAlert(false);
      }, 3000);
    } catch (error) {
      console.error('Error saving article:', error);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen bg-background overflow-hidden">
        <AdminSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <AdminHeader />
          <main className="flex-1 overflow-y-auto p-6">
            <div className="max-w-7xl mx-auto">
              <div className="flex items-center justify-center h-full">
                <p>Loading article...</p>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Edit Article</h1>
              <Button variant="outline" onClick={() => navigate('/admin/articles')} className="gap-2">
                <ArrowLeft className="h-4 w-4" />
                Back to Articles
              </Button>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Content
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/articles" className="text-muted-foreground hover:text-foreground">
                Articles
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Edit Article</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  Article has been saved successfully.
                </AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit}>
              <Tabs defaultValue="content" className="w-full">
                <TabsList className="grid grid-cols-4 mb-6">
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="media">Media</TabsTrigger>
                  <TabsTrigger value="seo">SEO</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>

                <TabsContent value="content">
                  <Card>
                    <CardHeader>
                      <CardTitle>Article Content</CardTitle>
                      <CardDescription>Edit the main content of your article</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="title" className={errors.title ? 'text-destructive' : ''}>
                          Title {errors.title && <span className="text-destructive text-sm">({errors.title})</span>}
                        </Label>
                        <Input
                          id="title"
                          name="title"
                          value={article.title}
                          onChange={handleInputChange}
                          className={errors.title ? 'border-destructive' : ''}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="slug">Slug (URL)</Label>
                        <Input
                          id="slug"
                          name="slug"
                          value={article.slug}
                          onChange={handleInputChange}
                          placeholder="article-url-slug"
                        />
                        <p className="text-sm text-muted-foreground">
                          The slug is the URL-friendly version of the title. It is usually all lowercase and contains only letters, numbers, and hyphens.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="content" className={errors.content ? 'text-destructive' : ''}>
                          Content {errors.content && <span className="text-destructive text-sm">({errors.content})</span>}
                        </Label>
                        <Textarea
                          id="content"
                          name="content"
                          value={article.content}
                          onChange={handleInputChange}
                          className={`min-h-[300px] font-mono ${errors.content ? 'border-destructive' : ''}`}
                        />
                        <p className="text-sm text-muted-foreground">
                          Write your article content using Markdown. You can use headings, lists, links, images, and more.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="excerpt" className={errors.excerpt ? 'text-destructive' : ''}>
                          Excerpt {errors.excerpt && <span className="text-destructive text-sm">({errors.excerpt})</span>}
                        </Label>
                        <Textarea
                          id="excerpt"
                          name="excerpt"
                          value={article.excerpt}
                          onChange={handleInputChange}
                          className={errors.excerpt ? 'border-destructive' : ''}
                        />
                        <p className="text-sm text-muted-foreground">
                          A short summary of the article that will be displayed in article listings.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="media">
                  <Card>
                    <CardHeader>
                      <CardTitle>Media</CardTitle>
                      <CardDescription>Manage article images and media</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="featuredImage">Featured Image</Label>
                        <div className="border rounded-md p-4">
                          <div className="flex items-center justify-center mb-4">
                            {article.featuredImage ? (
                              <div className="relative">
                                <img
                                  src={article.featuredImage}
                                  alt="Featured"
                                  className="max-h-[200px] rounded-md object-cover"
                                />
                                <Button
                                  variant="destructive"
                                  size="icon"
                                  className="absolute top-2 right-2 h-8 w-8"
                                  type="button"
                                  onClick={() => setArticle(prev => ({ ...prev, featuredImage: '' }))}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <div className="flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-md text-muted-foreground">
                                <Image className="h-10 w-10 mb-2" />
                                <p>No featured image selected</p>
                              </div>
                            )}
                          </div>
                          <div className="flex justify-center">
                            <Button type="button" variant="outline" className="gap-2">
                              <Image className="h-4 w-4" />
                              Select Image
                            </Button>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          The featured image will be displayed at the top of the article and in article listings.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="seo">
                  <Card>
                    <CardHeader>
                      <CardTitle>SEO Settings</CardTitle>
                      <CardDescription>Optimize your article for search engines</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="seoTitle">SEO Title</Label>
                        <Input
                          id="seoTitle"
                          name="title"
                          value={article.seo.title}
                          onChange={handleSeoChange}
                          placeholder="SEO-friendly title"
                        />
                        <p className="text-sm text-muted-foreground">
                          The title that will appear in search engine results. If left empty, the article title will be used.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="seoDescription">Meta Description</Label>
                        <Textarea
                          id="seoDescription"
                          name="description"
                          value={article.seo.description}
                          onChange={handleSeoChange}
                          placeholder="Brief description for search results"
                        />
                        <p className="text-sm text-muted-foreground">
                          A short description that will appear in search engine results. Keep it under 160 characters.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="seoKeywords">Meta Keywords</Label>
                        <Input
                          id="seoKeywords"
                          name="keywords"
                          value={article.seo.keywords}
                          onChange={handleSeoChange}
                          placeholder="keyword1, keyword2, keyword3"
                        />
                        <p className="text-sm text-muted-foreground">
                          Keywords related to your article, separated by commas.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="settings">
                  <Card>
                    <CardHeader>
                      <CardTitle>Article Settings</CardTitle>
                      <CardDescription>Configure article properties and publishing options</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="category" className={errors.category ? 'text-destructive' : ''}>
                          Category {errors.category && <span className="text-destructive text-sm">({errors.category})</span>}
                        </Label>
                        <Select
                          value={article.category}
                          onValueChange={(value) => handleSelectChange('category', value)}
                        >
                          <SelectTrigger id="category" className={errors.category ? 'border-destructive' : ''}>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map(category => (
                              <SelectItem key={category.id} value={category.name}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="tags">Tags</Label>
                        <Input
                          id="tags"
                          name="tags"
                          value={article.tags.join(', ')}
                          onChange={handleTagsChange}
                          placeholder="tag1, tag2, tag3"
                        />
                        <div className="flex flex-wrap gap-2 mt-2">
                          {article.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="gap-1">
                              <Tag className="h-3 w-3" />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Enter tags separated by commas.
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="author">Author</Label>
                        <Input
                          id="author"
                          name="author"
                          value={article.author}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="date">Publication Date</Label>
                        <Input
                          id="date"
                          name="date"
                          type="date"
                          value={article.date}
                          onChange={handleInputChange}
                        />
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <Label htmlFor="status">Status</Label>
                        <Select
                          value={article.status}
                          onValueChange={(value) => handleSelectChange('status', value)}
                        >
                          <SelectTrigger id="status">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Published">Published</SelectItem>
                            <SelectItem value="Draft">Draft</SelectItem>
                            <SelectItem value="Scheduled">Scheduled</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isFeatured"
                          checked={article.isFeatured}
                          onCheckedChange={(checked) => handleSwitchChange('isFeatured', checked)}
                        />
                        <Label htmlFor="isFeatured">Featured Article</Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isCommentingEnabled"
                          checked={article.isCommentingEnabled}
                          onCheckedChange={(checked) => handleSwitchChange('isCommentingEnabled', checked)}
                        />
                        <Label htmlFor="isCommentingEnabled">Enable Comments</Label>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

              <div className="mt-6 flex justify-end gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate('/admin/articles')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSaving}
                  className="gap-2"
                >
                  {isSaving ? (
                    <>Saving...</>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      Save Article
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </main>
      </div>
    </div>
  );
}
