import { config } from 'dotenv';
import mongoose from 'mongoose';
import { connectDB } from '../config/database';
import { PermissionModel, RoleModel, UserModel } from '../models';
import bcrypt from 'bcrypt';

// Load environment variables
config();

/**
 * Setup RBAC system with initial permissions, roles, and admin user
 */
async function setupRBAC() {
  try {
    console.log('🔑 Setting up RBAC system...');

    // Connect to the database
    await connectDB();

    // Create permissions
    console.log('Creating permissions...');
    const permissions = [
      // User permissions
      {
        name: 'Create User',
        description: 'Ability to create new user accounts',
        code: 'user:create',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'create',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Read User',
        description: 'Ability to view user account details',
        code: 'user:read',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'read',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Update User',
        description: 'Ability to update user account details',
        code: 'user:update',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'update',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Delete User',
        description: 'Ability to delete user accounts',
        code: 'user:delete',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'delete',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },

      // Role permissions
      {
        name: 'Create Role',
        description: 'Ability to create new roles',
        code: 'role:create',
        category: 'rbac',
        isActive: true,
        resourceType: 'role',
        action: 'create',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Read Role',
        description: 'Ability to view roles',
        code: 'role:read',
        category: 'rbac',
        isActive: true,
        resourceType: 'role',
        action: 'read',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Update Role',
        description: 'Ability to update roles',
        code: 'role:update',
        category: 'rbac',
        isActive: true,
        resourceType: 'role',
        action: 'update',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Delete Role',
        description: 'Ability to delete roles',
        code: 'role:delete',
        category: 'rbac',
        isActive: true,
        resourceType: 'role',
        action: 'delete',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Assign Role',
        description: 'Ability to assign roles to users',
        code: 'role:assign',
        category: 'rbac',
        isActive: true,
        resourceType: 'role',
        action: 'assign',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },

      // Permission permissions
      {
        name: 'Create Permission',
        description: 'Ability to create new permissions',
        code: 'permission:create',
        category: 'rbac',
        isActive: true,
        resourceType: 'permission',
        action: 'create',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Read Permission',
        description: 'Ability to view permissions',
        code: 'permission:read',
        category: 'rbac',
        isActive: true,
        resourceType: 'permission',
        action: 'read',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Update Permission',
        description: 'Ability to update permissions',
        code: 'permission:update',
        category: 'rbac',
        isActive: true,
        resourceType: 'permission',
        action: 'update',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Delete Permission',
        description: 'Ability to delete permissions',
        code: 'permission:delete',
        category: 'rbac',
        isActive: true,
        resourceType: 'permission',
        action: 'delete',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Assign Permission',
        description: 'Ability to assign permissions to users',
        code: 'permission:assign',
        category: 'rbac',
        isActive: true,
        resourceType: 'permission',
        action: 'assign',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
    ];

    // Clear existing permissions
    await PermissionModel.deleteMany({});

    // Insert permissions
    await PermissionModel.insertMany(permissions);
    console.log(`✅ Created ${permissions.length} permissions`);

    // Get permission codes
    const permissionCodes = permissions.map(p => p.code);

    // Create roles
    console.log('Creating roles...');
    const roles = [
      {
        name: 'Administrator',
        description: 'Full system access with all permissions',
        code: 'admin',
        permissions: permissionCodes,
        isSystem: true,
        isActive: true,
        priority: 10,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'User',
        description: 'Standard user with basic permissions',
        code: 'user',
        permissions: ['user:read'],
        isSystem: true,
        isActive: true,
        priority: 100,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
    ];

    // Clear existing roles
    await RoleModel.deleteMany({});

    // Insert roles
    const createdRoles = await RoleModel.insertMany(roles);
    console.log(`✅ Created ${roles.length} roles`);

    // Get admin role ID
    const adminRole = createdRoles.find(r => r.code === 'admin');
    const userRole = createdRoles.find(r => r.code === 'user');

    if (!adminRole || !userRole) {
      throw new Error('Failed to create roles');
    }

    // Create admin user
    console.log('Creating admin user...');

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);

    // Clear existing users
    await UserModel.deleteMany({});

    // Create admin user
    const adminUser = new UserModel({
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      displayName: 'System Admin',
      status: 'active',
      emailVerified: true,
      phoneVerified: true,
      roles: [adminRole.id],
      permissions: [],
      createdBy: 'system',
      updatedBy: 'system',
    });

    await adminUser.save();
    console.log('✅ Created admin user');

    // Create regular user
    console.log('Creating regular user...');

    // Hash password
    const userPassword = await bcrypt.hash('user123', salt);

    // Create regular user
    const regularUser = new UserModel({
      username: 'user',
      email: '<EMAIL>',
      password: userPassword,
      firstName: 'Regular',
      lastName: 'User',
      displayName: 'Regular User',
      status: 'active',
      emailVerified: true,
      phoneVerified: false,
      roles: [userRole.id],
      permissions: [],
      createdBy: 'system',
      updatedBy: 'system',
    });

    await regularUser.save();
    console.log('✅ Created regular user');

    console.log('🎉 RBAC setup completed successfully!');
    console.log('Admin credentials:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: admin123');
    console.log('User credentials:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: user123');

    // Close the database connection
    await mongoose.connection.close();
    console.log('📝 Database connection closed');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up RBAC:', error);

    // Close the database connection
    await mongoose.connection.close();
    console.log('📝 Database connection closed');

    process.exit(1);
  }
}

// Run the setup
setupRBAC();
