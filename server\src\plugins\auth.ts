import { FastifyInstance, FastifyPluginAsync, FastifyRequest } from 'fastify';
import { JwtPayload } from 'jsonwebtoken';

// Define custom JWT payload interface
interface CustomJwtPayload extends JwtPayload {
  id: string;
  email: string;
  type?: string;
  roles?: string[];
  permissions?: string[];
}
import fastifyPlugin from 'fastify-plugin';
import jwt from '@fastify/jwt';
import { UserModel } from '../models';
import permissionService from '../services/permission.service';
import { createNotFoundError, createUnauthorizedError, ErrorCodes } from '../utils/errors';

/**
 * Authentication plugin for Fastify
 * Sets up JWT authentication and adds helper methods
 */
const authPlugin: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  // Register JWT plugin
  await fastify.register(jwt, {
    secret: process.env.JWT_SECRET || 'default_secret_change_this',
    sign: {
      expiresIn: process.env.JWT_ACCESS_TOKEN_EXPIRES_IN || '15m',
    },
  });

  // Add decorator to generate access token
  fastify.decorate('generateAccessToken', function(this: FastifyInstance, payload: any) {
    return this.jwt.sign(payload);
  });

  // Add decorator to generate refresh token
  fastify.decorate('generateRefreshToken', function(this: FastifyInstance, payload: any) {
    return this.jwt.sign(
      { ...payload, type: 'refresh' },
      { expiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRES_IN || '7d' }
    );
  });

  // Add decorator to verify refresh token
  fastify.decorate('verifyRefreshToken', async function(this: FastifyInstance, token: string) {
    try {
      const decoded = this.jwt.verify(token) as CustomJwtPayload;

      // Check if it's a refresh token
      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      // Get user from database
      const user = await UserModel.findOne({
        id: decoded.id,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Check if user is active
      if (user.status !== 'active') {
        throw new Error(`Account is ${user.status}. Please contact support.`);
      }

      return {
        id: user.id,
        email: user.email,
        roles: user.roles,
        permissions: user.permissions,
      };
    } catch (error) {
      throw error;
    }
  });

  // Add decorator to get current user
  fastify.decorate('getCurrentUser', async function(this: FastifyInstance, request: any) {
    try {
      const userId = request.user.id;

      // Get user from database
      const user = await UserModel.findOne({
        id: userId,
        deletedAt: { $exists: false },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Check if user is active
      if (user.status !== 'active') {
        throw new Error(`Account is ${user.status}. Please contact support.`);
      }

      return user;
    } catch (error) {
      throw error;
    }
  });

  // Add decorator to check if user has permission
  fastify.decorate('hasPermission', async function(this: FastifyInstance, userId: string, permissionCode: string) {
    return await permissionService.userHasPermission(userId, permissionCode);
  });
};

export default fastifyPlugin(authPlugin);
