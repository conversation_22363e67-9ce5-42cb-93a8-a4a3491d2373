import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import logController from '../controllers/log.controller';
import {
  createLogSchema,
  getAllLogsSchema,
  getLogByIdSchema,
  deleteLogSchema,
} from '../schemas/log.schema';
import { authenticate } from '../middleware/auth';
import { checkPermission } from '../middleware/rbac';

/**
 * Log routes
 */
export default async function logRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // All log routes require authentication
  fastify.register(async (fastify: FastifyInstance) => {
    // Apply authentication middleware to all routes in this context
    fastify.addHook('preHandler', authenticate);

    // Routes for reading logs (requires log:read permission)
    fastify.register(async (fastify: FastifyInstance) => {
      fastify.addHook('preHandler', checkPermission('log:read'));

      fastify.get('/', { schema: getAllLogsSchema }, logController.getAllLogs as any);
      fastify.get('/:id', { schema: getLogByIdSchema }, logController.getLogById as any);
    });

    // Routes for creating logs (requires log:create permission)
    fastify.register(async (fastify: FastifyInstance) => {
      fastify.addHook('preHandler', checkPermission('log:create'));

      fastify.post('/', { schema: createLogSchema }, logController.createLog as any);
    });

    // Routes for deleting logs (requires log:delete permission)
    fastify.register(async (fastify: FastifyInstance) => {
      fastify.addHook('preHandler', checkPermission('log:delete'));

      fastify.delete('/:id', { schema: deleteLogSchema }, logController.deleteLog as any);
    });
  });
}
