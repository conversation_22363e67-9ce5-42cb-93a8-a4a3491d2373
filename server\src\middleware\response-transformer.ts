/**
 * Middleware to transform response data
 * This ensures that all videos in responses use Engaxe IDs consistently
 */

import { FastifyReply, FastifyRequest, HookHandlerDoneFunction } from 'fastify';
import { videoIdMappingService } from '../services';

/**
 * Transform a video object to ensure it uses Engaxe IDs consistently
 * @param video The video object to transform
 * @returns The transformed video object
 */
async function transformVideo(video: any): Promise<any> {
  if (!video) return video;
  return await videoIdMappingService.transformVideoForClient(video);
}

/**
 * Transform an array of videos
 * @param videos Array of video objects
 * @returns Transformed array
 */
async function transformVideos(videos: any[]): Promise<any[]> {
  if (!videos || !Array.isArray(videos)) return videos;
  return await videoIdMappingService.transformVideosForClient(videos);
}

/**
 * Recursively search for video objects in a response payload and transform them
 * @param payload The response payload to transform
 * @returns The transformed payload
 */
async function transformPayload(payload: any): Promise<any> {
  if (!payload) return payload;

  // If it's an array, check if it's an array of videos
  if (Array.isArray(payload)) {
    // Check if this is an array of videos
    if (payload.length > 0 && payload[0] && typeof payload[0] === 'object' && 'id' in payload[0] && 'title' in payload[0]) {
      console.log('Found video array in response with', payload.length, 'videos');
      console.log('First video before:', payload[0].id, 'URL:', payload[0].url);
      const transformedVideos = await transformVideos(payload);
      console.log('First video after:', transformedVideos[0].id, 'URL:', transformedVideos[0].url);
      return transformedVideos;
    }
    
    // Otherwise, recursively transform each item in the array
    const transformedArray = [];
    for (const item of payload) {
      transformedArray.push(await transformPayload(item));
    }
    return transformedArray;
  }
  
  // If it's an object, check if it's a video object
  if (typeof payload === 'object' && payload !== null) {
    // Check if this is a video object
    if ('id' in payload && 'title' in payload && ('url' in payload || 'channelId' in payload)) {
      console.log('Found video object in response:', payload.id);
      return await transformVideo(payload);
    }
    
    // Check if this is a response with a videos array
    if ('videos' in payload && Array.isArray(payload.videos)) {
      console.log('Found videos array in response with', payload.videos.length, 'videos');
      payload.videos = await transformPayload(payload.videos);
      console.log('Transformed video IDs in response');
    }
    
    // Check if this is a response with a video object
    if ('video' in payload && typeof payload.video === 'object') {
      console.log('Found video object in response:', payload.video.id);
      payload.video = await transformVideo(payload.video);
      console.log('Transformed video ID in response');
    }
    
    // Otherwise, recursively transform each property in the object
    const transformedObject: any = {};
    for (const [key, value] of Object.entries(payload)) {
      transformedObject[key] = await transformPayload(value);
    }
    return transformedObject;
  }
  
  // For primitive values, return as is
  return payload;
}

/**
 * Middleware to transform response data
 */
export async function responseTransformer(
  request: FastifyRequest,
  reply: FastifyReply,
  payload: string,
  done: HookHandlerDoneFunction
) {
  try {
    // Only transform JSON responses
    if (reply.getHeader('content-type')?.toString().includes('application/json')) {
      // Parse the payload
      const parsedPayload = JSON.parse(payload);
      
      // Check if the payload contains video data
      console.log('Response transformer: Checking payload for video data');
      console.log('Response URL:', request.url);
      
      // Transform the payload
      const transformedPayload = await transformPayload(parsedPayload);
      
      // Return the transformed payload
      return JSON.stringify(transformedPayload);
    }
  } catch (error) {
    console.error('Error transforming response:', error);
  }
  
  // If there was an error or the response is not JSON, return the original payload
  return payload;
}
