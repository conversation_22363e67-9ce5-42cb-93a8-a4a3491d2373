
import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus, Languages, Loader2, Search } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { Language } from '@/types';
import { useVideos } from '@/context/VideoContext';
import { videoAPI } from '@/services/api';
import axios from 'axios';

interface VideoLinkFormProps {
  videoId: string;
}

export default function VideoLinkForm({ videoId }: VideoLinkFormProps) {
  const { toast } = useToast();
  const { languages, videos, refreshVideos } = useVideos();
  const [isOpen, setIsOpen] = useState(false);
  const [videoUrl, setVideoUrl] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [videoDetails, setVideoDetails] = useState<any>(null);

  // Get the current video
  const currentVideo = videos.find(v => v.id === videoId);

  // Get languages that are already used for this video
  const usedLanguageCodes = currentVideo?.languages?.map(lang => lang.code) || [];

  // Available languages that don't have versions yet
  const availableLanguages = languages.filter(lang => !usedLanguageCodes.includes(lang.code));

  // Function to extract Engaxe video ID from URL
  const extractEngaxeVideoId = (url: string): string | null => {
    if (!url) return null;

    // Trim the input
    const trimmedUrl = url.trim();

    // If it's just a video ID (no slashes, dots, or protocol)
    if (!trimmedUrl.includes('/') && !trimmedUrl.includes('.') && !trimmedUrl.includes(':')) {
      if (trimmedUrl.length > 3 && trimmedUrl.length < 20 && /^[a-zA-Z0-9]+$/.test(trimmedUrl)) {
        console.log(`Input appears to be a direct video ID: ${trimmedUrl}`);
        return trimmedUrl;
      }
    }

    // Engaxe URL patterns
    const patterns = [
      // Format: engaxe.com/videos/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/videos\/([^/?]+)/i,
      // Format: engaxe.com/v/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/v\/([^/?]+)/i,
      // Format: engaxe.com/watch/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/watch\/([^/?]+)/i,
      // Format: engaxe.com/embed/[id]
      /(?:https?:\/\/)?(?:www\.)?engaxe\.com\/embed\/([^/?]+)/i
    ];

    // Try each pattern
    for (const pattern of patterns) {
      const match = trimmedUrl.match(pattern);
      if (match && match[1]) {
        console.log(`Successfully extracted video ID: ${match[1]} using pattern: ${pattern}`);
        return match[1];
      }
    }

    // If no pattern matches, try a simple extraction as a fallback
    try {
      // Try to parse as URL first
      const urlObj = new URL(trimmedUrl.startsWith('http') ? trimmedUrl : `https://${trimmedUrl}`);
      const pathParts = urlObj.pathname.split('/').filter(part => part.length > 0);

      if (pathParts.length > 0) {
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
          console.log(`Using URL path extraction, got video ID: ${lastPart}`);
          return lastPart;
        }
      }
    } catch (error) {
      // If URL parsing fails, try simple string splitting
      const parts = trimmedUrl.split('/');
      const lastPart = parts[parts.length - 1];
      if (lastPart && lastPart.length > 0 && !lastPart.includes('.') && !lastPart.includes('?')) {
        console.log(`Using fallback string extraction, got video ID: ${lastPart}`);
        return lastPart;
      }
    }

    console.log('Failed to extract video ID from input');
    return null;
  };

  // Function to fetch video details from Engaxe
  const fetchVideoDetails = async () => {
    try {
      setIsFetching(true);

      // Extract video ID from URL
      const extractedVideoId = extractEngaxeVideoId(videoUrl);
      if (!extractedVideoId) {
        toast({
          title: "Invalid URL",
          description: "Could not extract a valid Engaxe video ID from the URL",
          variant: "destructive"
        });
        return;
      }

      // Validate that the ID is a valid 6-7 character Engaxe ID
      const isValidEngaxeId = /^[a-zA-Z0-9]{6,7}$/.test(extractedVideoId);
      if (!isValidEngaxeId) {
        toast({
          title: "Invalid Engaxe ID",
          description: `The ID "${extractedVideoId}" is not a valid Engaxe ID. Must be 6-7 alphanumeric characters.`,
          variant: "destructive"
        });
        return;
      }

      // Fetch video details from Engaxe via our server proxy to avoid CORS issues
      console.log(`Fetching video details for ID: ${extractedVideoId}`);
      // Use our server API instead of directly calling Engaxe
      const response = await videoAPI.getVideoMetadata(extractedVideoId);

      if (response.success && response.status === 'success' && response.data) {
        console.log("Video details fetched successfully:", response);
        setVideoDetails(response.data);

        // Show success notification
        toast({
          title: "Video details fetched",
          description: `Successfully fetched details for "${response.data.title}"`,
        });
      } else {
        console.error("Failed to fetch video details:", response);
        toast({
          title: "Error",
          description: "Failed to fetch video details from Engaxe",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error fetching video details:", error);
      toast({
        title: "Error",
        description: "An error occurred while fetching video details",
        variant: "destructive"
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleLinkVideo = async () => {
    try {
      setIsLoading(true);

      // Extract video ID from URL if possible
      const extractedVideoId = extractEngaxeVideoId(videoUrl);
      const processedUrl = extractedVideoId || videoUrl;

      if (extractedVideoId) {
        console.log(`Using extracted video ID: ${extractedVideoId} (original URL: ${videoUrl})`);
      } else {
        console.log(`Could not extract video ID, using original URL: ${videoUrl}`);
      }

      // Validate that the ID is a valid 6-7 character Engaxe ID
      const isValidEngaxeId = /^[a-zA-Z0-9]{6,7}$/.test(processedUrl);
      if (!isValidEngaxeId) {
        toast({
          title: "Invalid Engaxe ID",
          description: `The ID "${processedUrl}" is not a valid Engaxe ID. Must be 6-7 alphanumeric characters.`,
          variant: "destructive"
        });
        return;
      }

      if (!currentVideo) {
        toast({
          title: "Error",
          description: "Could not find the current video",
          variant: "destructive"
        });
        return;
      }

      // Get the language object for the selected language
      const languageObj = languages.find(lang => lang.code === selectedLanguage);
      if (!languageObj) {
        toast({
          title: "Error",
          description: "Invalid language selected",
          variant: "destructive"
        });
        return;
      }

      console.log(`Linking video ${videoId} with ${processedUrl} in language ${selectedLanguage}`);
      console.log("Current video languages:", currentVideo.languages);

      // Create a copy of the current video's languages
      const updatedLanguages = [...(currentVideo.languages || [])];

      // Check if this language already exists
      const existingLangIndex = updatedLanguages.findIndex(lang => lang.code === selectedLanguage);

      if (existingLangIndex >= 0) {
        // Update the existing language
        console.log(`Updating existing language ${selectedLanguage} with new URL: ${processedUrl}`);
        updatedLanguages[existingLangIndex] = {
          ...updatedLanguages[existingLangIndex],
          url: processedUrl
        };
      } else {
        // Add the new language with its URL
        console.log(`Adding new language ${selectedLanguage} with URL: ${processedUrl}`);
        updatedLanguages.push({
          code: languageObj.code,
          name: languageObj.name,
          flag: languageObj.flag,
          url: processedUrl,
          isDefault: updatedLanguages.length === 0 // Make it default if it's the first language
        });
      }

      console.log("Updated languages:", updatedLanguages);

      // Make an API call to update the video with the new language
      const response = await videoAPI.updateVideo(videoId, {
        languages: updatedLanguages
      });

      console.log("API response:", response);

      if (response.success) {
        // Show success notification
        toast({
          title: "Language version linked",
          description: `Your video now supports ${languageObj.name}`,
        });

        // Refresh videos to update the UI
        await refreshVideos();
      } else {
        throw new Error(response.message || "Failed to update video");
      }

      // Reset and close form
      setVideoUrl("");
      setSelectedLanguage("");
      setIsOpen(false);
    } catch (error) {
      console.error("Error linking language version:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to link language version",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
          <Plus className="h-3 w-3 mr-1" />
          Add
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Languages className="h-5 w-5" />
            Link Language Version
          </DialogTitle>
          <DialogDescription>
            Connect another language version of this video from Engaxe to create a multilingual experience.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="language">Select Language</label>
            <Select value={selectedLanguage} onValueChange={setSelectedLanguage} disabled={isLoading}>
              <SelectTrigger>
                <SelectValue placeholder="Choose language" />
              </SelectTrigger>
              <SelectContent>
                {availableLanguages.length > 0 ? (
                  availableLanguages.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      <div className="flex items-center">
                        <span className="mr-2">{lang.flag}</span> {lang.name}
                      </div>
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="none" disabled>
                    No additional languages available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            {availableLanguages.length === 0 && (
              <p className="text-xs text-amber-500">
                All available languages are already added to this video.
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="videoUrl">Engaxe Video URL or ID</label>
            <div className="flex gap-2">
              <Input
                id="videoUrl"
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                placeholder="https://engaxe.com/videos/1234 or video-id"
                disabled={isLoading || isFetching}
              />
              <Button
                variant="secondary"
                onClick={fetchVideoDetails}
                disabled={!videoUrl || isLoading || isFetching}
                type="button"
              >
                {isFetching ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Search className="h-4 w-4" />
                )}
              </Button>
            </div>
            <p className="text-xs text-lingstream-muted">
              Paste the Engaxe video URL or ID that contains the same content in the selected language.
              Must be a valid 6-7 character Engaxe ID.
            </p>
          </div>

          {videoDetails && (
            <div className="space-y-2 border rounded-md p-3 bg-gray-50 dark:bg-gray-900">
              <h4 className="font-medium">Video Details</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <p className="text-xs text-lingstream-muted">Title:</p>
                  <p className="truncate">{videoDetails.title}</p>
                </div>
                <div>
                  <p className="text-xs text-lingstream-muted">Duration:</p>
                  <p>{videoDetails.duration || 'Unknown'}</p>
                </div>
                {videoDetails.thumbnail && (
                  <div className="col-span-2">
                    <img
                      src={videoDetails.thumbnail}
                      alt={videoDetails.title}
                      className="h-20 w-auto object-cover rounded-md"
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isLoading || isFetching}>
            Cancel
          </Button>
          <Button
            onClick={handleLinkVideo}
            disabled={!selectedLanguage || !videoUrl || isLoading || isFetching || availableLanguages.length === 0}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              'Link Video'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
