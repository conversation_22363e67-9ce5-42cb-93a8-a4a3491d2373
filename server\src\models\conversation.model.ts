import mongoose, { Schema, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Conversation interface extending the base entity
 */
export interface IConversation extends IBaseEntity {
  /**
   * ID of the creator receiving messages
   * This is the content creator who is being contacted by the user
   */
  creatorId: string;

  /**
   * ID of the user initiating the conversation
   * This is the regular user contacting the creator
   */
  userId: string;

  /**
   * Subject or title of the conversation
   * Helps users and creators identify the topic
   */
  subject: string;

  /**
   * Current status of the conversation
   */
  status: 'active' | 'archived' | 'closed';

  /**
   * Language preference for the creator
   */
  creatorLanguage: string;

  /**
   * Language preference for the user
   */
  userLanguage: string;

  /**
   * Last message timestamp for sorting
   */
  lastMessageAt: Date;

  /**
   * Count of unread messages for the creator
   */
  creatorUnreadCount: number;

  /**
   * Count of unread messages for the user
   */
  userUnreadCount: number;

  /**
   * Whether the conversation is pinned for the creator
   */
  isPinnedByCreator: boolean;

  /**
   * Whether the conversation is pinned for the user
   */
  isPinnedByUser: boolean;

  /**
   * Conversation settings
   */
  settings: {
    /**
     * Whether notifications are enabled for this conversation
     */
    notifications: boolean;

    /**
     * Whether read receipts are enabled for this conversation
     */
    readReceipts: boolean;
  };

  /**
   * Additional metadata
   */
  metadata: {
    /**
     * Tags for categorizing conversations
     */
    tags?: string[];

    /**
     * Custom fields for integration with other systems
     */
    customFields?: Record<string, any>;
  };
}

/**
 * Conversation schema definition
 */
const ConversationSchema = new Schema<IConversation>(
  {
    creatorId: {
      type: String,
      required: true,
      ref: 'User',
      index: true,
    },
    userId: {
      type: String,
      required: true,
      ref: 'User',
      index: true,
    },
    subject: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    status: {
      type: String,
      enum: ['active', 'archived', 'closed'],
      default: 'active',
      index: true,
    },
    creatorLanguage: {
      type: String,
      default: 'en',
    },
    userLanguage: {
      type: String,
      default: 'en',
    },
    lastMessageAt: {
      type: Date,
      default: Date.now,
      index: true,
    },
    creatorUnreadCount: {
      type: Number,
      default: 0,
    },
    userUnreadCount: {
      type: Number,
      default: 0,
    },
    isPinnedByCreator: {
      type: Boolean,
      default: false,
    },
    isPinnedByUser: {
      type: Boolean,
      default: false,
    },
    settings: {
      notifications: {
        type: Boolean,
        default: true,
      },
      readReceipts: {
        type: Boolean,
        default: true,
      },
    },
    metadata: {
      tags: {
        type: [String],
        default: [],
      },
      customFields: {
        type: Schema.Types.Mixed,
        default: {},
      },
    },
  },
  { timestamps: true }
);

// Merge with base schema
ConversationSchema.add(BaseSchema);

// Add compound indexes for common queries
ConversationSchema.index({ creatorId: 1, status: 1, lastMessageAt: -1 });
ConversationSchema.index({ userId: 1, status: 1, lastMessageAt: -1 });
ConversationSchema.index({ creatorId: 1, isPinnedByCreator: 1, lastMessageAt: -1 });
ConversationSchema.index({ userId: 1, isPinnedByUser: 1, lastMessageAt: -1 });

// Create and export the Conversation model
const ConversationModel = mongoose.model<IConversation>('Conversation', ConversationSchema);
export default ConversationModel;
