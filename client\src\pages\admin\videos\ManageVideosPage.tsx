import React, { useState } from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { Home, Search, ChevronUp, ChevronDown, Edit, Trash2, Eye, EyeOff, Filter, Plus, X, SlidersHorizontal, Check, Download, FileText, FileSpreadsheet, FileJson } from 'lucide-react';
import { exportToCSV, exportToJSON, exportToText } from '@/utils/exportUtils';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';

interface VideoData {
  id: number;
  videoId: string;
  title: string;
  category: string;
  source: string;
  privacy: string;
  addedBy: string;
  approved: string;
}

export default function ManageVideosPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortColumn, setSortColumn] = useState<keyof VideoData>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState('all');
  const [resultsPerPage, setResultsPerPage] = useState('50');
  const [videoType, setVideoType] = useState('all');
  const [category, setCategory] = useState('category');
  const [paymentType, setPaymentType] = useState('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [showIdColumn, setShowIdColumn] = useState(false);
  const [showExportDropdown, setShowExportDropdown] = useState(false);

  const videos: VideoData[] = [
    {
      id: 757,
      videoId: 'h8SN9aB7J91Deb6',
      title: 'UNO: You Play! Interactive Video Game',
      category: 'Gaming',
      source: 'YouTube',
      privacy: 'Public',
      addedBy: 'avatar_Sharliya',
      approved: '1'
    }
  ];

  const handleSort = (column: keyof VideoData) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const SortIcon = ({ column }: { column: keyof VideoData }) => {
    if (sortColumn !== column) return <ChevronDown size={14} className="ml-1 opacity-50" />;
    return sortDirection === 'asc' ?
      <ChevronUp size={14} className="ml-1" /> :
      <ChevronDown size={14} className="ml-1" />;
  };

  // Update active filters based on current filter states
  const updateActiveFilters = () => {
    const filters: string[] = [];

    if (filterStatus !== 'all') {
      filters.push(`Status: ${filterStatus}`);
    }

    if (videoType !== 'all') {
      filters.push(`Type: ${videoType}`);
    }

    if (category !== 'category') {
      filters.push(`Category: ${category}`);
    }

    if (paymentType !== 'all') {
      filters.push(`Payment: ${paymentType}`);
    }

    setActiveFilters(filters);
  };

  // Handle filter changes
  const handleFilterChange = (type: string, value: string) => {
    switch (type) {
      case 'status':
        setFilterStatus(value);
        break;
      case 'perPage':
        setResultsPerPage(value);
        break;
      case 'videoType':
        setVideoType(value);
        break;
      case 'category':
        setCategory(value);
        break;
      case 'paymentType':
        setPaymentType(value);
        break;
    }

    // Update active filters after a short delay to ensure state is updated
    setTimeout(updateActiveFilters, 100);
  };

  // Clear all filters
  const clearFilters = () => {
    setFilterStatus('all');
    setVideoType('all');
    setCategory('category');
    setPaymentType('all');
    setActiveFilters([]);
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Videos</h1>
              <div className="flex items-center space-x-2">
                {/* Column Visibility Toggle */}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm">
                      {showIdColumn ? <Eye className="h-4 w-4 mr-2" /> : <EyeOff className="h-4 w-4 mr-2" />}
                      {showIdColumn ? 'Hide ID' : 'Show ID'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-48 p-0" align="end">
                    <div className="py-1">
                      <button
                        className={`w-full text-left px-4 py-2 text-sm flex items-center ${showIdColumn ? 'text-black hover:bg-gray-100' : 'bg-blue-500 text-white'}`}
                        onClick={() => setShowIdColumn(false)}
                      >
                        <EyeOff className="h-4 w-4 mr-2" />
                        Hide ID Column
                      </button>
                      <button
                        className={`w-full text-left px-4 py-2 text-sm flex items-center ${showIdColumn ? 'bg-blue-500 text-white' : 'text-black hover:bg-gray-100'}`}
                        onClick={() => setShowIdColumn(true)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Show ID Column
                      </button>
                    </div>
                  </PopoverContent>
                </Popover>

                {/* Export Button */}
                <Popover open={showExportDropdown} onOpenChange={setShowExportDropdown}>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-40 p-0" align="end">
                    <div className="py-1">
                      <button
                        className="w-full text-left px-4 py-2 text-sm flex items-center text-black hover:bg-gray-100"
                        onClick={() => {
                          exportToText(videos, 'videos');
                          setShowExportDropdown(false);
                        }}
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Export as PDF
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 text-sm flex items-center text-black hover:bg-gray-100"
                        onClick={() => {
                          exportToCSV(videos, 'videos');
                          setShowExportDropdown(false);
                        }}
                      >
                        <FileSpreadsheet className="h-4 w-4 mr-2" />
                        Export as CSV
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 text-sm flex items-center text-black hover:bg-gray-100"
                        onClick={() => {
                          exportToCSV(videos, 'videos');
                          setShowExportDropdown(false);
                        }}
                      >
                        <FileSpreadsheet className="h-4 w-4 mr-2" />
                        Export as Excel
                      </button>
                      <button
                        className="w-full text-left px-4 py-2 text-sm flex items-center text-black hover:bg-gray-100"
                        onClick={() => {
                          exportToJSON(videos, 'videos');
                          setShowExportDropdown(false);
                        }}
                      >
                        <FileJson className="h-4 w-4 mr-2" />
                        Export as JSON
                      </button>
                    </div>
                  </PopoverContent>
                </Popover>

                <Button onClick={() => navigate('/admin/videos/edit/new')}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Video
                </Button>
              </div>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground">&gt;</span>
              <Link to="/admin/videos" className="text-muted-foreground hover:text-foreground">
                Videos
              </Link>
              <span className="mx-2 text-muted-foreground">&gt;</span>
              <span className="text-primary">Manage Videos</span>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Manage & Edit Videos</CardTitle>
                <CardDescription>View, edit, and manage all videos in your library</CardDescription>
              </CardHeader>
              <CardContent>

                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search by keyword, ID, or title..."
                      className="pl-10 pr-20"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Button
                      className="absolute right-0 top-0 h-full rounded-l-none"
                      size="sm"
                    >
                      Search
                    </Button>
                  </div>

                  <div className="flex gap-2">
                    <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant={activeFilters.length > 0 ? "default" : "outline"}
                          className="min-w-[120px]"
                        >
                          <Filter className="mr-2 h-4 w-4" />
                          Filter {activeFilters.length > 0 && (
                            <Badge variant="secondary" className="ml-2 bg-white text-primary">{activeFilters.length}</Badge>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[340px] p-0" align="end">
                        <div className="bg-primary text-primary-foreground px-4 py-3 font-medium">
                          Filter Videos
                        </div>
                        <div className="p-4 space-y-4">
                          <div className="space-y-3">
                            <div className="space-y-1">
                              <Label htmlFor="status" className="text-sm font-medium">Status</Label>
                              <Select value={filterStatus} onValueChange={(value) => handleFilterChange('status', value)}>
                                <SelectTrigger id="status" className="w-full">
                                  <SelectValue placeholder="Filter status" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">All (Default)</SelectItem>
                                  <SelectItem value="approved">Approved</SelectItem>
                                  <SelectItem value="pending">Pending</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="videoType" className="text-sm font-medium">Video Type</Label>
                              <Select value={videoType} onValueChange={(value) => handleFilterChange('videoType', value)}>
                                <SelectTrigger id="videoType" className="w-full">
                                  <SelectValue placeholder="Video type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">All Videos</SelectItem>
                                  <SelectItem value="featured">Featured</SelectItem>
                                  <SelectItem value="trending">Trending</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="category" className="text-sm font-medium">Category</Label>
                              <Select value={category} onValueChange={(value) => handleFilterChange('category', value)}>
                                <SelectTrigger id="category" className="w-full">
                                  <SelectValue placeholder="Category" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="category">All Categories</SelectItem>
                                  <SelectItem value="gaming">Gaming</SelectItem>
                                  <SelectItem value="education">Education</SelectItem>
                                  <SelectItem value="entertainment">Entertainment</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="paymentType" className="text-sm font-medium">Payment Type</Label>
                              <Select value={paymentType} onValueChange={(value) => handleFilterChange('paymentType', value)}>
                                <SelectTrigger id="paymentType" className="w-full">
                                  <SelectValue placeholder="Payment type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="all">Paid & Free</SelectItem>
                                  <SelectItem value="paid">Paid Only</SelectItem>
                                  <SelectItem value="free">Free Only</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-1">
                              <Label htmlFor="perPage" className="text-sm font-medium">Results Per Page</Label>
                              <Select value={resultsPerPage} onValueChange={(value) => handleFilterChange('perPage', value)}>
                                <SelectTrigger id="perPage" className="w-full">
                                  <SelectValue placeholder="Results per page" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="10">10 per page</SelectItem>
                                  <SelectItem value="25">25 per page</SelectItem>
                                  <SelectItem value="50">50 per page</SelectItem>
                                  <SelectItem value="100">100 per page</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-4 bg-muted/30 border-t">
                          {activeFilters.length > 0 && (
                            <Button variant="ghost" size="sm" onClick={clearFilters}>
                              <X className="mr-2 h-4 w-4" />
                              Clear all
                            </Button>
                          )}
                          {!activeFilters.length && <div></div>}
                          <Button
                            className="bg-primary text-primary-foreground hover:bg-primary/90"
                            onClick={() => setIsFilterOpen(false)}
                          >
                            <Check className="mr-2 h-4 w-4" />
                            Apply Filters
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>

                    <Select value={resultsPerPage} onValueChange={(value) => handleFilterChange('perPage', value)}>
                      <SelectTrigger className="w-[130px]">
                        <SelectValue placeholder="Results per page" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10 per page</SelectItem>
                        <SelectItem value="25">25 per page</SelectItem>
                        <SelectItem value="50">50 per page</SelectItem>
                        <SelectItem value="100">100 per page</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {activeFilters.length > 0 && (
                  <div className="bg-muted/30 border rounded-md p-3 mb-6 flex items-center">
                    <div className="text-sm font-medium mr-3">Filters:</div>
                    <div className="flex flex-wrap gap-2 flex-1">
                      {activeFilters.map((filter, index) => {
                        const [type, value] = filter.split(': ');
                        return (
                          <Badge key={index} variant="outline" className="px-3 py-1 bg-background flex items-center gap-1">
                            <span className="font-medium">{type}:</span>
                            <span>{value}</span>
                            <X
                              className="ml-1 h-3 w-3 cursor-pointer hover:text-destructive"
                              onClick={() => {
                                if (type === 'Status') handleFilterChange('status', 'all');
                                if (type === 'Type') handleFilterChange('videoType', 'all');
                                if (type === 'Category') handleFilterChange('category', 'category');
                                if (type === 'Payment') handleFilterChange('paymentType', 'all');
                              }}
                            />
                          </Badge>
                        );
                      })}
                    </div>
                    <Button variant="ghost" size="sm" onClick={clearFilters} className="ml-auto">
                      Clear all filters
                    </Button>
                  </div>
                )}

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-10">
                          <Checkbox />
                        </TableHead>
                        {showIdColumn && (
                          <TableHead className="cursor-pointer" onClick={() => handleSort('id')}>
                            <div className="flex items-center">
                              ID <SortIcon column="id" />
                            </div>
                          </TableHead>
                        )}
                        <TableHead className="cursor-pointer" onClick={() => handleSort('videoId')}>
                          <div className="flex items-center">
                            VIDEO ID <SortIcon column="videoId" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('title')}>
                          <div className="flex items-center">
                            TITLE <SortIcon column="title" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('category')}>
                          <div className="flex items-center">
                            CATEGORY <SortIcon column="category" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('source')}>
                          <div className="flex items-center">
                            SOURCE <SortIcon column="source" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('privacy')}>
                          <div className="flex items-center">
                            PRIVACY <SortIcon column="privacy" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('addedBy')}>
                          <div className="flex items-center">
                            ADDED BY <SortIcon column="addedBy" />
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('approved')}>
                          <div className="flex items-center">
                            APPROVED <SortIcon column="approved" />
                          </div>
                        </TableHead>
                        <TableHead>ACTION</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {videos.map((video) => (
                        <TableRow key={video.id}>
                          <TableCell>
                            <Checkbox />
                          </TableCell>
                          {showIdColumn && (
                            <TableCell>{video.id}</TableCell>
                          )}
                          <TableCell>{video.videoId}</TableCell>
                          <TableCell>{video.title}</TableCell>
                          <TableCell>{video.category}</TableCell>
                          <TableCell>{video.source}</TableCell>
                          <TableCell>{video.privacy}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <img src="/avatar.jpg" alt="Avatar" className="w-6 h-6 rounded-full mr-2" />
                              {video.addedBy.split('_')[1]}
                            </div>
                          </TableCell>
                          <TableCell>
                            {video.approved === '1' ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Approved
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Pending
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex space-x-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-green-500 hover:text-green-700 hover:bg-green-50"
                                onClick={() => navigate(`/admin/videos/edit/${video.id}`)}
                              >
                                <Edit size={16} />
                                <span className="sr-only">Edit</span>
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                                onClick={() => navigate(`/admin/videos/fake-views/${video.id}`)}
                                title="Add Fake Views"
                              >
                                <Eye size={16} />
                                <span className="sr-only">Add Fake Views</span>
                              </Button>
                              <Button variant="ghost" size="icon" className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50">
                                <Trash2 size={16} />
                                <span className="sr-only">Delete</span>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-muted-foreground">
                    Showing <span className="font-medium">1</span> to <span className="font-medium">1</span> of{" "}
                    <span className="font-medium">1</span> results
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm" disabled>
                      Previous
                    </Button>
                    <Button variant="outline" size="sm" className="bg-primary text-primary-foreground">
                      1
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      Next
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
