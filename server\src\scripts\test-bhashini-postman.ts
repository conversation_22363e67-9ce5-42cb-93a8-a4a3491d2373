/**
 * Test script for Bhashini API integration based on Postman tests
 * 
 * This script tests the Bhashini API integration using the credentials:
 * - User ID: cee60134c6bb4d179efd3fda48ff32fe
 * - ULCA API Key: 13a647c84b-2747-4f0c-afcd-2ac8235f5318
 * 
 * Usage:
 * ts-node src/scripts/test-bhashini-postman.ts
 */

import axios from 'axios';

// Bhashini API credentials
const BHASHINI_USER_ID = 'cee60134c6bb4d179efd3fda48ff32fe';
const BHASHINI_ULCA_API_KEY = '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

// Bhashini API endpoint
const BHASHINI_TRANSLATION_API_URL = 'https://bhashini.gov.in/api/v1/inference/translation';

// Test text
const TEST_TEXT = 'Hello, how are you today?';
const SOURCE_LANGUAGE = 'en';
const TARGET_LANGUAGE = 'hi';

/**
 * Test translation using Bhashini API
 */
async function testTranslation() {
  console.log(`\nTesting Bhashini API translation...`);
  console.log(`Translating "${TEST_TEXT}" from ${SOURCE_LANGUAGE} to ${TARGET_LANGUAGE}`);
  
  try {
    // Prepare request payload
    const payload = {
      userId: BHASHINI_USER_ID,
      ulcaApiKey: BHASHINI_ULCA_API_KEY,
      sourceLanguage: SOURCE_LANGUAGE,
      targetLanguage: TARGET_LANGUAGE,
      domain: "general",
      text: TEST_TEXT
    };
    
    console.log('Request payload:', JSON.stringify(payload, null, 2));
    
    // Make API request
    const response = await axios.post(
      BHASHINI_TRANSLATION_API_URL,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );
    
    console.log('Translation successful!');
    console.log('Response:', JSON.stringify(response.data, null, 2));
    console.log('Translated text:', response.data.translatedText);
    
    return response.data;
  } catch (error: any) {
    console.error('Translation failed:', error.message);
    if (error.response) {
      console.error('Error response:', error.response.data);
    }
    throw error;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== BHASHINI API INTEGRATION TEST (POSTMAN) ===\n');
  console.log('Using credentials:');
  console.log(`- User ID: ${BHASHINI_USER_ID}`);
  console.log(`- ULCA API Key: ${BHASHINI_ULCA_API_KEY}`);
  
  try {
    // Test translation
    await testTranslation();
    
    console.log('\n=== ALL TESTS PASSED ===');
  } catch (error) {
    console.error('\n=== TEST FAILED ===');
    process.exit(1);
  }
}

// Run the tests
runTests();
