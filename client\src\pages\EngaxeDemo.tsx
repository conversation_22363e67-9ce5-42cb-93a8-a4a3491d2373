import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import Navbar from '@/components/layout/Navbar';

/**
 * Demo page for Engaxe video player integration
 */
const EngaxeDemo: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="container py-8">
        <h1 className="text-3xl font-bold mb-6">Engaxe Video Player Demo</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Sample Engaxe Video</CardTitle>
              <CardDescription>
                This is a sample video embedded from Engaxe
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                <p className="text-muted-foreground">Video player would be embedded here</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="mr-2">
                Play
              </Button>
              <Button>
                Add to Watchlist
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
              <CardDescription>
                Understanding the Engaxe integration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                The Engaxe video player is integrated using the embed API. Videos are fetched from Engaxe
                and displayed within our application.
              </p>
              <ul className="list-disc pl-5 space-y-2">
                <li>Videos are embedded using the Engaxe embed script</li>
                <li>Video metadata is fetched using the Engaxe API</li>
                <li>Users can interact with videos just like on the Engaxe platform</li>
              </ul>
            </CardContent>
            <CardFooter>
              <Button variant="outline">
                Learn More
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default EngaxeDemo;
