
import { useState } from 'react';
import { useVideos } from '@/context/VideoContext';
import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import VideoCard from '@/components/video/VideoCard';
import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function UserVideos() {
  const { videos } = useVideos();
  const { currentUser } = useAuth();
  const { t } = useLanguage();

  // Filter videos by current user (in a real app this would be from API)
  const userVideos = videos.filter(video => video.creator.id === currentUser?.id);

  if (userVideos.length === 0) {
    return (
      <div className="text-center p-8 border border-dashed rounded-lg">
        <h3 className="font-medium mb-2">{t('profile.no_videos')}</h3>
        <p className="text-sm text-lingstream-muted mb-4">
          {t('profile.start_sharing')}
        </p>
        <Link to="/creator-studio">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            {t('profile.add_video')}
          </Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="font-medium">{t('profile.your_videos')} ({userVideos.length})</h3>
        <Link to="/creator-studio">
          <Button size="sm">
            <Plus className="mr-2 h-4 w-4" />
            {t('profile.add_video')}
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {userVideos.map((video) => (
          <VideoCard key={video.id} video={video} />
        ))}
      </div>
    </div>
  );
}
