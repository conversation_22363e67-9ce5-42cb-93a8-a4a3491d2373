
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./context/AuthContext";
import { VideoProvider } from "./context/VideoContext";
import { MessageProvider } from "./context/MessageContext";
import { NotificationProvider } from "./context/NotificationContext";
import { ChatbotProvider } from "./context/ChatbotContext";
import { LanguageProvider } from "./context/LanguageContext";
import { WatchlistProvider } from "./context/WatchlistContext";
import { ThemeProvider } from "./context/ThemeContext";

// Pages
import HomePage from "./pages/HomePage";
import VideoPage from "./pages/VideoPage";
import ChannelPage from "./pages/channels/ChannelPage";
import ChannelsPage from "./pages/channels/ChannelsPage";
import CreateChannelPage from "./pages/channels/CreateChannelPage";
import MessagesPage from "./pages/MessagesPage";
import ChannelChatPage from "./pages/ChannelChatPage";
import SignInPage from "./pages/SignInPage";
import SignUpPage from "./pages/SignUpPage";
import TrendingPage from "./pages/TrendingPage";
import NotFound from "./pages/NotFound";
import ProfilePage from "./pages/ProfilePage";
import ReportIssuePage from "./pages/ReportIssuePage";
import BasicProfilePage from "./pages/BasicProfilePage";
import CreatorStudioPage from "./pages/CreatorStudioPage";
import AdminPage from "./pages/AdminPage";
import SettingsPage from "./pages/SettingsPage";
import AdminToolsPage from "./pages/AdminToolsPage";
import ManageUsersPage from "./pages/admin/ManageUsersPage";
import UserPermissionsPage from "./pages/admin/UserPermissionsPage";
import VerificationRequestsPage from "./pages/admin/VerificationRequestsPage";
import ManageCustomProfileFieldsPage from "./pages/admin/ManageCustomProfileFieldsPage";
import EditCustomFieldPage from "./pages/admin/EditCustomFieldPage";
import AddNewLanguageKeyPage from "./pages/admin/AddNewLanguageKeyPage";
import ManageLanguagesPage from "./pages/admin/ManageLanguagesPage";
import ImportVideosPage from "./pages/admin/ImportVideosPage";
import ThemesPage from "./pages/admin/ThemesPage";
import CreateArticlePage from "./pages/admin/CreateArticlePage";
import ManageArticlesPage from "./pages/admin/ManageArticlesPage";
import EditArticlePage from "./pages/admin/articles/EditArticlePage";
import ManageCategoriesPage from "./pages/admin/ManageCategoriesPage";
import ManageSubCategoriesPage from "./pages/admin/ManageSubCategoriesPage";
import AnnouncementsPage from "./pages/admin/tools/AnnouncementsPage";
import BanUsersPage from "./pages/admin/tools/BanUsersPage";
import ActivitiesPage from "./pages/admin/tools/ActivitiesPage";
import MassNotificationsPage from "./pages/admin/tools/MassNotificationsPage";
import NewslettersPage from "./pages/admin/tools/NewslettersPage";
import ManageVideoReportsPage from "./pages/admin/reports/ManageVideoReportsPage";
import ManageCopyrightReportsPage from "./pages/admin/reports/ManageCopyrightReportsPage";
import ManageCustomPagesPage from "./pages/admin/pages/ManageCustomPagesPage";
import ManagePagesPage from "./pages/admin/pages/ManagePagesPage";
import ManageFAQsPage from "./pages/admin/pages/ManageFAQsPage";
import ManagePagesSEOPage from "./pages/admin/pages/ManagePagesSEOPage";
import CreateSitemapPage from "./pages/admin/sitemap/CreateSitemapPage";
import ManageAPIKeysPage from "./pages/admin/mobile-api/ManageAPIKeysPage";
import PushNotificationsPage from "./pages/admin/mobile-api/PushNotificationsPage";
import BackupPage from "./pages/admin/BackupPage";
import SystemStatusPage from "./pages/admin/SystemStatusPage";
import ManageVideosPage from "./pages/admin/videos/ManageVideosPage";
import EditVideoPage from "./pages/admin/videos/EditVideoPage";
import FakeViewsPage from "./pages/admin/videos/FakeViewsPage";
import ManageCommentsPage from "./pages/admin/videos/ManageCommentsPage";
import VideoViewsPage from "./pages/admin/videos/VideoViewsPage";
import VideoLikesPage from "./pages/admin/videos/VideoLikesPage";
import VideoDislikesPage from "./pages/admin/videos/VideoDislikesPage";
import SavedVideosPage from "./pages/admin/videos/SavedVideosPage";
import SubscriptionsListPage from "./pages/admin/subscriptions/SubscriptionsListPage";
import ManagePostsPage from "./pages/admin/posts/ManagePostsPage";
import LogsPage from "./pages/admin/LogsPage";

import ImportFromEngaxePage from "./pages/admin/videos/ImportFromEngaxePage";
import AuthDebug from "./components/debug/AuthDebug"; // Debug panel
import FixVideosPage from "./pages/FixVideosPage";
import LanguageTestPage from "./pages/LanguageTestPage";

import EngaxeDemo from "./pages/EngaxeDemo";
// Removed test page imports

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <BrowserRouter>

      <ThemeProvider>
        <LanguageProvider>

          <AuthProvider>
            <VideoProvider>
              <MessageProvider>
                <NotificationProvider>
                  <ChatbotProvider>
                    <WatchlistProvider>
                    <Toaster />
                    <Sonner />
                    <Routes>
                      <Route path="/" element={<HomePage />} />
                      {/* CRITICAL FIX: Use videoUrl instead of videoId */}
                      <Route path="/video/:videoId" element={<VideoPage />} />
                      {/* Watch route for query parameter format */}
                      <Route path="/watch" element={<VideoPage />} />

                      <Route path="/channel/:channelName" element={<ChannelPage />} />
                      <Route path="/channels/:channelName" element={<ChannelPage />} />
                      <Route path="/channels" element={<ChannelsPage />} />
                      <Route path="/create-channel" element={<CreateChannelPage />} />
                      <Route path="/messages" element={<MessagesPage />} />
                      <Route path="/channel-chat" element={<ChannelChatPage />} />
                      <Route path="/signin" element={<SignInPage />} />
                      <Route path="/signup" element={<SignUpPage />} />
                      <Route path="/trending" element={<TrendingPage />} />
                      <Route path="/report-issue" element={<ReportIssuePage />} />
                      <Route path="/profile" element={<ProfilePage />} />
                      <Route path="/creator-studio" element={<CreatorStudioPage />} />
                      <Route path="/admin" element={<AdminPage />} />
                      <Route path="/admin/tools" element={<AdminToolsPage />} />
                      <Route path="/admin/users/manage" element={<ManageUsersPage />} />
                      <Route path="/admin/users/:userId/permissions" element={<UserPermissionsPage />} />
                      <Route path="/admin/users/verification" element={<VerificationRequestsPage />} />
                      <Route path="/admin/users/custom-fields" element={<ManageCustomProfileFieldsPage />} />
                      <Route path="/admin/users/custom-fields/:fieldId" element={<EditCustomFieldPage />} />
                      <Route path="/admin/languages/add" element={<AddNewLanguageKeyPage />} />
                      <Route path="/admin/languages/manage" element={<ManageLanguagesPage />} />
                      <Route path="/admin/videos/manage" element={<ManageVideosPage />} />
                      <Route path="/admin/videos/edit/:id" element={<EditVideoPage />} />
                      <Route path="/admin/videos/fake-views/:id" element={<FakeViewsPage />} />
                      <Route path="/admin/videos/import" element={<ImportVideosPage />} />

                      <Route path="/admin/videos/import/dailymotion" element={<ImportVideosPage />} />
                      <Route path="/admin/videos/import/twitch" element={<ImportVideosPage />} />
                      <Route path="/admin/videos/import/engaxe" element={<ImportFromEngaxePage />} />
                      <Route path="/admin/videos/fix" element={<FixVideosPage />} />
                      <Route path="/language-test/:videoId" element={<LanguageTestPage />} />
                      <Route path="/engaxe-demo" element={<EngaxeDemo />} />
                      <Route path="/admin/design/themes" element={<ThemesPage />} />
                      <Route path="/admin/articles/create" element={<CreateArticlePage />} />
                      <Route path="/admin/articles/manage" element={<ManageArticlesPage />} />
                      <Route path="/admin/articles/edit/:id" element={<EditArticlePage />} />
                      <Route path="/admin/categories/manage" element={<ManageCategoriesPage />} />
                      <Route path="/admin/categories/sub" element={<ManageSubCategoriesPage />} />
                      <Route path="/admin/tools/announcements" element={<AnnouncementsPage />} />
                      <Route path="/admin/tools/ban-users" element={<BanUsersPage />} />
                      <Route path="/admin/tools/activities" element={<ActivitiesPage />} />
                      <Route path="/admin/tools/mass-notifications" element={<MassNotificationsPage />} />
                      <Route path="/admin/tools/newsletters" element={<NewslettersPage />} />
                      <Route path="/admin/reports/video" element={<ManageVideoReportsPage />} />
                      <Route path="/admin/reports/copyright" element={<ManageCopyrightReportsPage />} />
                      <Route path="/admin/pages/custom" element={<ManageCustomPagesPage />} />
                      <Route path="/admin/pages/manage" element={<ManagePagesPage />} />
                      <Route path="/admin/pages/faqs" element={<ManageFAQsPage />} />
                      <Route path="/admin/pages/seo" element={<ManagePagesSEOPage />} />
                      <Route path="/admin/sitemap/create" element={<CreateSitemapPage />} />
                      <Route path="/admin/mobile-api/keys" element={<ManageAPIKeysPage />} />
                      <Route path="/admin/mobile-api/push" element={<PushNotificationsPage />} />
                      <Route path="/admin/backup" element={<BackupPage />} />
                      <Route path="/admin/system-status" element={<SystemStatusPage />} />
                      <Route path="/admin/logs" element={<LogsPage />} />
                      <Route path="/admin/videos/comments" element={<ManageCommentsPage />} />
                      <Route path="/admin/videos/views" element={<VideoViewsPage />} />
                      <Route path="/admin/videos/likes" element={<VideoLikesPage />} />
                      <Route path="/admin/videos/dislikes" element={<VideoDislikesPage />} />
                      <Route path="/admin/videos/saved" element={<SavedVideosPage />} />
                      <Route path="/admin/subscriptions/list" element={<SubscriptionsListPage />} />

                      {/* Posts Management Routes */}
                      <Route path="/admin/posts/manage" element={<ManagePostsPage />} />


                      <Route path="/settings" element={<SettingsPage />} />
                      <Route path="/settings/general" element={<SettingsPage />} />
                      <Route path="/settings/email" element={<SettingsPage />} />
                      <Route path="/settings/social" element={<SettingsPage />} />
                      <Route path="/settings/website-information" element={<SettingsPage />} />
                      <Route path="/settings/import-upload" element={<SettingsPage />} />
                      <Route path="/settings/video-player" element={<SettingsPage />} />
                      <Route path="/settings/live-streaming" element={<SettingsPage />} />
                      <Route path="/settings/cronjob-settings" element={<SettingsPage />} />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                    </WatchlistProvider>
                  </ChatbotProvider>
                </NotificationProvider>
              </MessageProvider>
            </VideoProvider>
          </AuthProvider>


        </LanguageProvider>
      </ThemeProvider>

    </BrowserRouter>
  </QueryClientProvider>
);

export default App;
