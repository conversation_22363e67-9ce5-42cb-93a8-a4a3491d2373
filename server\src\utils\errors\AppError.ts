/**
 * Custom application error class
 * Extends the built-in Error class with additional properties
 */
export class AppError extends Error {
  /**
   * HTTP status code for the error
   */
  statusCode: number;

  /**
   * Error code for client-side error handling
   */
  errorCode: string;

  /**
   * Whether this is an operational error (expected error)
   */
  isOperational: boolean;

  /**
   * Additional data to include in the error response
   */
  data?: any;

  /**
   * Create a new AppError
   * @param message Error message
   * @param statusCode HTTP status code
   * @param errorCode Error code for client-side error handling
   * @param isOperational Whether this is an operational error
   * @param data Additional data to include in the error response
   */
  constructor(
    message: string,
    statusCode: number = 500,
    errorCode: string = 'INTERNAL_SERVER_ERROR',
    isOperational: boolean = true,
    data?: any
  ) {
    super(message);
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = isOperational;
    this.data = data;
    
    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
    
    // Set the prototype explicitly
    Object.setPrototypeOf(this, AppError.prototype);
  }
}

/**
 * Create a bad request error (400)
 */
export function createBadRequestError(
  message: string = 'Bad request',
  errorCode: string = 'BAD_REQUEST',
  data?: any
): AppError {
  return new AppError(message, 400, errorCode, true, data);
}

/**
 * Create an unauthorized error (401)
 */
export function createUnauthorizedError(
  message: string = 'Unauthorized',
  errorCode: string = 'UNAUTHORIZED',
  data?: any
): AppError {
  return new AppError(message, 401, errorCode, true, data);
}

/**
 * Create a forbidden error (403)
 */
export function createForbiddenError(
  message: string = 'Forbidden',
  errorCode: string = 'FORBIDDEN',
  data?: any
): AppError {
  return new AppError(message, 403, errorCode, true, data);
}

/**
 * Create a not found error (404)
 */
export function createNotFoundError(
  message: string = 'Not found',
  errorCode: string = 'NOT_FOUND',
  data?: any
): AppError {
  return new AppError(message, 404, errorCode, true, data);
}

/**
 * Create a conflict error (409)
 */
export function createConflictError(
  message: string = 'Conflict',
  errorCode: string = 'CONFLICT',
  data?: any
): AppError {
  return new AppError(message, 409, errorCode, true, data);
}

/**
 * Create a validation error (422)
 */
export function createValidationError(
  message: string = 'Validation error',
  errorCode: string = 'VALIDATION_ERROR',
  data?: any
): AppError {
  return new AppError(message, 422, errorCode, true, data);
}

/**
 * Create a rate limit error (429)
 */
export function createRateLimitError(
  message: string = 'Too many requests',
  errorCode: string = 'RATE_LIMIT_EXCEEDED',
  data?: any
): AppError {
  return new AppError(message, 429, errorCode, true, data);
}

/**
 * Create an internal server error (500)
 */
export function createInternalServerError(
  message: string = 'Internal server error',
  errorCode: string = 'INTERNAL_SERVER_ERROR',
  data?: any
): AppError {
  return new AppError(message, 500, errorCode, true, data);
}

/**
 * Create a service unavailable error (503)
 */
export function createServiceUnavailableError(
  message: string = 'Service unavailable',
  errorCode: string = 'SERVICE_UNAVAILABLE',
  data?: any
): AppError {
  return new AppError(message, 503, errorCode, true, data);
}
