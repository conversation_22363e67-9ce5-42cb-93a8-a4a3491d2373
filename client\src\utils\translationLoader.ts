// This is a helper function to load translations
// It provides a workaround for dynamic imports in environments where they might not work as expected

import enTranslations from '../locales/en.json';
import hiTranslations from '../locales/hi.json';
import mrTranslations from '../locales/mr.json';
import guTranslations from '../locales/gu.json';
import taTranslations from '../locales/ta.json';
import teTranslations from '../locales/te.json';
import bnTranslations from '../locales/bn.json';
import knTranslations from '../locales/kn.json';
import mlTranslations from '../locales/ml.json';
import deTranslations from '../locales/de.json';

// Map of all available translations
const translationsMap: Record<string, Record<string, string>> = {
  en: enTranslations,
  hi: hiTranslations,
  mr: mrTranslations,
  gu: guTranslations,
  ta: taTranslations,
  te: teTranslations,
  bn: bnTranslations,
  kn: knTranslations,
  ml: mlTranslations,
  de: deTranslations
};

/**
 * Load translations for a specific language code
 * @param languageCode The language code to load translations for
 * @returns The translations object or English translations as fallback
 */
export function loadTranslations(languageCode: string): Record<string, string> {
  // Return the translations for the requested language or fall back to English
  return translationsMap[languageCode] || translationsMap.en;
}

/**
 * Get a list of all available language codes
 * @returns Array of language codes
 */
export function getAvailableLanguageCodes(): string[] {
  return Object.keys(translationsMap);
}
