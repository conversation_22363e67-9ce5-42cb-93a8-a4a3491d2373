import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import userController from '../controllers/user.controller';
import {
  registerUserSchema,
  loginUserSchema,
  getUserProfileSchema,
  updateUserProfileSchema,
  changePasswordSchema,
  getAllUsersSchema,
  deleteUserSchema,
  updateUserStatusSchema,
} from '../schemas/user.schema';
import { authenticate } from '../middleware/auth';
import { checkPermission } from '../middleware/rbac';
import { ErrorResponseSchema, SuccessResponseSchema, UserSchema, PaginationSchema } from '../middleware/swagger';
import { userActionLogger, adminActionLogger } from '../middleware/auditLogger';
import {
  AuthenticatedUser,
  FastifyAuthInstance,
  RegisterUserBody,
  LoginUserBody,
  ChangePasswordBody,
  UpdateUserStatusBody,
  UserIdParams,
  GetAllUsersQuery
} from '../types/user';

/**
 * User routes
 */
export default async function userRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Public routes
  fastify.post('/register', {
    preHandler: userActionLogger('register', 'User registration'),
    schema: {
      ...registerUserSchema,
      tags: ['Users'],
      description: 'Register a new user account',
      response: {
        201: {
          description: 'User registered successfully',
          ...(registerUserSchema.response as any)[201]
        },
        400: {
          description: 'Bad request',
          ...ErrorResponseSchema
        }
      }
    }
  }, userController.registerUser as any);

  fastify.post('/login', {
    preHandler: userActionLogger('login', 'User login'),
    schema: {
      ...loginUserSchema,
      tags: ['Users'],
      description: 'Login with email and password',
      response: {
        200: {
          description: 'Login successful',
          ...(loginUserSchema.response as any)[200]
        },
        401: {
          description: 'Unauthorized',
          ...ErrorResponseSchema
        }
      }
    }
  }, userController.loginUser as any);

  // Protected routes
  fastify.register(async (fastify: FastifyInstance) => {
    // Apply authentication middleware to all routes in this context
    fastify.addHook('preHandler', authenticate);

    // User profile routes
    fastify.get('/me', {
      schema: {
        tags: ['Users'],
        description: 'Get current user profile',
        response: {
          200: {
            description: 'User profile retrieved successfully',
            type: 'object',
            properties: {
              success: { type: 'boolean', default: true },
              user: UserSchema
            }
          },
          401: {
            description: 'Unauthorized',
            ...ErrorResponseSchema
          }
        }
      }
    }, userController.getCurrentUser as any);

    fastify.get('/:id', {
      schema: {
        ...getUserProfileSchema,
        tags: ['Users'],
        description: 'Get user profile by ID',
        response: {
          200: {
            description: 'User profile retrieved successfully',
            ...(getUserProfileSchema.response as any)[200]
          },
          404: {
            description: 'User not found',
            ...ErrorResponseSchema
          }
        }
      }
    }, userController.getUserProfile as any);

    fastify.put('/:id', {
      preHandler: userActionLogger('update-profile', 'User profile update'),
      schema: {
        ...updateUserProfileSchema,
        tags: ['Users'],
        description: 'Update user profile',
        response: {
          200: {
            description: 'User profile updated successfully',
            ...(updateUserProfileSchema.response as any)[200]
          },
          400: {
            description: 'Bad request',
            ...ErrorResponseSchema
          },
          404: {
            description: 'User not found',
            ...ErrorResponseSchema
          }
        }
      }
    }, userController.updateUserProfile as any);

    fastify.post('/change-password', {
      preHandler: userActionLogger('change-password', 'Password change'),
      schema: {
        ...changePasswordSchema,
        tags: ['Users'],
        description: 'Change user password',
        response: {
          200: {
            description: 'Password changed successfully',
            ...(changePasswordSchema.response as any)[200]
          },
          400: {
            description: 'Bad request',
            ...ErrorResponseSchema
          }
        }
      }
    }, userController.changePassword as any);

    // Admin routes
    fastify.register(async (fastify: FastifyInstance) => {
      // Apply permission check middleware to all routes in this context
      fastify.addHook('preHandler', checkPermission('user:read'));

      fastify.get('/', {
        schema: {
          ...getAllUsersSchema,
          tags: ['Users'],
          description: 'Get all users (admin only)',
          response: {
            200: {
              description: 'Users retrieved successfully',
              type: 'object',
              properties: {
                success: { type: 'boolean', default: true },
                users: {
                  type: 'array',
                  items: UserSchema
                },
                pagination: PaginationSchema
              }
            },
            401: {
              description: 'Unauthorized',
              ...ErrorResponseSchema
            },
            403: {
              description: 'Forbidden',
              ...ErrorResponseSchema
            }
          }
        }
      }, userController.getAllUsers as any);

      // Routes requiring specific permissions
      fastify.delete(
        '/:id',
        {
          schema: {
            ...deleteUserSchema,
            tags: ['Users'],
            description: 'Delete a user (admin only)',
            response: {
              200: {
                description: 'User deleted successfully',
                ...SuccessResponseSchema
              },
              401: {
                description: 'Unauthorized',
                ...ErrorResponseSchema
              },
              403: {
                description: 'Forbidden',
                ...ErrorResponseSchema
              },
              404: {
                description: 'User not found',
                ...ErrorResponseSchema
              }
            }
          },
          preHandler: [checkPermission('user:delete'), adminActionLogger('delete', 'user', 'User deletion')],
        },
        userController.deleteUser as any
      );

      fastify.patch(
        '/:id/status',
        {
          schema: {
            ...updateUserStatusSchema,
            tags: ['Users'],
            description: 'Update user status (admin only)',
            response: {
              200: {
                description: 'User status updated successfully',
                ...(updateUserStatusSchema.response as any)[200]
              },
              401: {
                description: 'Unauthorized',
                ...ErrorResponseSchema
              },
              403: {
                description: 'Forbidden',
                ...ErrorResponseSchema
              },
              404: {
                description: 'User not found',
                ...ErrorResponseSchema
              }
            }
          },
          preHandler: [checkPermission('user:update'), adminActionLogger('update-status', 'user', 'User status update')],
        },
        userController.updateUserStatus as any
      );
    });
  });
}
