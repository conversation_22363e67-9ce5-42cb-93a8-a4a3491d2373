import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import reportController from '../controllers/report.controller';
import { authenticate } from '../plugins/authenticate';
import { Type } from '@sinclair/typebox';

/**
 * Report routes
 */
export default async function reportRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Get all reports (admin only)
  fastify.get(
    '/',
    {
      preHandler: authenticate,
      schema: {
        tags: ['Reports'],
        description: 'Get all reports with filtering and pagination',
        querystring: Type.Object({
          page: Type.Optional(Type.Union([Type.Number(), Type.String()])),
          limit: Type.Optional(Type.Union([Type.Number(), Type.String()])),
          status: Type.Optional(Type.String()),
          reportType: Type.Optional(Type.String()),
          search: Type.Optional(Type.String()),
          sortBy: Type.Optional(Type.String()),
          sortDirection: Type.Optional(Type.Union([Type.Literal('asc'), Type.Literal('desc')])),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            data: Type.Object({
              reports: Type.Array(Type.Object({
                id: Type.String(),
                reportType: Type.String(),
                targetId: Type.String(),
                reporterId: Type.String(),
                reporterUsername: Type.String(),
                category: Type.String(),
                reason: Type.String(),
                description: Type.String(),
                evidence: Type.Array(Type.String()),
                status: Type.String(),
                priority: Type.String(),
                contentTitle: Type.Optional(Type.String()),
                createdAt: Type.String({ format: 'date-time' }),
                updatedAt: Type.String({ format: 'date-time' }),
              })),
              pagination: Type.Object({
                total: Type.Number(),
                page: Type.Number(),
                limit: Type.Number(),
                pages: Type.Number(),
              }),
            }),
          }),
        },
      },
    },
    reportController.getAllReports as any
  );

  // Get a report by ID (admin only)
  fastify.get(
    '/:id',
    {
      preHandler: authenticate,
      schema: {
        tags: ['Reports'],
        description: 'Get a report by ID',
        params: Type.Object({
          id: Type.String(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            data: Type.Object({
              id: Type.String(),
              reportType: Type.String(),
              targetId: Type.String(),
              reporterId: Type.String(),
              reporterUsername: Type.String(),
              category: Type.String(),
              reason: Type.String(),
              description: Type.String(),
              evidence: Type.Array(Type.String()),
              status: Type.String(),
              priority: Type.String(),
              contentTitle: Type.Optional(Type.String()),
              createdAt: Type.String({ format: 'date-time' }),
              updatedAt: Type.String({ format: 'date-time' }),
            }),
          }),
        },
      },
    },
    reportController.getReportById as any
  );

  // Create a new report (can be anonymous)
  fastify.post(
    '/',
    {
      schema: {
        tags: ['Reports'],
        description: 'Create a new report',
        body: Type.Object({
          reportType: Type.Union([
            Type.Literal('video'),
            Type.Literal('comment'),
            Type.Literal('user'),
            Type.Literal('channel'),
          ]),
          targetId: Type.String(),
          category: Type.String(),
          reason: Type.String(),
          description: Type.Optional(Type.String()),
          evidence: Type.Optional(Type.Array(Type.String())),
        }),
        response: {
          201: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
            data: Type.Object({
              id: Type.String(),
              reportType: Type.String(),
              targetId: Type.String(),
              reporterId: Type.Optional(Type.String()),
              reporterUsername: Type.Optional(Type.String()),
              category: Type.String(),
              reason: Type.String(),
              description: Type.String(),
              evidence: Type.Array(Type.String()),
              status: Type.String(),
              priority: Type.String(),
              contentTitle: Type.Optional(Type.String()),
              createdAt: Type.String({ format: 'date-time' }),
              updatedAt: Type.String({ format: 'date-time' }),
            }),
          }),
        },
      },
    },
    reportController.createReport as any
  );

  // Create a new issue report (can be anonymous)
  fastify.post(
    '/issue',
    {
      schema: {
        tags: ['Reports'],
        description: 'Create a new issue report',
        body: Type.Object({
          title: Type.String(),
          type: Type.Union([
            Type.Literal('bug'),
            Type.Literal('suggestion'),
            Type.Literal('other'),
          ]),
          description: Type.String(),
          evidence: Type.Optional(Type.Array(Type.String())),
        }),
        response: {
          201: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
            data: Type.Object({
              id: Type.String(),
              reportType: Type.String(),
              targetId: Type.String(),
              reporterId: Type.Optional(Type.String()),
              reporterUsername: Type.Optional(Type.String()),
              category: Type.String(),
              reason: Type.String(),
              description: Type.String(),
              evidence: Type.Array(Type.String()),
              status: Type.String(),
              priority: Type.String(),
              createdAt: Type.String({ format: 'date-time' }),
              updatedAt: Type.String({ format: 'date-time' }),
            }),
          }),
        },
      },
    },
    reportController.createIssueReport as any
  );

  // Update a report's status (admin only)
  fastify.put(
    '/:id/status',
    {
      preHandler: authenticate,
      schema: {
        tags: ['Reports'],
        description: 'Update a report\'s status',
        params: Type.Object({
          id: Type.String(),
        }),
        body: Type.Object({
          status: Type.Union([
            Type.Literal('pending'),
            Type.Literal('investigating'),
            Type.Literal('resolved'),
            Type.Literal('rejected'),
            Type.Literal('safe'),
          ]),
          note: Type.Optional(Type.String()),
          adminComment: Type.Optional(Type.String()),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
            data: Type.Object({
              id: Type.String(),
              status: Type.String(),
            }),
          }),
        },
      },
    },
    reportController.updateReportStatus as any
  );

  // Delete a report (admin only)
  fastify.delete(
    '/:id',
    {
      preHandler: authenticate,
      schema: {
        tags: ['Reports'],
        description: 'Delete a report',
        params: Type.Object({
          id: Type.String(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    reportController.deleteReport as any
  );

  // Bulk update report statuses (admin only)
  fastify.put(
    '/bulk/status',
    {
      preHandler: authenticate,
      schema: {
        tags: ['Reports'],
        description: 'Bulk update report statuses',
        body: Type.Object({
          ids: Type.Array(Type.String()),
          status: Type.Union([
            Type.Literal('pending'),
            Type.Literal('investigating'),
            Type.Literal('resolved'),
            Type.Literal('rejected'),
            Type.Literal('safe'),
          ]),
          note: Type.Optional(Type.String()),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    reportController.bulkUpdateReportStatus as any
  );

  // Bulk delete reports (admin only)
  fastify.delete(
    '/bulk',
    {
      preHandler: authenticate,
      schema: {
        tags: ['Reports'],
        description: 'Bulk delete reports',
        body: Type.Object({
          ids: Type.Array(Type.String()),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    reportController.bulkDeleteReports as any
  );
}
