
import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';


import { Bell, MessageSquare, Search, User, LogOut, Video as VideoIcon, Settings, ChevronDown, PlusCircle, SwitchCamera, Sun, Moon, Monitor, LayoutDashboard } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/context/AuthContext';
import { useNotifications } from '@/context/NotificationContext';
import { useLanguage } from '@/context/LanguageContext';
import { useVideos } from '@/context/VideoContext';
import { useTheme } from '@/context/ThemeContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { FallbackAvatar } from '@/components/ui/fallback-avatar';
import { getInitials, handleAvatarError } from '@/lib/avatar-utils';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import NotificationDropdown from '../notifications/NotificationDropdown';
import LanguageSwitcher from '../language/LanguageSwitcher';
import AddAccountDialog from '../profile/AddAccountDialog';
import AccountSwitcher from '../profile/AccountSwitcher';
import ThemeSwitcher from '../theme/ThemeSwitcher';

export default function Navbar() {
  const navigate = useNavigate();
  const { currentUser, userAccounts, switchAccount, logout, isCreator, isAdmin } = useAuth();
  const { unreadCount } = useNotifications();
  const { t } = useLanguage();
  const { videos } = useVideos();
  const { theme } = useTheme();

  const [showNotifications, setShowNotifications] = useState(false);
  const [showAddAccountDialog, setShowAddAccountDialog] = useState(false);

  // Search functionality state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Search functionality
  const performSearch = (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    setIsSearching(true);

    // Filter videos based on search query
    const filteredVideos = videos.filter(video => {
      const searchTerm = query.toLowerCase();
      return (
        video.title.toLowerCase().includes(searchTerm) ||
        video.description?.toLowerCase().includes(searchTerm) ||
        video.creator.username.toLowerCase().includes(searchTerm) ||
        video.category.toLowerCase().includes(searchTerm)
      );
    });

    setSearchResults(filteredVideos.slice(0, 8)); // Limit to 8 results
    setShowSearchResults(true);
    setIsSearching(false);
  };

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, videos]);

  // Handle click outside to close search results
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearchResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle search result click
  const handleSearchResultClick = (videoId: string) => {
    navigate(`/video/${videoId}`);
    setShowSearchResults(false);
    setSearchQuery('');
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle search input focus
  const handleSearchFocus = () => {
    if (searchQuery.trim() && searchResults.length > 0) {
      setShowSearchResults(true);
    }
  };

  return (
    <div className="fixed top-0 z-50 w-full border-b border-border bg-lingstream-background py-2">
      <div className="container flex items-center justify-between">
        <div className="flex items-center gap-8">
          <Link to="/" className="flex items-center gap-2">
            <span className="text-xl font-bold tracking-tight">
              <span className="text-lingstream-accent">{t('app.law')}</span>
              <span className="text-lingstream-text">{t('app.engaxe')}</span>
            </span>
          </Link>

          <nav className="hidden md:flex items-center space-x-4">
            <Link to="/" className="text-sm font-medium transition-colors hover:text-lingstream-accent">
              {t('nav.home')}
            </Link>
            <Link to="/trending" className="text-sm font-medium transition-colors hover:text-lingstream-accent">
              {t('nav.trending')}
            </Link>
            <Link to="/channels" className="text-sm font-medium transition-colors hover:text-lingstream-accent">
              {t('nav.channels')}
            </Link>
          </nav>
        </div>

        <div className="hidden md:flex items-center justify-center flex-1 max-w-md">
          <div className="relative w-full" ref={searchRef}>
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              ref={searchInputRef}
              placeholder={t('nav.search_placeholder')}
              className="w-full pl-8 bg-lingstream-card border-border"
              value={searchQuery}
              onChange={handleSearchChange}
              onFocus={handleSearchFocus}
            />

            {/* Search Results Dropdown */}
            {showSearchResults && (
              <div className={`absolute top-full left-0 right-0 mt-1 rounded-lg border shadow-lg z-50 max-h-96 overflow-y-auto ${
                theme === 'dark'
                  ? 'bg-gray-800 border-gray-700'
                  : 'bg-white border-gray-200'
              }`}>
                {isSearching ? (
                  <div className="p-4 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-lingstream-accent mx-auto"></div>
                    <p className="mt-2 text-sm text-muted-foreground">{t('common.searching')}</p>
                  </div>
                ) : searchResults.length > 0 ? (
                  <div className="py-2">
                    {searchResults.map((video) => (
                      <div
                        key={video.id}
                        className={`flex items-center gap-3 px-4 py-3 cursor-pointer transition-colors ${
                          theme === 'dark'
                            ? 'hover:bg-gray-700'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleSearchResultClick(video.id)}
                      >
                        <img
                          src={video.thumbnail || '/placeholder.svg'}
                          alt={video.title}
                          className="w-16 h-12 object-cover rounded"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder.svg';
                          }}
                        />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm truncate">{video.title}</h4>
                          <p className="text-xs text-muted-foreground truncate">
                            {video.creator.username} • {video.views.toLocaleString()} views
                          </p>
                          <p className="text-xs text-muted-foreground">{video.category}</p>
                        </div>
                        <VideoIcon className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      </div>
                    ))}
                  </div>
                ) : searchQuery.trim() ? (
                  <div className="p-4 text-center">
                    <p className="text-sm text-muted-foreground">{t('search.no_results')}</p>
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-4">
          <ThemeSwitcher />
          <LanguageSwitcher />
          {currentUser ? (
            <>
              <div className="relative">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setShowNotifications(!showNotifications)}
                  className="relative"
                >
                  <Bell className="h-5 w-5" aria-label={t('nav.notifications')} />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-lingstream-accent text-[10px] text-white">
                      {unreadCount}
                    </span>
                  )}
                </Button>
                {showNotifications && <NotificationDropdown onClose={() => setShowNotifications(false)} />}
              </div>

              <Link to="/messages">
                <Button variant="ghost" size="icon">
                  <MessageSquare className="h-5 w-5" aria-label={t('nav.messages')} />
                </Button>
              </Link>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full p-0"
                  >
                    <FallbackAvatar
                      name={currentUser.username}
                      username={currentUser.username}
                      size="sm"
                      className="h-8 w-8"
                    />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-lingstream-card border-border p-0">
                  <div className="px-2 py-1.5">
                    <div className="flex items-center gap-2 cursor-pointer p-2 rounded hover:bg-lingstream-card/50">
                      <div className="flex flex-col">
                        <span className="font-medium">{currentUser.email || currentUser.username}</span>
                      </div>
                    </div>
                  </div>

                  <DropdownMenuSeparator className="my-1" />

                  {/* Switch Account Button */}
                  <div className="px-2 py-1.5">
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-center gap-2 bg-white border border-black text-black"
                    >
                      <SwitchCamera className="h-4 w-4" />
                      <span>{t('nav.switch_account')}</span>
                    </Button>
                  </div>

                  <DropdownMenuSeparator className="my-1" />

                  {/* User Accounts */}
                  {userAccounts && userAccounts.map((account) => (
                    <DropdownMenuItem
                      key={account.id}
                      className={`flex items-center gap-3 py-2 ${account.id === currentUser.id ? 'bg-lingstream-accent/20' : ''}`}
                      onClick={() => {
                        if (account.id !== currentUser.id) {
                          switchAccount(account.id);
                        }
                      }}
                    >
                      <Avatar className="h-6 w-6 group">
                        <AvatarImage
                          src={account.avatar || ''}
                          alt={account.username}
                          onError={(e) => handleAvatarError(e, account.username)}
                        />
                        <AvatarFallback>{getInitials(account.username)}</AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="font-medium">{account.username}</span>
                        <span className="text-xs text-muted-foreground">{account.email}</span>
                      </div>
                    </DropdownMenuItem>
                  ))}

                  {/* Add Account Button */}
                  <div className="px-2 py-1.5">
                    <Button
                      variant="outline"
                      className="w-full flex items-center justify-center gap-2 bg-white border border-black text-black"
                      onClick={() => setShowAddAccountDialog(true)}
                    >
                      <PlusCircle className="h-4 w-4" />
                      <span>{t('nav.add_account')}</span>
                    </Button>
                  </div>

                  <DropdownMenuSeparator className="my-1" />

                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/profile">
                      <User className="h-4 w-4 mr-2" />
                      {t('nav.profile')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/creator-studio">
                      <VideoIcon className="h-4 w-4 mr-2" />
                      {t('nav.creator_studio')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/create-channel">
                      <PlusCircle className="h-4 w-4 mr-2" />
                      {t('nav.create_channel')}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem className="flex items-center gap-2" asChild>
                    <Link to="/profile?tab=settings">
                      <Settings className="h-4 w-4 mr-2" />
                      {t('profile.settings')}
                    </Link>
                  </DropdownMenuItem>
                  {isAdmin && (
                    <DropdownMenuItem className="flex items-center gap-2" asChild>
                      <Link to="/admin">
                        <LayoutDashboard className="h-4 w-4 mr-2" />
                        {t('nav.admin_panel')}
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    className="flex items-center gap-2 text-destructive focus:text-destructive"
                    onClick={logout}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    {t('nav.logout')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Link to="/signin">
                <Button variant="outline" size="sm" className="hover:bg-accent hover:text-accent-foreground">{t('auth.sign_in')}</Button>
              </Link>
              <Link to="/signup">
                <Button size="sm" className="hover:bg-primary/90">{t('auth.sign_up')}</Button>
              </Link>
            </>
          )}
        </div>
      </div>

      {/* Add Account Dialog */}
      <AddAccountDialog
        open={showAddAccountDialog}
        onOpenChange={setShowAddAccountDialog}
      />
    </div>
  );
}
