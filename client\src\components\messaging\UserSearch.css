.user-search {
  position: relative;
  width: 100%;
}

.search-input-container {
  position: relative;
  width: 100%;
  /* Removing border as requested */
  border: none;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 10px 15px;
  padding-left: 40px; /* Changed from padding-right to padding-left */
  border: 1px solid var(--border);
  border-radius: 5px;
  background-color: var(--background);
  color: var(--foreground);
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: var(--ring);
}

.search-button {
  position: absolute;
  left: 10px; /* Changed from right to left */
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--muted-foreground);
  z-index: 2;
  padding: 5px;
  border-radius: 50%;
  transition: background-color 0.2s, color 0.2s;
}

.search-button:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
}

.search-icon {
  width: 18px;
  height: 18px;
}

.search-loading {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--muted-foreground);
  font-size: 12px;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 0 0 5px 5px;
  z-index: 10;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.light .search-results {
  background-color: #f0f0f0; /* Grey color for light mode */
}

.dark .search-results {
  background-color: #2c3e50; /* Darker color for dark mode */
}

.search-results-header {
  padding: 10px 15px;
  font-size: 12px;
  font-weight: bold;
  color: hsl(var(--muted-foreground));
  background-color: hsl(var(--secondary));
  border-bottom: 1px solid hsl(var(--border));
}

.light .search-results-header {
  background-color: #e0e0e0; /* Lighter grey for header in light mode */
  color: #333;
}

.dark .search-results-header {
  background-color: #1a242f; /* Darker color for header in dark mode */
  color: #bdc3c7;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.light .user-item {
  color: #333;
}

.dark .user-item {
  color: #ecf0f1;
}

.user-item:hover {
  background-color: hsl(var(--accent) / 0.2);
}

.light .user-item:hover {
  background-color: #e0e0e0;
}

.dark .user-item:hover {
  background-color: #34495e;
}

.user-avatar {
  position: relative;
  width: 40px;
  height: 40px;
  margin-right: 15px;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid var(--background);
}

.status-indicator.online {
  background-color: #2ecc71;
}

.status-indicator.offline {
  background-color: var(--muted-foreground);
}

.user-info {
  flex-grow: 1;
  overflow: hidden;
}

.user-name {
  font-weight: bold;
  color: var(--foreground);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-username {
  font-size: 12px;
  color: var(--muted-foreground);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message-time {
  font-size: 12px;
  color: var(--muted-foreground);
  margin-left: 10px;
}

.no-results {
  padding: 15px;
  text-align: center;
  color: hsl(var(--foreground));
  font-style: italic;
}

.light .no-results {
  color: #333; /* Dark text for light mode */
}

.dark .no-results {
  color: #ffffff; /* White text for dark mode */
}
