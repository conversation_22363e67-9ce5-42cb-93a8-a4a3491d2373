import { FastifyRequest, FastifyReply } from 'fastify';
import logService from '../services/log.service';
import { AuthenticatedUser } from '../types/user';
import { LogModel } from '../models';

/**
 * Configuration for audit logging
 */
interface AuditLogConfig {
  /** Whether to log this route */
  enabled: boolean;

  /** Action to log (e.g., 'create', 'update', 'delete') */
  action: string;

  /** Category of the action (e.g., 'user', 'content', 'security') */
  category: string;

  /** Type of resource affected (e.g., 'user', 'video', 'role') */
  resourceType: string;

  /** Function to extract resource ID from request */
  getResourceId?: (request: FastifyRequest) => string | undefined;

  /** Function to extract previous state from request */
  getPreviousState?: (request: FastifyRequest) => Record<string, any> | undefined;

  /** Function to extract new state from request */
  getNewState?: (request: FastifyRequest) => Record<string, any> | undefined;

  /** Status of the action (default: 'success') */
  status?: 'success' | 'failure' | 'warning' | 'info';

  /** Severity level of the log entry (default: 'low') */
  severity?: 'low' | 'medium' | 'high' | 'critical';

  /** Additional details about the action */
  details?: string | ((request: FastifyRequest) => string);
}

/**
 * Create audit logger middleware
 * @param config Audit log configuration
 * @returns Middleware function
 */
export function createAuditLogger(config: AuditLogConfig) {
  return async function auditLogger(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    // Skip if logging is disabled
    if (!config.enabled) {
      return;
    }

    // Store original request information before response is sent
    const user = request.user as AuthenticatedUser;
    const userId = user?.id || 'anonymous';
    const userIp = request.ip;
    const userAgent = request.headers['user-agent'] || 'Unknown';
    const resourceId = config.getResourceId ? config.getResourceId(request) : undefined;
    const previousState = config.getPreviousState ? config.getPreviousState(request) : undefined;
    const newState = config.getNewState ? config.getNewState(request) : undefined;
    const details = typeof config.details === 'function' ? config.details(request) : config.details;

    // Create a function to log after the response
    const createLogEntry = async () => {
      try {
        // Determine status based on response status code
        let status = config.status || 'success';
        if (reply.statusCode >= 400 && reply.statusCode < 500) {
          status = 'warning';
        } else if (reply.statusCode >= 500) {
          status = 'failure';
        }

        // Create log directly using the model instead of the service
        const log = new LogModel({
          action: config.action,
          category: config.category,
          resourceType: config.resourceType,
          resourceId,
          previousState,
          newState,
          status,
          severity: config.severity || 'low',
          details,
          userId,
          userIp,
          userAgent,
          createdBy: userId,
          updatedBy: userId,
        });

        await log.save();
        console.log(`Audit log created: ${log.id}`);
      } catch (error) {
        console.error('Error creating audit log:', error);
      }
    };

    // Execute after the response is sent
    reply.raw.on('finish', createLogEntry);
  };
}

/**
 * Create audit logger for user actions
 * @param action Action to log
 * @param details Additional details
 * @returns Middleware function
 */
export function userActionLogger(action: string, details?: string) {
  return createAuditLogger({
    enabled: true,
    action,
    category: 'user',
    resourceType: 'user',
    getResourceId: (request) => {
      if (request.params && (request.params as any).id) {
        return (request.params as any).id;
      }
      if (request.body && (request.body as any).userId) {
        return (request.body as any).userId;
      }
      return undefined;
    },
    getNewState: (request) => {
      if (request.body && typeof request.body === 'object') {
        // Filter out sensitive information
        const { password, ...safeData } = request.body as any;
        return Object.keys(safeData).length > 0 ? safeData : undefined;
      }
      return undefined;
    },
    details,
    severity: 'medium',
  });
}

/**
 * Create audit logger for authentication actions
 * @param action Action to log
 * @param details Additional details
 * @returns Middleware function
 */
export function authActionLogger(action: string, details?: string) {
  return createAuditLogger({
    enabled: true,
    action,
    category: 'security',
    resourceType: 'auth',
    getResourceId: (request) => {
      if (request.body && (request.body as any).email) {
        return (request.body as any).email;
      }
      return undefined;
    },
    details,
    severity: 'medium',
  });
}

/**
 * Create audit logger for admin actions
 * @param action Action to log
 * @param resourceType Type of resource affected
 * @param details Additional details
 * @returns Middleware function
 */
export function adminActionLogger(action: string, resourceType: string, details?: string) {
  return createAuditLogger({
    enabled: true,
    action,
    category: 'admin',
    resourceType,
    getResourceId: (request) => {
      if (request.params && (request.params as any).id) {
        return (request.params as any).id;
      }
      return undefined;
    },
    getNewState: (request) => {
      if (request.body && typeof request.body === 'object') {
        return request.body as any;
      }
      return undefined;
    },
    details,
    severity: 'high',
  });
}
