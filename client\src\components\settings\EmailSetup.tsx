import React, { useState } from 'react';
import { Mail, Server, Lock, Globe, Send, AlertTriangle, Info, Terminal, Play } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export default function EmailSetup() {
  const [serverType, setServerType] = useState('SMTP Server');
  const [smtpHost, setSmtpHost] = useState('smtp.zoho.in');
  const [smtpUsername, setSmtpUsername] = useState('<EMAIL>');
  const [smtpPassword, setSmtpPassword] = useState('0GjtlaGpzQb0EO/d4mDo2Q==');
  const [smtpPort, setSmtpPort] = useState('587');
  const [smtpEncryption, setSmtpEncryption] = useState('TLS');
  const [activeTab, setActiveTab] = useState('general');
  const [debugLog, setDebugLog] = useState('Click on Debug Email Deliverability to show test results.');

  const handleTestEmail = () => {
    alert('Test email sent successfully!');
  };

  const handleDebugEmail = () => {
    setDebugLog(`[${new Date().toLocaleTimeString()}] Starting email delivery test...
[${new Date().toLocaleTimeString()}] Checking SMTP configuration...
[${new Date().toLocaleTimeString()}] Connecting to ${smtpHost}:${smtpPort}...
[${new Date().toLocaleTimeString()}] Connection successful
[${new Date().toLocaleTimeString()}] Authenticating with username: ${smtpUsername}...
[${new Date().toLocaleTimeString()}] Authentication successful
[${new Date().toLocaleTimeString()}] Preparing test email...
[${new Date().toLocaleTimeString()}] Sending test email...
[${new Date().toLocaleTimeString()}] Test email sent successfully
[${new Date().toLocaleTimeString()}] Test completed successfully`);
  };

  return (
    <div>
      {/* Breadcrumb Navigation */}
      <div className="flex items-center gap-2 text-sm mb-4 bg-blue-50 p-2 rounded">
        <span className="flex items-center gap-1 text-black font-medium">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          Admin Panel
        </span>
        <span className="text-gray-600">›</span>
        <span className="text-black">Settings</span>
        <span className="text-gray-600">›</span>
        <span className="text-blue-600 font-medium">E-mail Setup</span>
      </div>

      <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email Configuration
          </TabsTrigger>
          <TabsTrigger value="debug" className="flex items-center gap-2">
            <Terminal className="h-4 w-4" />
            Debug & Testing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-primary" />
                Email Configuration
              </CardTitle>
              <CardDescription>
                Configure your website's email settings and SMTP server details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <Alert className="bg-amber-50 border-amber-200">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                <AlertDescription className="text-amber-800">
                  Your SMTP email username should be same as website email, or at least from same server.
                </AlertDescription>
              </Alert>

              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="site-email" className="text-black">Site E-mail</Label>
                  <Input
                    id="site-email"
                    type="email"
                    defaultValue="<EMAIL>"
                    className="border-gray-300"
                  />
                  <p className="text-sm text-gray-500">Your website E-mail, it will appear on website's footer and E-mails.</p>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="contact-email" className="text-black">Contact Us Email</Label>
                  <Input
                    id="contact-email"
                    type="email"
                    defaultValue="<EMAIL>"
                    className="border-gray-300"
                  />
                  <p className="text-sm text-gray-500">Receive emails from contact us form to this email. This email should not be the same as the default website email.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5 text-primary" />
                SMTP Configuration
              </CardTitle>
              <CardDescription>
                Configure your SMTP server settings for sending emails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="server-type" className="text-black">Server Type</Label>
                <Select value={serverType} onValueChange={setServerType}>
                  <SelectTrigger id="server-type" className="border-gray-300">
                    <SelectValue placeholder="Select server type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SMTP Server">SMTP Server</SelectItem>
                    <SelectItem value="Server Mail (Default)">Server Mail (Default)</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500">Select which E-mail server you want to use, Server Mail function is not recommended.</p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="smtp-host" className="text-black">SMTP Host</Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="smtp-host"
                    type="text"
                    value={smtpHost}
                    onChange={(e) => setSmtpHost(e.target.value)}
                    className="border-gray-300 pl-10"
                  />
                </div>
                <p className="text-sm text-gray-500">Your SMTP account host name, can be IP, domain or subdomain.</p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="smtp-username" className="text-black">SMTP Username</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="smtp-username"
                    type="text"
                    value={smtpUsername}
                    onChange={(e) => setSmtpUsername(e.target.value)}
                    className="border-gray-300 pl-10"
                  />
                </div>
                <p className="text-sm text-gray-500">Your SMTP account username.</p>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="smtp-password" className="text-black">SMTP Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="smtp-password"
                    type="password"
                    value={smtpPassword}
                    onChange={(e) => setSmtpPassword(e.target.value)}
                    className="border-gray-300 pl-10"
                  />
                </div>
                <p className="text-sm text-gray-500">Your SMTP account password.</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="smtp-port" className="text-black">SMTP Port</Label>
                  <Input
                    id="smtp-port"
                    type="text"
                    value={smtpPort}
                    onChange={(e) => setSmtpPort(e.target.value)}
                    className="border-gray-300"
                  />
                  <p className="text-sm text-gray-500">Which port does your SMTP server use? Most used 587 for TLS, and 465 for SSL encryption.</p>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="smtp-encryption" className="text-black">SMTP Encryption</Label>
                  <Select value={smtpEncryption} onValueChange={setSmtpEncryption}>
                    <SelectTrigger id="smtp-encryption" className="border-gray-300">
                      <SelectValue placeholder="Select encryption" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TLS">TLS</SelectItem>
                      <SelectItem value="SSL">SSL</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500">Which encryption method does your SMTP server use?</p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleTestEmail}
                className="bg-cyan-500 hover:bg-cyan-600 text-white"
              >
                <Send className="mr-2 h-4 w-4" /> Test E-mail Server
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="debug" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Terminal className="h-5 w-5 text-primary" />
                Debug Email Deliverability
              </CardTitle>
              <CardDescription>
                Test and debug your email delivery system
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="bg-blue-50 border-blue-200">
                <Info className="h-4 w-4 text-blue-500" />
                <AlertDescription className="text-blue-800">
                  This feature will test the Email Deliverability and make sure the system is working fine.
                </AlertDescription>
              </Alert>

              <div className="grid gap-2">
                <Label htmlFor="debug-log" className="text-black">Debug Log</Label>
                <div className="bg-gray-100 p-4 rounded-md h-64 overflow-y-auto text-black font-mono text-sm">
                  {debugLog.split('\n').map((line, index) => (
                    <div key={index} className="py-1">{line}</div>
                  ))}
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleDebugEmail}
                className="bg-emerald-500 hover:bg-emerald-600 text-white w-full"
              >
                <Play className="mr-2 h-4 w-4" /> Debug Email Deliverability
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
