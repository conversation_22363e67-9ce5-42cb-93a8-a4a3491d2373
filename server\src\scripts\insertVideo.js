// Script to directly insert a video into the database
// Run with: node insertVideo.js

const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

// MongoDB connection URI
const MONGODB_URI = 'mongodb://localhost:27017/lawengaxe';

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => console.log('Connected to MongoDB'))
  .catch(err => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: { type: String, required: true, unique: true },
  title: { type: String, required: true },
  description: { type: String, required: true },
  url: { type: String, required: true },
  thumbnailUrl: { type: String, required: true },
  duration: { type: Number, required: true },
  userId: { type: String, required: true },
  channelId: { type: String, required: true },
  visibility: {
    type: String,
    enum: ['public', 'unlisted', 'private', 'scheduled'],
    default: 'public'
  },
  tags: { type: [String], default: [] },
  category: { type: String, required: true },
  contentRating: {
    type: String,
    enum: ['general', 'teen', 'mature', 'explicit'],
    default: 'general'
  },
  processingStatus: {
    type: String,
    enum: ['uploading', 'processing', 'ready', 'failed', 'transcoding'],
    default: 'ready'
  },
  commentsEnabled: { type: Boolean, default: true },
  ratingsEnabled: { type: Boolean, default: true },
  embeddingEnabled: { type: Boolean, default: true },
  stats: {
    views: { type: Number, default: 0 },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    comments: { type: Number, default: 0 },
    shares: { type: Number, default: 0 }
  },
  file: {
    originalName: { type: String, required: true },
    size: { type: Number, required: true },
    mimeType: { type: String, required: true }
  },
  source: {
    type: { type: String, enum: ['import', 'embed'] },
    originalUrl: { type: String },
    platform: { type: String },
    externalId: { type: String }
  },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
  deletedAt: { type: Date }
});

// Create the Video model
const Video = mongoose.model('Video', VideoSchema);

// Create a new video document
const newVideo = new Video({
  id: uuidv4(),
  title: 'Sample Video Added Directly',
  description: 'This is a sample video added directly to the database',
  url: 'https://engaxe.com/videos/sample123',
  thumbnailUrl: 'https://example.com/thumbnail.jpg',
  duration: 300, // 5 minutes
  userId: 'eb7d9625-d11c-40a7-b476-4fdd6cb3c4fb', // Replace with actual user ID
  channelId: '956c2164-d429-4101-a6fb-c10c8cf6a62a', // Replace with actual channel ID
  visibility: 'public',
  tags: ['sample', 'test', 'direct-insert'],
  category: 'Education',
  contentRating: 'general',
  processingStatus: 'ready',
  commentsEnabled: true,
  ratingsEnabled: true,
  embeddingEnabled: true,
  stats: {
    views: 0,
    likes: 0,
    dislikes: 0,
    comments: 0,
    shares: 0
  },
  file: {
    originalName: 'sample.mp4',
    size: 10485760, // 10MB
    mimeType: 'video/mp4'
  },
  source: {
    type: 'embed',
    originalUrl: 'https://engaxe.com/videos/sample123',
    platform: 'engaxe',
    externalId: 'sample123'
  }
});

// Save the video to the database
newVideo.save()
  .then(video => {
    console.log('Video saved successfully:', video);
    mongoose.disconnect();
  })
  .catch(err => {
    console.error('Error saving video:', err);
    mongoose.disconnect();
    process.exit(1);
  });
