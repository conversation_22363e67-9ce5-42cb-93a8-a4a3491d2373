import fs from 'fs';
import path from 'path';

/**
 * Utility class for JSON file storage operations
 */
export class JsonStorageUtil {
  private static readonly JSON_DIR = path.join(process.cwd(), 'json', 'videos');
  private static readonly ALL_VIDEOS_FILE = path.join(JsonStorageUtil.JSON_DIR, 'all-videos.json');
  private static readonly BACKUP_DIR = path.join(JsonStorageUtil.JSON_DIR, 'backups');

  /**
   * Initialize storage directories
   */
  static async initializeStorage(): Promise<void> {
    try {
      // Create main videos directory
      if (!fs.existsSync(JsonStorageUtil.JSON_DIR)) {
        fs.mkdirSync(JsonStorageUtil.JSON_DIR, { recursive: true });
      }

      // Create backup directory
      if (!fs.existsSync(JsonStorageUtil.BACKUP_DIR)) {
        fs.mkdirSync(JsonStorageUtil.BACKUP_DIR, { recursive: true });
      }

      // Create initial all-videos.json if it doesn't exist
      if (!fs.existsSync(JsonStorageUtil.ALL_VIDEOS_FILE)) {
        const initialData = {
          metadata: {
            totalVideos: 0,
            lastUpdated: new Date().toISOString(),
            version: '1.0.0'
          },
          videos: []
        };
        fs.writeFileSync(JsonStorageUtil.ALL_VIDEOS_FILE, JSON.stringify(initialData, null, 2));
      }
    } catch (error) {
      console.error('Error initializing JSON storage:', error);
      throw error;
    }
  }

  /**
   * Read all videos from JSON file
   */
  static async readAllVideos(): Promise<any> {
    try {
      if (!fs.existsSync(JsonStorageUtil.ALL_VIDEOS_FILE)) {
        await JsonStorageUtil.initializeStorage();
      }

      const data = fs.readFileSync(JsonStorageUtil.ALL_VIDEOS_FILE, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error reading videos JSON:', error);
      return {
        metadata: {
          totalVideos: 0,
          lastUpdated: new Date().toISOString(),
          version: '1.0.0'
        },
        videos: []
      };
    }
  }

  /**
   * Write all videos to JSON file
   */
  static async writeAllVideos(videosData: any): Promise<void> {
    try {
      // Create backup before writing
      await JsonStorageUtil.createBackup();

      // Update metadata
      videosData.metadata = {
        ...videosData.metadata,
        totalVideos: videosData.videos.length,
        lastUpdated: new Date().toISOString()
      };

      // Write to file
      fs.writeFileSync(JsonStorageUtil.ALL_VIDEOS_FILE, JSON.stringify(videosData, null, 2));
    } catch (error) {
      console.error('Error writing videos JSON:', error);
      throw error;
    }
  }

  /**
   * Create backup of current JSON file
   */
  static async createBackup(): Promise<void> {
    try {
      if (fs.existsSync(JsonStorageUtil.ALL_VIDEOS_FILE)) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = path.join(JsonStorageUtil.BACKUP_DIR, `all-videos-backup-${timestamp}.json`);
        fs.copyFileSync(JsonStorageUtil.ALL_VIDEOS_FILE, backupFile);

        // Keep only last 10 backups
        await JsonStorageUtil.cleanupOldBackups();
      }
    } catch (error) {
      console.error('Error creating backup:', error);
    }
  }

  /**
   * Clean up old backup files (keep only last 10)
   */
  static async cleanupOldBackups(): Promise<void> {
    try {
      const files = fs.readdirSync(JsonStorageUtil.BACKUP_DIR)
        .filter(file => file.startsWith('all-videos-backup-'))
        .map(file => ({
          name: file,
          path: path.join(JsonStorageUtil.BACKUP_DIR, file),
          time: fs.statSync(path.join(JsonStorageUtil.BACKUP_DIR, file)).mtime
        }))
        .sort((a, b) => b.time.getTime() - a.time.getTime());

      // Keep only the 10 most recent backups
      if (files.length > 10) {
        const filesToDelete = files.slice(10);
        filesToDelete.forEach(file => {
          fs.unlinkSync(file.path);
        });
      }
    } catch (error) {
      console.error('Error cleaning up old backups:', error);
    }
  }

  /**
   * Format video data for JSON storage
   */
  static formatVideoForStorage(video: any): any {
    return {
      id: video.id,
      title: video.title,
      description: video.description,
      url: video.url,
      thumbnailUrl: video.thumbnailUrl,
      duration: video.duration,
      userId: video.userId,
      channelId: video.channelId,
      visibility: video.visibility,
      tags: video.tags || [],
      category: video.category,
      contentRating: video.contentRating,
      processingStatus: video.processingStatus,
      stats: video.stats || {
        views: 0,
        likes: 0,
        dislikes: 0,
        comments: 0
      },
      file: video.file || {},
      languages: video.languages || [],
      source: video.source || {},
      createdAt: video.createdAt,
      updatedAt: video.updatedAt,
      // Additional fields for search functionality
      searchKeywords: JsonStorageUtil.generateSearchKeywords(video),
      watchUrl: `http://localhost:5173/watch?id=${video.id}`
    };
  }

  /**
   * Generate search keywords from video data
   */
  static generateSearchKeywords(video: any): string[] {
    const keywords = new Set<string>();

    // Add title words
    if (video.title) {
      video.title.toLowerCase().split(/\s+/).forEach((word: string) => {
        if (word.length > 2) keywords.add(word);
      });
    }

    // Add description words
    if (video.description) {
      video.description.toLowerCase().split(/\s+/).forEach((word: string) => {
        if (word.length > 2) keywords.add(word);
      });
    }

    // Add tags
    if (video.tags && Array.isArray(video.tags)) {
      video.tags.forEach((tag: string) => {
        keywords.add(tag.toLowerCase());
      });
    }

    // Add category
    if (video.category) {
      keywords.add(video.category.toLowerCase());
    }

    return Array.from(keywords);
  }
}
