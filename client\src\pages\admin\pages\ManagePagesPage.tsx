import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Home,
  FileText,
  Edit,
  ToggleLeft,
  ToggleRight,
  Plus,
  Search,
  Eye,
  Calendar,
  MoreHorizontal
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Enhanced page model
interface Page {
  id: number;
  name: string;
  status: 'active' | 'disabled';
  lastUpdated: string;
  views: number;
  content?: string;
}

// Mock data for terms pages
const mockPages: Page[] = [
  {
    id: 1,
    name: 'Terms of Use',
    status: 'active',
    lastUpdated: '2023-10-15T14:30:00Z',
    views: 1245,
    content: 'These terms and conditions outline the rules and regulations for the use of our website.'
  },
  {
    id: 2,
    name: 'Privacy Policy',
    status: 'active',
    lastUpdated: '2023-09-22T11:15:00Z',
    views: 987,
    content: 'This privacy policy sets out how we use and protect any information that you give us when you use this website.'
  },
  {
    id: 3,
    name: 'About',
    status: 'active',
    lastUpdated: '2023-08-10T09:45:00Z',
    views: 654,
    content: 'Learn more about our company, mission, and values.'
  },
  {
    id: 4,
    name: 'Refund',
    status: 'active',
    lastUpdated: '2023-11-05T16:20:00Z',
    views: 432,
    content: 'Our refund policy details the conditions under which we provide refunds for our products and services.'
  },
];

export default function ManagePagesPage() {
  const [pages, setPages] = useState<Page[]>(mockPages);
  const [searchTerm, setSearchTerm] = useState('');
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentPage, setCurrentPage] = useState<Page | null>(null);

  // Filter pages based on search term
  const filteredPages = pages.filter(page =>
    page.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle edit page
  const handleEdit = (page: Page) => {
    setCurrentPage(page);
    setShowEditDialog(true);
  };

  // Handle toggle page status
  const handleToggleStatus = (pageId: number) => {
    setPages(pages.map(page => {
      if (page.id === pageId) {
        return {
          ...page,
          status: page.status === 'active' ? 'disabled' : 'active'
        };
      }
      return page;
    }));
  };

  // Handle save page
  const handleSavePage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!currentPage) return;

    const formData = new FormData(e.currentTarget);

    const updatedPage: Page = {
      ...currentPage,
      name: formData.get('name') as string,
      content: formData.get('content') as string,
      lastUpdated: new Date().toISOString()
    };

    setPages(pages.map(page => page.id === currentPage.id ? updatedPage : page));
    setShowEditDialog(false);
    setCurrentPage(null);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Active</Badge>;
      case 'disabled':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Disabled</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Manage Terms Pages</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Content</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Pages</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Manage Terms Pages</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Main Content */}
            <Card>
              <CardHeader>
                <CardTitle>Manage Terms Pages</CardTitle>
                <CardDescription>
                  Edit and manage the standard terms pages for your website
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search pages..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-8"
                    />
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Page Name</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Updated</TableHead>
                        <TableHead>Views</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPages.length > 0 ? (
                        filteredPages.map((page) => (
                          <TableRow key={page.id}>
                            <TableCell className="font-medium">
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-muted-foreground" />
                                {page.name}
                              </div>
                            </TableCell>
                            <TableCell>{getStatusBadge(page.status)}</TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                                {formatDate(page.lastUpdated)}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                <Eye className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                                {page.views.toLocaleString()}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-8 bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                  onClick={() => handleEdit(page)}
                                >
                                  <Edit className="h-3.5 w-3.5 mr-1" /> Edit
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className={page.status === 'active' ?
                                    "h-8 bg-yellow-50 hover:bg-yellow-100 text-yellow-600 border-yellow-200" :
                                    "h-8 bg-green-50 hover:bg-green-100 text-green-600 border-green-200"}
                                  onClick={() => handleToggleStatus(page.id)}
                                >
                                  {page.status === 'active' ? (
                                    <>
                                      <ToggleLeft className="h-3.5 w-3.5 mr-1" /> Disable
                                    </>
                                  ) : (
                                    <>
                                      <ToggleRight className="h-3.5 w-3.5 mr-1" /> Enable
                                    </>
                                  )}
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem onClick={() => handleEdit(page)}>
                                      <Edit className="h-4 w-4 mr-2" /> Edit
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleToggleStatus(page.id)}>
                                      {page.status === 'active' ? (
                                        <>
                                          <ToggleLeft className="h-4 w-4 mr-2" /> Disable
                                        </>
                                      ) : (
                                        <>
                                          <ToggleRight className="h-4 w-4 mr-2" /> Enable
                                        </>
                                      )}
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem>
                                      <Eye className="h-4 w-4 mr-2" /> View Page
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                            No pages found matching your search.
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <div className="text-sm text-muted-foreground">
                  Showing {filteredPages.length} of {pages.length} pages
                </div>
              </CardFooter>
            </Card>
          </div>
        </main>
      </div>

      {/* Edit Page Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit {currentPage?.name}</DialogTitle>
            <DialogDescription>
              Make changes to the page content and settings.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSavePage}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Page Name
                </Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={currentPage?.name || ''}
                  className="col-span-3"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="content" className="text-right pt-2">
                  Content
                </Label>
                <Textarea
                  id="content"
                  name="content"
                  defaultValue={currentPage?.content || ''}
                  className="col-span-3 min-h-[200px]"
                  required
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right">
                  Status
                </Label>
                <div className="col-span-3 flex items-center gap-2">
                  {getStatusBadge(currentPage?.status || 'active')}
                  <span className="text-sm text-muted-foreground">
                    You can change the status from the main page
                  </span>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowEditDialog(false)}>
                Cancel
              </Button>
              <Button type="submit">
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
