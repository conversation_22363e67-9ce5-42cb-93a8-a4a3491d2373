import { FastifyRequest, FastifyReply } from 'fastify';
import { conversationService } from '../services/conversation.service';
import { AuthenticatedUser } from '../types/user';

/**
 * Conversation controller for handling conversation-related requests
 */
export class ConversationController {
  /**
   * Get all conversations for the current user
   */
  async getConversations(
    request: FastifyRequest<{
      Querystring: {
        page?: number;
        limit?: number;
        sort?: string;
        status?: 'active' | 'archived' | 'closed';
        isPinned?: boolean;
        search?: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { page, limit, sort, status, isPinned, search } = request.query;

      // Get all conversations for this user (both as user and as creator)
      const result = await conversationService.getConversationsForUser(userId, {
        page,
        limit,
        sort,
        status,
        isPinned,
        search,
      });

      return reply.code(200).send({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to get conversations',
        error: error.message || 'Unknown error',
      });
    }
  }

  /**
   * Get a conversation by ID
   */
  async getConversationById(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { id } = request.params;

      const conversation = await conversationService.getConversationById(id, userId);

      return reply.code(200).send({
        success: true,
        data: conversation,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        message: error.message || 'Failed to get conversation',
        error: error.code || 'INTERNAL_SERVER_ERROR',
      });
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(
    request: FastifyRequest<{
      Body: {
        creatorId: string;
        subject: string;
        initialMessage?: string;
        userLanguage?: string;
        creatorLanguage?: string;
        settings?: {
          notifications?: boolean;
          readReceipts?: boolean;
        };
        metadata?: {
          tags?: string[];
          customFields?: Record<string, any>;
        };
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const {
        creatorId,
        subject,
        initialMessage,
        userLanguage,
        creatorLanguage,
        settings,
        metadata,
      } = request.body;

      // Create conversation
      const conversation = await conversationService.createConversation({
        creatorId,
        userId,
        subject,
        userLanguage,
        creatorLanguage,
        settings,
        metadata,
      });

      // If initial message is provided, send it
      if (initialMessage && initialMessage.trim()) {
        // We'll handle this in the message controller
        // This is just a placeholder for now
      }

      // Ensure the conversation object has all required fields for the response schema
      const responseData = {
        ...conversation,
        metadata: {
          tags: conversation.metadata?.tags || [],
          customFields: conversation.metadata?.customFields || {},
        },
        settings: {
          notifications: conversation.settings?.notifications !== undefined ? conversation.settings.notifications : true,
          readReceipts: conversation.settings?.readReceipts !== undefined ? conversation.settings.readReceipts : true,
        }
      };

      return reply.code(201).send({
        success: true,
        data: responseData,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        error: {
          message: error.message || 'Failed to create conversation',
          code: error.code || 'INTERNAL_SERVER_ERROR',
        }
      });
    }
  }

  /**
   * Update a conversation
   */
  async updateConversation(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
      Body: {
        subject?: string;
        status?: 'active' | 'archived' | 'closed';
        isPinned?: boolean;
        settings?: {
          notifications?: boolean;
          readReceipts?: boolean;
        };
        metadata?: {
          tags?: string[];
          customFields?: Record<string, any>;
        };
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { id } = request.params;
      const { subject, status, isPinned, settings, metadata } = request.body;

      const conversation = await conversationService.updateConversation(id, userId, {
        subject,
        status,
        isPinned,
        settings,
        metadata,
      });

      return reply.code(200).send({
        success: true,
        data: conversation,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        error: {
          message: error.message || 'Failed to update conversation',
          code: error.code || 'INTERNAL_SERVER_ERROR',
        }
      });
    }
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { id } = request.params;

      await conversationService.deleteConversation(id, userId);

      return reply.code(200).send({
        success: true,
        message: 'Conversation deleted successfully',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        error: {
          message: error.message || 'Failed to delete conversation',
          code: error.code || 'INTERNAL_SERVER_ERROR',
        }
      });
    }
  }

  /**
   * Mark a conversation as read
   */
  async markConversationAsRead(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { id } = request.params;

      await conversationService.markConversationAsRead(id, userId);

      return reply.code(200).send({
        success: true,
        message: 'Conversation marked as read',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        error: {
          message: error.message || 'Failed to mark conversation as read',
          code: error.code || 'INTERNAL_SERVER_ERROR',
        }
      });
    }
  }
}

export const conversationController = new ConversationController();
