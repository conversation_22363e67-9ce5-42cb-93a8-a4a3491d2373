import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import { Bell, Sun, Moon, Monitor, Home, User, Shield, LogOut } from 'lucide-react';

const ProfileDropdown: React.FC = () => {
  const { currentUser, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLogout = () => {
    logout();
    navigate('/signin');
  };

  const handleViewProfile = () => {
    navigate('/profile');
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex items-center">
        <button className={`mr-4 ${theme === 'dark' ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}>
          <Bell size={20} />
        </button>
        <button
          className="flex items-center space-x-1"
          onClick={() => setIsOpen(!isOpen)}
        >
          <img
            src={currentUser?.avatar || '/avatar.png'}
            alt="Profile"
            className="w-8 h-8 rounded-full object-cover"
          />
          <span className={`text-sm font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-700'} ml-2`}>
            {currentUser?.username || 'User'}
          </span>
        </button>
      </div>

      {isOpen && (
        <div className={`absolute right-0 mt-2 w-64 ${theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border rounded-md shadow-lg z-50 overflow-hidden`}>
          <div className={`p-4 border-b ${theme === 'dark' ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex justify-center mb-2">
              <img
                src={currentUser?.avatar || '/avatar.png'}
                alt="Profile"
                className="w-16 h-16 rounded-full object-cover"
              />
            </div>
            <div className="text-center">
              <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{currentUser?.username || 'shree'}</h3>
              <p className={`text-sm ${theme === 'dark' ? 'text-white' : 'text-gray-600'}`}>{currentUser?.email || '<EMAIL>'}</p>
            </div>
            <button
              onClick={handleViewProfile}
              className={`mt-3 w-full py-2 px-4 ${theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-100 hover:bg-gray-200'} ${theme === 'dark' ? 'text-white' : 'text-gray-800'} rounded-md text-sm transition-colors`}
            >
              View Profile
            </button>
          </div>

          <div className="p-2 space-y-1">
            <Link
              to="/"
              className={`w-full text-left px-4 py-2 text-sm ${theme === 'dark' ? 'text-white hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'} rounded-md flex items-center`}
            >
              <Home size={16} className={`mr-2 ${theme === 'dark' ? 'text-white' : 'text-gray-700'}`} />
              Home
            </Link>

            <Link
              to="/creator-status"
              className={`w-full text-left px-4 py-2 text-sm ${theme === 'dark' ? 'text-white hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100'} rounded-md flex items-center`}
            >
              <User size={16} className={`mr-2 ${theme === 'dark' ? 'text-white' : 'text-gray-700'}`} />
              Creator Status
            </Link>

            <Link
              to="/admin"
              className={`w-full text-left px-4 py-2 text-sm ${theme === 'dark' ? 'text-white bg-indigo-800 hover:bg-indigo-700' : 'text-white bg-indigo-600 hover:bg-indigo-500'} rounded-md flex items-center`}
            >
              <Shield size={16} className="mr-2 text-white" />
              Admin Panel
            </Link>

            <div className="px-4 py-2">
              <p className={`text-xs font-medium mb-2 ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>THEME</p>
              <div className="space-y-1">
                <button
                  onClick={() => setTheme('light')}
                  className={`w-full text-left px-3 py-1.5 text-sm rounded-md flex items-center ${theme === 'light' ? (theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800') : (theme === 'dark' ? 'text-white hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')}`}
                >
                  <Sun size={16} className={`mr-2 ${theme === 'dark' ? 'text-white' : 'text-gray-700'}`} />
                  Light
                </button>
                <button
                  onClick={() => setTheme('dark')}
                  className={`w-full text-left px-3 py-1.5 text-sm rounded-md flex items-center ${theme === 'dark' ? (theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800') : (theme === 'dark' ? 'text-white hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')}`}
                >
                  <Moon size={16} className={`mr-2 ${theme === 'dark' ? 'text-white' : 'text-gray-700'}`} />
                  Dark
                </button>
                <button
                  onClick={() => setTheme('system')}
                  className={`w-full text-left px-3 py-1.5 text-sm rounded-md flex items-center ${theme === 'system' ? (theme === 'dark' ? 'bg-gray-700 text-white' : 'bg-gray-100 text-gray-800') : (theme === 'dark' ? 'text-white hover:bg-gray-700' : 'text-gray-700 hover:bg-gray-100')}`}
                >
                  <Monitor size={16} className={`mr-2 ${theme === 'dark' ? 'text-white' : 'text-gray-700'}`} />
                  System
                </button>
              </div>
            </div>

            <button
              onClick={handleLogout}
              className={`w-full text-left px-4 py-2 text-sm ${theme === 'dark' ? 'text-red-400 hover:bg-gray-700' : 'text-red-500 hover:bg-gray-100'} rounded-md flex items-center`}
            >
              <LogOut size={16} className={`mr-2 ${theme === 'dark' ? 'text-red-400' : 'text-red-500'}`} />
              Log out
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileDropdown;
