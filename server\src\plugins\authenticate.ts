import { FastifyRequest, FastifyReply } from 'fastify';
import { verify } from 'jsonwebtoken';
import { UserModel } from '../models';
import { createUnauthorizedError } from '../utils/errors/AppError';
import { ErrorCodes } from '../utils/errors/errorCodes';

/**
 * Authentication middleware for Fastify routes
 * Verifies JWT token and attaches user to request
 */
export async function authenticate(request: FastifyRequest, reply: FastifyReply) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development';
    console.log(`Authentication running in ${isDevelopment ? 'development' : 'production'} mode`);

    // Get token from Authorization header
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('No valid Authorization header found:', authHeader);

      if (isDevelopment) {
        console.warn('No valid Authorization header found, using default admin user for development');
        // Use a default admin user for development
        request.user = {
          id: 'eb7d9625-d11c-40a7-b476-4fdd6cb3c4fb',
          username: 'admin',
          email: '<EMAIL>',
          roles: ['admin'],
          permissions: ['*'],
        };
        return;
      }
      throw createUnauthorizedError('No auth credentials found', ErrorCodes.UNAUTHORIZED);
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      if (isDevelopment) {
        console.warn('Empty token, using default admin user for development');
        // Use a default admin user for development
        request.user = {
          id: 'eb7d9625-d11c-40a7-b476-4fdd6cb3c4fb',
          username: 'admin',
          email: '<EMAIL>',
          roles: ['admin'],
          permissions: ['*'],
        };
        return;
      }
      throw createUnauthorizedError('Invalid token format', ErrorCodes.TOKEN_INVALID);
    }

    try {
      // Verify token
      const decoded: any = verify(token, process.env.JWT_SECRET || 'your-secret-key');
      if (!decoded || !decoded.id) {
        throw new Error('Invalid token payload');
      }

      // Get user from database
      const user = await UserModel.findOne({ id: decoded.id, deletedAt: null });
      if (!user) {
        throw new Error('User not found');
      }

      // Check if token is expired
      const currentTimestamp = Math.floor(Date.now() / 1000);
      if (decoded.exp && decoded.exp < currentTimestamp) {
        throw new Error('Token expired');
      }

      // Attach user to request
      request.user = {
        id: user.id,
        username: user.username,
        email: user.email,
        roles: user.roles || [],
        permissions: user.permissions || [],
      };
    } catch (tokenError) {
      console.error('Token verification error:', tokenError);

      if (isDevelopment) {
        console.warn('Token verification failed, using default admin user for development');
        // Use a default admin user for development
        request.user = {
          id: 'eb7d9625-d11c-40a7-b476-4fdd6cb3c4fb',
          username: 'admin',
          email: '<EMAIL>',
          roles: ['admin'],
          permissions: ['*'],
        };
        return;
      }
      throw tokenError;
    }

  } catch (error: any) {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      console.error('Authentication error in development mode:', error);
      console.warn('Using default admin user for development due to authentication error');
      // Use a default admin user for development
      request.user = {
        id: 'eb7d9625-d11c-40a7-b476-4fdd6cb3c4fb',
        username: 'admin',
        email: '<EMAIL>',
        roles: ['admin'],
        permissions: ['*'],
      };
      return;
    }

    // Handle specific JWT errors in production
    if (error.name === 'JsonWebTokenError') {
      throw createUnauthorizedError('Invalid token', ErrorCodes.TOKEN_INVALID);
    } else if (error.name === 'TokenExpiredError') {
      throw createUnauthorizedError('Token expired', ErrorCodes.TOKEN_EXPIRED);
    }

    // Re-throw the error if it's already an AppError
    if (error.statusCode) {
      throw error;
    }

    // Otherwise, throw a generic unauthorized error
    throw createUnauthorizedError('Authentication failed', ErrorCodes.UNAUTHORIZED);
  }
}

/**
 * Type declaration for FastifyRequest to include user property
 */
// Type declaration moved to types/fastify.d.ts
