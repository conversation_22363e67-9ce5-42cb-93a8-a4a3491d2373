import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { videoAPI } from '@/services/api';
import Navbar from '@/components/layout/Navbar';

export default function FixVideosPage() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    stats?: {
      total: number;
      updated: number;
      skipped: number;
      errors: number;
    };
  } | null>(null);

  const handleFixVideos = async () => {
    try {
      setIsLoading(true);
      setResult(null);

      // Call the API to fix all video languages
      const response = await videoAPI.fixAllVideoLanguages();

      console.log('Update response:', response);
      setResult(response);

      // Show success message
      toast({
        title: "Videos Updated",
        description: response.message || "All videos have been updated with proper language data.",
        variant: "default"
      });
    } catch (error) {
      console.error('Error fixing videos:', error);

      // Extract error message
      let errorMessage = "Failed to update videos. Please try again.";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error.response && error.response.data) {
        errorMessage = error.response.data.message || error.response.data.error?.message || errorMessage;
      }

      // Show error message
      toast({
        title: "Error Updating Videos",
        description: errorMessage,
        variant: "destructive"
      });

      setResult({
        success: false,
        message: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <main className="container py-8">
        <div className="max-w-3xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Fix Video Languages</CardTitle>
              <CardDescription>
                This tool will update all existing videos in the database to ensure they have proper language data.
                Use this if you're experiencing issues with language selection in videos.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-yellow-900/20 border border-yellow-800 rounded-md p-4 text-yellow-300">
                <p className="text-sm">
                  <strong>Warning:</strong> This operation will modify all videos in the database.
                  It will add default languages to videos that don't have any and ensure all language URLs are valid.
                  This is useful if you're experiencing issues with language selection in videos.
                </p>
              </div>

              <div className="bg-blue-900/20 border border-blue-800 rounded-md p-4 text-blue-300">
                <p className="text-sm">
                  <strong>Note:</strong> This tool will try multiple methods to fix your videos:
                </p>
                <ol className="list-decimal ml-5 mt-2 text-xs space-y-1">
                  <li>First, it will try a simple fix that adds a default English language to all videos</li>
                  <li>If that fails, it will try a more comprehensive fix that validates and updates all language data</li>
                  <li>As a last resort, it will try to update just the language URLs</li>
                </ol>
              </div>

              <Button
                onClick={handleFixVideos}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating Videos...
                  </>
                ) : (
                  'Fix All Videos'
                )}
              </Button>

              {result && (
                <div className={`mt-4 p-4 rounded-md ${result.success ? 'bg-green-900/20 border border-green-800 text-green-300' : 'bg-red-900/20 border border-red-800 text-red-300'}`}>
                  <h3 className="font-semibold mb-2">{result.success ? 'Success!' : 'Error'}</h3>
                  <p>{result.message}</p>

                  {result.stats && (
                    <div className="mt-2 space-y-1 text-sm">
                      <p>Total videos: {result.stats.total}</p>
                      <p>Updated: {result.stats.updated}</p>
                      <p>Skipped: {result.stats.skipped}</p>
                      <p>Errors: {result.stats.errors}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
