import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Home,
  Clock,
  Folder,
  Database,
  Download,
  HardDrive,
  Server,
  CloudUpload,
  AlertCircle,
  Calendar,
  FileArchive,
  RefreshCw,
  Wrench
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Backup interface
interface Backup {
  id: string;
  date: string;
  size: string;
  type: 'full' | 'database' | 'files';
  status: 'completed' | 'failed';
}

export default function BackupPage() {
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [backupProgress, setBackupProgress] = useState(0);
  const [lastBackupDate, setLastBackupDate] = useState('07-12-2024');
  const [backupType, setBackupType] = useState<'full' | 'database' | 'files'>('full');
  const [backups, setBackups] = useState<Backup[]>([
    {
      id: '1',
      date: '07-12-2024',
      size: '256 MB',
      type: 'full',
      status: 'completed'
    },
    {
      id: '2',
      date: '01-12-2024',
      size: '245 MB',
      type: 'full',
      status: 'completed'
    },
    {
      id: '3',
      date: '25-11-2024',
      size: '120 MB',
      type: 'database',
      status: 'completed'
    },
    {
      id: '4',
      date: '18-11-2024',
      size: '238 MB',
      type: 'full',
      status: 'failed'
    }
  ]);

  const handleCreateBackup = () => {
    setIsCreatingBackup(true);
    setBackupProgress(0);

    // Simulate backup creation with progress updates
    const interval = setInterval(() => {
      setBackupProgress(prev => {
        const newProgress = prev + 5;
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setIsCreatingBackup(false);
            const newDate = new Date().toLocaleDateString('en-GB', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric'
            }).replace(/\//g, '-');

            setLastBackupDate(newDate);

            // Add new backup to the list
            const newBackup: Backup = {
              id: (backups.length + 1).toString(),
              date: newDate,
              size: `${Math.floor(Math.random() * 100) + 200} MB`,
              type: backupType,
              status: 'completed'
            };

            setBackups([newBackup, ...backups]);
          }, 500);
        }
        return newProgress;
      });
    }, 300);
  };

  // Get backup type badge
  const getBackupTypeBadge = (type: string) => {
    switch (type) {
      case 'full':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">Full Backup</Badge>;
      case 'database':
        return <Badge variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200">Database Only</Badge>;
      case 'files':
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-200">Files Only</Badge>;
      default:
        return null;
    }
  };

  // Get backup status badge
  const getBackupStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Completed</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Backup System</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#" className="flex items-center">
                        <Wrench className="h-4 w-4 mr-1" />
                        Tools
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Backup</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Main Content */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Backup Creator Card */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle>Create New Backup</CardTitle>
                  <CardDescription>
                    Backup your database and files for safekeeping
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col items-center py-6">
                    <div className="bg-blue-50 rounded-full p-8 mb-6">
                      <div className="bg-blue-500 rounded-full p-4">
                        <Database className="h-8 w-8 text-white" />
                      </div>
                    </div>

                    {isCreatingBackup ? (
                      <div className="w-full max-w-md space-y-4 mb-8">
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">Creating backup...</span>
                          <span className="text-sm font-medium">{backupProgress}%</span>
                        </div>
                        <Progress value={backupProgress} className="h-2" />
                        <p className="text-sm text-muted-foreground text-center">
                          Please wait while we create your backup. This may take several minutes.
                        </p>
                      </div>
                    ) : (
                      <div className="space-y-6 w-full max-w-md">
                        <Tabs defaultValue={backupType} onValueChange={(value) => setBackupType(value as 'full' | 'database' | 'files')}>
                          <TabsList className="grid w-full grid-cols-3">
                            <TabsTrigger value="full" className="flex items-center gap-1">
                              <HardDrive className="h-4 w-4" />
                              Full
                            </TabsTrigger>
                            <TabsTrigger value="database" className="flex items-center gap-1">
                              <Server className="h-4 w-4" />
                              Database
                            </TabsTrigger>
                            <TabsTrigger value="files" className="flex items-center gap-1">
                              <Folder className="h-4 w-4" />
                              Files
                            </TabsTrigger>
                          </TabsList>
                          <TabsContent value="full" className="pt-4">
                            <Alert>
                              <HardDrive className="h-4 w-4" />
                              <AlertTitle>Full Backup</AlertTitle>
                              <AlertDescription>
                                Creates a complete backup of your database and all uploaded files.
                              </AlertDescription>
                            </Alert>
                          </TabsContent>
                          <TabsContent value="database" className="pt-4">
                            <Alert>
                              <Server className="h-4 w-4" />
                              <AlertTitle>Database Only</AlertTitle>
                              <AlertDescription>
                                Creates a backup of your database only, excluding uploaded files.
                              </AlertDescription>
                            </Alert>
                          </TabsContent>
                          <TabsContent value="files" className="pt-4">
                            <Alert>
                              <Folder className="h-4 w-4" />
                              <AlertTitle>Files Only</AlertTitle>
                              <AlertDescription>
                                Creates a backup of your uploaded files only, excluding the database.
                              </AlertDescription>
                            </Alert>
                          </TabsContent>
                        </Tabs>

                        <div className="flex justify-center">
                          <Button
                            size="lg"
                            onClick={handleCreateBackup}
                            className="w-full"
                          >
                            <CloudUpload className="mr-2 h-4 w-4" />
                            Create New Backup
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="border-t pt-6">
                  <Alert className="w-full bg-blue-50 border-blue-200">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-700">
                      Backups are stored in <code className="bg-blue-100 px-1 py-0.5 rounded">/script_backups/</code> directory. It's recommended to download them via FTP for safekeeping.
                    </AlertDescription>
                  </Alert>
                </CardFooter>
              </Card>

              {/* Backup Info Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Backup Information</CardTitle>
                  <CardDescription>
                    Details about your backup system
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Last Backup:</p>
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-1 text-muted-foreground" />
                        <p className="text-sm">{lastBackupDate}</p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start gap-2">
                    <Folder className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Backups Directory:</p>
                      <p className="text-sm font-mono bg-muted px-2 py-1 rounded">/script_backups/</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start gap-2">
                    <FileArchive className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Total Backups:</p>
                      <p className="text-sm">{backups.length} backups stored</p>
                    </div>
                  </div>

                  <Separator />

                  <div className="flex items-start gap-2">
                    <Database className="h-4 w-4 text-muted-foreground mt-0.5" />
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Backup Types:</p>
                      <div className="flex flex-wrap gap-2 mt-1">
                        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Full</Badge>
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Database</Badge>
                        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Files</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" size="sm" className="w-full">
                    <Download className="mr-2 h-4 w-4" />
                    Download Latest Backup
                  </Button>
                </CardFooter>
              </Card>
            </div>

            {/* Backup History */}
            <Card>
              <CardHeader>
                <CardTitle>Backup History</CardTitle>
                <CardDescription>
                  View your recent backups
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <div className="grid grid-cols-5 p-4 bg-muted/50 text-sm font-medium">
                    <div>Date</div>
                    <div>Type</div>
                    <div>Size</div>
                    <div>Status</div>
                    <div className="text-right">Actions</div>
                  </div>
                  {backups.map((backup) => (
                    <div key={backup.id} className="grid grid-cols-5 p-4 border-t items-center text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {backup.date}
                      </div>
                      <div>
                        {getBackupTypeBadge(backup.type)}
                      </div>
                      <div>{backup.size}</div>
                      <div>
                        {getBackupStatusBadge(backup.status)}
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                          <Download className="h-4 w-4" />
                        </Button>
                        {backup.status === 'failed' && (
                          <Button variant="outline" size="sm" className="h-8 w-8 p-0">
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <div className="text-sm text-muted-foreground">
                  Showing {backups.length} backups
                </div>
                <Button variant="outline" size="sm">
                  View All Backups
                </Button>
              </CardFooter>
            </Card>

            {/* Best Practices */}
            <Card>
              <CardHeader>
                <CardTitle>Backup Best Practices</CardTitle>
                <CardDescription>
                  Tips for effective backup management
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Regular Backups</h3>
                    <p className="text-sm text-muted-foreground">Schedule regular backups to ensure your data is always protected. Daily backups are recommended for active websites.</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Offsite Storage</h3>
                    <p className="text-sm text-muted-foreground">Always store backups in multiple locations, including at least one offsite location to protect against physical disasters.</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Test Restores</h3>
                    <p className="text-sm text-muted-foreground">Regularly test your backup restoration process to ensure your backups are working correctly and can be restored when needed.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
