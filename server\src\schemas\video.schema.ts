import { Type } from '@sinclair/typebox';

/**
 * Base video schema with common properties
 */
const VideoBaseSchema = {
  title: Type.String({
    minLength: 1,
    maxLength: 100,
    description: 'Video title (1-100 characters)',
  }),
  description: Type.String({
    minLength: 1,
    maxLength: 5000,
    description: 'Video description (1-5000 characters)',
  }),
  channelId: Type.String({
    description: 'Channel ID',
  }),
  category: Type.String({
    description: 'Video category',
  }),
  tags: Type.Array(
    Type.String({
      description: 'Video tag',
    }),
    {
      maxItems: 20,
      description: 'Tags for the video (max 20)',
    }
  ),
  visibility: Type.Enum(
    {
      public: 'public',
      unlisted: 'unlisted',
      private: 'private',
      scheduled: 'scheduled',
    },
    {
      default: 'private',
      description: 'Video visibility',
    }
  ),
  scheduledPublishTime: Type.Optional(
    Type.String({
      format: 'date-time',
      description: 'Scheduled publish time (ISO 8601)',
    })
  ),
  contentRating: Type.Enum(
    {
      general: 'general',
      teen: 'teen',
      mature: 'mature',
      explicit: 'explicit',
    },
    {
      default: 'general',
      description: 'Content rating',
    }
  ),
  commentsEnabled: Type.Optional(
    Type.Boolean({
      default: true,
      description: 'Whether comments are enabled',
    })
  ),
  ratingsEnabled: Type.Optional(
    Type.Boolean({
      default: true,
      description: 'Whether ratings are enabled',
    })
  ),
  embeddingEnabled: Type.Optional(
    Type.Boolean({
      default: true,
      description: 'Whether embedding is allowed',
    })
  ),
  chapters: Type.Optional(
    Type.Array(
      Type.Object(
        {
          title: Type.String({
            minLength: 1,
            maxLength: 100,
            description: 'Chapter title',
          }),
          startTime: Type.Number({
            minimum: 0,
            description: 'Start time in seconds',
          }),
          endTime: Type.Number({
            minimum: 0,
            description: 'End time in seconds',
          }),
        },
        {
          description: 'Video chapter',
        }
      ),
      {
        description: 'Video chapters',
      }
    )
  ),
  location: Type.Optional(
    Type.Object(
      {
        latitude: Type.Number({
          minimum: -90,
          maximum: 90,
          description: 'Latitude',
        }),
        longitude: Type.Number({
          minimum: -180,
          maximum: 180,
          description: 'Longitude',
        }),
        name: Type.String({
          description: 'Location name',
        }),
      },
      {
        description: 'Video location',
      }
    )
  ),
  copyright: Type.Optional(
    Type.Object(
      {
        owner: Type.String({
          description: 'Copyright owner',
        }),
        license: Type.String({
          description: 'License type',
        }),
        allowReuse: Type.Boolean({
          description: 'Whether reuse is allowed',
        }),
        allowCommercialUse: Type.Boolean({
          description: 'Whether commercial use is allowed',
        }),
        allowModification: Type.Boolean({
          description: 'Whether modification is allowed',
        }),
        attributionRequired: Type.Boolean({
          description: 'Whether attribution is required',
        }),
      },
      {
        description: 'Copyright information',
      }
    )
  ),
  languages: Type.Optional(
    Type.Array(
      Type.Object(
        {
          code: Type.String({
            description: 'Language code (ISO 639-1)',
          }),
          name: Type.String({
            description: 'Language name (e.g., "English", "Hindi")',
          }),
          flag: Type.Optional(
            Type.String({
              description: 'Flag emoji for the language',
            })
          ),
          url: Type.Optional(
            Type.String({
              description: 'URL or ID for this language version',
            })
          ),
          isDefault: Type.Optional(
            Type.Boolean({
              description: 'Whether this is the default language',
              default: false,
            })
          ),
        },
        {
          description: 'Language information',
        }
      ),
      {
        description: 'Available languages for the video',
      }
    )
  ),
};

/**
 * Schema for uploading a new video
 */
export const UploadVideoSchema = {
  body: Type.Object(
    {
      ...VideoBaseSchema,
      file: Type.Object(
        {
          originalName: Type.String({
            description: 'Original filename',
          }),
          size: Type.Number({
            minimum: 0,
            description: 'File size in bytes',
          }),
          mimeType: Type.String({
            description: 'MIME type',
          }),
        },
        {
          description: 'Video file details',
        }
      ),
      thumbnailUrl: Type.String({
        description: 'URL to video thumbnail',
      }),
      url: Type.Optional(
        Type.String({
          description: 'URL to the video file',
        })
      ),
    },
    {
      description: 'Upload a new video',
    }
  ),
  response: {
    201: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        video: Type.Object(
          {
            id: Type.String(),
            title: Type.String(),
            description: Type.String(),
            thumbnailUrl: Type.String(),
            url: Type.String(),
            duration: Type.Number(),
            userId: Type.String(),
            channelId: Type.String(),
            visibility: Type.String(),
            processingStatus: Type.String(),
            createdAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Uploaded video',
          }
        ),
      },
      {
        description: 'Video uploaded successfully',
      }
    ),
    400: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Bad request',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
  },
};

/**
 * Schema for importing a video from an external source
 */
export const ImportVideoSchema = {
  body: Type.Object(
    {
      ...VideoBaseSchema,
      source: Type.Object(
        {
          type: Type.Enum(
            {
              import: 'import',
              embed: 'embed',
            },
            {
              description: 'Source type',
            }
          ),
          originalUrl: Type.String({
            description: 'Original source URL or Engaxe video ID',
          }),
          platform: Type.String({
            description: 'Source platform (e.g., YouTube, Vimeo)',
          }),
          externalId: Type.Optional(
            Type.String({
              description: 'External ID on the source platform',
            })
          ),
        },
        {
          description: 'Video source information',
        }
      ),
      thumbnailUrl: Type.Optional(
        Type.String({
          description: 'URL to video thumbnail (optional for imports)',
        })
      ),
      duration: Type.Optional(
        Type.Number({
          minimum: 0,
          description: 'Video duration in seconds (optional for imports)',
        })
      ),
      url: Type.Optional(
        Type.String({
          description: 'URL to the video file',
        })
      ),
    },
    {
      description: 'Import a video from external source',
    }
  ),
  response: {
    201: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        video: Type.Object(
          {
            id: Type.String(),
            title: Type.String(),
            description: Type.String(),
            thumbnailUrl: Type.String(),
            url: Type.String(),
            duration: Type.Number(),
            userId: Type.String(),
            channelId: Type.String(),
            visibility: Type.String(),
            processingStatus: Type.String(),
            source: Type.Object(
              {
                type: Type.String(),
                originalUrl: Type.String(),
                platform: Type.String(),
                externalId: Type.Optional(Type.String()),
              },
              {
                description: 'Video source information',
              }
            ),
            createdAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Imported video',
          }
        ),
      },
      {
        description: 'Video imported successfully',
      }
    ),
    400: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Bad request',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    422: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unprocessable entity',
      }
    ),
  },
};

/**
 * Schema for updating an existing video
 */
export const UpdateVideoSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Video ID',
      }),
    },
    {
      description: 'Video parameters',
    }
  ),
  body: Type.Object(
    {
      ...VideoBaseSchema,
      thumbnailUrl: Type.Optional(
        Type.String({
          description: 'URL to video thumbnail',
        })
      ),
    },
    {
      description: 'Update video',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        video: Type.Object(
          {
            id: Type.String(),
            title: Type.String(),
            description: Type.String(),
            thumbnailUrl: Type.String(),
            url: Type.String(),
            duration: Type.Number(),
            userId: Type.String(),
            channelId: Type.String(),
            visibility: Type.String(),
            category: Type.String(),
            tags: Type.Array(Type.String()),
            contentRating: Type.String(),
            commentsEnabled: Type.Boolean(),
            ratingsEnabled: Type.Boolean(),
            embeddingEnabled: Type.Boolean(),
            processingStatus: Type.String(),
            languages: Type.Optional(
              Type.Array(
                Type.Object(
                  {
                    code: Type.String({
                      description: 'Language code (ISO 639-1)',
                    }),
                    name: Type.String({
                      description: 'Language name (e.g., "English", "Hindi")',
                    }),
                    flag: Type.Optional(
                      Type.String({
                        description: 'Flag emoji for the language',
                      })
                    ),
                    url: Type.String({
                      description: 'URL or ID for this language version',
                    }),
                    isDefault: Type.Boolean({
                      description: 'Whether this is the default language',
                      default: false,
                    }),
                  },
                  {
                    description: 'Language information',
                  }
                ),
                {
                  description: 'Available languages for the video',
                }
              )
            ),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Updated video',
          }
        ),
      },
      {
        description: 'Video updated successfully',
      }
    ),
    400: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Bad request',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    403: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Forbidden',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Video not found',
      }
    ),
  },
};

/**
 * Schema for getting a video by ID
 */
export const GetVideoSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Video ID',
      }),
    },
    {
      description: 'Video parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        video: Type.Object(
          {
            id: Type.String(),
            title: Type.String(),
            description: Type.String(),
            thumbnailUrl: Type.String(),
            url: Type.String(),
            duration: Type.Number(),
            userId: Type.String(),
            channelId: Type.String(),
            visibility: Type.String(),
            category: Type.String(),
            tags: Type.Array(Type.String()),
            contentRating: Type.String(),
            commentsEnabled: Type.Boolean(),
            ratingsEnabled: Type.Boolean(),
            embeddingEnabled: Type.Boolean(),
            processingStatus: Type.String(),
            stats: Type.Object(
              {
                views: Type.Number(),
                likes: Type.Number(),
                dislikes: Type.Number(),
                comments: Type.Number(),
                shares: Type.Number(),
                playlistAdds: Type.Number(),
              },
              {
                description: 'Video statistics',
              }
            ),
            source: Type.Optional(
              Type.Object(
                {
                  type: Type.String(),
                  originalUrl: Type.Optional(Type.String()),
                  platform: Type.Optional(Type.String()),
                  externalId: Type.Optional(Type.String()),
                },
                {
                  description: 'Video source information',
                }
              )
            ),
            chapters: Type.Optional(
              Type.Array(
                Type.Object(
                  {
                    title: Type.String(),
                    startTime: Type.Number(),
                    endTime: Type.Number(),
                    thumbnailUrl: Type.Optional(Type.String()),
                  },
                  {
                    description: 'Video chapter',
                  }
                )
              )
            ),
            channel: Type.Object(
              {
                id: Type.String(),
                name: Type.String(),
                displayName: Type.String(),
                avatar: Type.Optional(Type.String()),
                isVerified: Type.Boolean(),
                subscriberCount: Type.Number(),
              },
              {
                description: 'Channel information',
              }
            ),
            user: Type.Object(
              {
                id: Type.String(),
                username: Type.String(),
                avatar: Type.Optional(Type.String()),
              },
              {
                description: 'User information',
              }
            ),
            languages: Type.Optional(
              Type.Array(
                Type.Object(
                  {
                    code: Type.String({
                      description: 'Language code (ISO 639-1)',
                    }),
                    name: Type.String({
                      description: 'Language name (e.g., "English", "Hindi")',
                    }),
                    flag: Type.Optional(
                      Type.String({
                        description: 'Flag emoji for the language',
                      })
                    ),
                    url: Type.String({
                      description: 'URL or ID for this language version',
                    }),
                    isDefault: Type.Boolean({
                      description: 'Whether this is the default language',
                      default: false,
                    }),
                  },
                  {
                    description: 'Language information',
                  }
                ),
                {
                  description: 'Available languages for the video',
                }
              )
            ),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Video details',
          }
        ),
      },
      {
        description: 'Video retrieved successfully',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Video not found',
      }
    ),
  },
};

/**
 * Schema for getting a list of videos
 */
export const GetVideosSchema = {
  querystring: Type.Object(
    {
      page: Type.Optional(
        Type.Union([
          Type.Number({
            minimum: 1,
            default: 1,
            description: 'Page number',
          }),
          Type.String()
        ])
      ),
      limit: Type.Optional(
        Type.Union([
          Type.Number({
            minimum: 1,
            maximum: 100,
            default: 20,
            description: 'Number of items per page',
          }),
          Type.String()
        ])
      ),
      sort: Type.Optional(
        Type.String({
          enum: ['newest', 'popular', 'trending'],
          default: 'newest',
          description: 'Sort order',
        })
      ),
      category: Type.Optional(
        Type.String({
          description: 'Filter by category',
        })
      ),
      channelId: Type.Optional(
        Type.String({
          description: 'Filter by channel ID',
        })
      ),
      userId: Type.Optional(
        Type.String({
          description: 'Filter by user ID',
        })
      ),
      search: Type.Optional(
        Type.String({
          description: 'Search term',
        })
      ),
      tags: Type.Optional(
        Type.Array(
          Type.String({
            description: 'Tag to filter by',
          }),
          {
            description: 'Filter by tags',
          }
        )
      ),
      contentRating: Type.Optional(
        Type.String({
          enum: ['general', 'teen', 'mature', 'explicit'],
          description: 'Filter by content rating',
        })
      ),
    },
    {
      description: 'Query parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        videos: Type.Array(
          Type.Object(
            {
              id: Type.String(),
              title: Type.String(),
              description: Type.String(),
              thumbnailUrl: Type.String(),
              duration: Type.Number(),
              userId: Type.String(),
              channelId: Type.String(),
              visibility: Type.String(),
              category: Type.String(),
              contentRating: Type.String(),
              processingStatus: Type.String(),
              stats: Type.Object(
                {
                  views: Type.Number(),
                  likes: Type.Number(),
                  comments: Type.Number(),
                },
                {
                  description: 'Video statistics summary',
                }
              ),
              channel: Type.Object(
                {
                  id: Type.String(),
                  name: Type.String(),
                  displayName: Type.String(),
                  avatar: Type.Optional(Type.String()),
                  isVerified: Type.Boolean(),
                },
                {
                  description: 'Channel information',
                }
              ),
              languages: Type.Optional(
                Type.Array(
                  Type.Object(
                    {
                      code: Type.String({
                        description: 'Language code (ISO 639-1)',
                      }),
                      name: Type.String({
                        description: 'Language name (e.g., "English", "Hindi")',
                      }),
                      flag: Type.Optional(
                        Type.String({
                          description: 'Flag emoji for the language',
                        })
                      ),
                      url: Type.String({
                        description: 'URL or ID for this language version',
                      }),
                      isDefault: Type.Boolean({
                        description: 'Whether this is the default language',
                        default: false,
                      }),
                    },
                    {
                      description: 'Language information',
                    }
                  ),
                  {
                    description: 'Available languages for the video',
                  }
                )
              ),
              createdAt: Type.String({ format: 'date-time' }),
            },
            {
              description: 'Video summary',
            }
          )
        ),
        pagination: Type.Object(
          {
            total: Type.Number(),
            page: Type.Number(),
            limit: Type.Number(),
            pages: Type.Number(),
          },
          {
            description: 'Pagination information',
          }
        ),
      },
      {
        description: 'Videos retrieved successfully',
      }
    ),
  },
};

/**
 * Schema for getting videos by channel
 */
export const GetChannelVideosSchema = {
  params: Type.Object(
    {
      channelId: Type.String({
        description: 'Channel ID',
      }),
    },
    {
      description: 'Channel parameters',
    }
  ),
  querystring: Type.Object(
    {
      page: Type.Optional(
        Type.Union([
          Type.Number({
            minimum: 1,
            default: 1,
            description: 'Page number',
          }),
          Type.String()
        ])
      ),
      limit: Type.Optional(
        Type.Union([
          Type.Number({
            minimum: 1,
            maximum: 100,
            default: 20,
            description: 'Number of items per page',
          }),
          Type.String()
        ])
      ),
      sort: Type.Optional(
        Type.String({
          enum: ['newest', 'popular', 'oldest'],
          default: 'newest',
          description: 'Sort order',
        })
      ),
    },
    {
      description: 'Query parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        videos: Type.Array(
          Type.Object(
            {
              id: Type.String(),
              title: Type.String(),
              description: Type.String(),
              thumbnailUrl: Type.String(),
              duration: Type.Number(),
              visibility: Type.String(),
              category: Type.String(),
              contentRating: Type.String(),
              processingStatus: Type.String(),
              stats: Type.Object(
                {
                  views: Type.Number(),
                  likes: Type.Number(),
                  comments: Type.Number(),
                },
                {
                  description: 'Video statistics summary',
                }
              ),
              languages: Type.Optional(
                Type.Array(
                  Type.Object(
                    {
                      code: Type.String({
                        description: 'Language code (ISO 639-1)',
                      }),
                      name: Type.String({
                        description: 'Language name (e.g., "English", "Hindi")',
                      }),
                      flag: Type.Optional(
                        Type.String({
                          description: 'Flag emoji for the language',
                        })
                      ),
                      url: Type.String({
                        description: 'URL or ID for this language version',
                      }),
                      isDefault: Type.Boolean({
                        description: 'Whether this is the default language',
                        default: false,
                      }),
                    },
                    {
                      description: 'Language information',
                    }
                  ),
                  {
                    description: 'Available languages for the video',
                  }
                )
              ),
              createdAt: Type.String({ format: 'date-time' }),
            },
            {
              description: 'Video summary',
            }
          )
        ),
        pagination: Type.Object(
          {
            total: Type.Number(),
            page: Type.Number(),
            limit: Type.Number(),
            pages: Type.Number(),
          },
          {
            description: 'Pagination information',
          }
        ),
        channel: Type.Object(
          {
            id: Type.String(),
            name: Type.String(),
            displayName: Type.String(),
            avatar: Type.Optional(Type.String()),
            isVerified: Type.Boolean(),
            subscriberCount: Type.Number(),
          },
          {
            description: 'Channel information',
          }
        ),
      },
      {
        description: 'Channel videos retrieved successfully',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Channel not found',
      }
    ),
  },
};

/**
 * Schema for updating video duration
 */
export const UpdateVideoDurationSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Video ID',
      }),
    },
    {
      description: 'Video parameters',
    }
  ),
  body: Type.Object(
    {
      duration: Type.Number({
        minimum: 0,
        description: 'Video duration in seconds',
      }),
    },
    {
      description: 'Update video duration',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        data: Type.Object(
          {
            videoId: Type.String(),
            duration: Type.Number(),
          },
          {
            description: 'Updated video data',
          }
        ),
      },
      {
        description: 'Video duration updated successfully',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Video not found',
      }
    ),
  },
};

/**
 * Schema for deleting a video
 */
export const DeleteVideoSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Video ID',
      }),
    },
    {
      description: 'Video parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
      },
      {
        description: 'Video deleted successfully',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    403: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Forbidden',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Video not found',
      }
    ),
  },
};

/**
 * Schema for saving an Engaxe video to the database
 */
export const SaveEngaxeVideoSchema = {
  body: Type.Object(
    {
      videoId: Type.String({
        description: 'Engaxe video ID',
      }),
      channelId: Type.String({
        description: 'Channel ID',
      }),
      languages: Type.Optional(
        Type.Array(
          Type.Object(
            {
              code: Type.String({
                description: 'Language code (ISO 639-1)',
              }),
              name: Type.String({
                description: 'Language name (e.g., "English", "Hindi")',
              }),
              flag: Type.Optional(
                Type.String({
                  description: 'Flag emoji for the language',
                })
              ),
              url: Type.Optional(
                Type.String({
                  description: 'URL or ID for this language version',
                })
              ),
              isDefault: Type.Optional(
                Type.Boolean({
                  description: 'Whether this is the default language',
                  default: false,
                })
              ),
            },
            {
              description: 'Language information',
            }
          ),
          {
            description: 'Available languages for the video',
          }
        )
      ),
    },
    {
      description: 'Save Engaxe video to database',
    }
  ),
  response: {
    201: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        video: Type.Object(
          {
            id: Type.String(),
            title: Type.String(),
            description: Type.String(),
            thumbnailUrl: Type.String(),
            url: Type.String(),
            duration: Type.Number(),
            userId: Type.String(),
            channelId: Type.String(),
            visibility: Type.String(),
            processingStatus: Type.String(),
            source: Type.Object(
              {
                type: Type.String(),
                originalUrl: Type.String(),
                platform: Type.String(),
                externalId: Type.String(),
              },
              {
                description: 'Video source information',
              }
            ),
            languages: Type.Array(
              Type.Object(
                {
                  code: Type.String(),
                  name: Type.String(),
                  flag: Type.Optional(Type.String()),
                  url: Type.String(),
                  isDefault: Type.Boolean(),
                },
                {
                  description: 'Language information',
                }
              ),
              {
                description: 'Available languages for the video',
              }
            ),
            createdAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Saved video',
          }
        ),
      },
      {
        description: 'Video saved successfully',
      }
    ),
    400: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Bad request',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Channel not found',
      }
    ),
  },
};