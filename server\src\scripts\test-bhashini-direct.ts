/**
 * Test script for Bhashini API integration using direct API call
 *
 * This script tests the Bhashini API integration by making a direct API call
 * to the Bhashini API endpoint with the correct payload format.
 *
 * Usage:
 * ts-node src/scripts/test-bhashini-direct.ts
 */

import axios from 'axios';

// Bhashini API credentials
const BHASHINI_USER_ID = 'cee60134c6bb4d179efd3fda48ff32fe';
const BHASHINI_ULCA_API_KEY = '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

// Bhashini API endpoints
const BHASHINI_API_URL = 'https://meity-auth.ulcacontrib.org/ulca/apis/v0/model/getModelsPipeline';
// Alternative endpoints to try
const BHASHINI_ALT_API_URL = 'https://api.bhashini.gov.in/translate';
const BHASHINI_COMPUTE_API_URL = 'https://dhruva-api.bhashini.gov.in/services/inference/pipeline';

// Test text
const TEST_TEXT = 'Hello, how are you today?';
const SOURCE_LANGUAGE = 'en';
const TARGET_LANGUAGE = 'hi';

/**
 * Test translation using Bhashini API
 */
async function testTranslation() {
  console.log(`\nTesting Bhashini API translation...`);
  console.log(`Translating "${TEST_TEXT}" from ${SOURCE_LANGUAGE} to ${TARGET_LANGUAGE}`);

  try {
    // Try the first API endpoint
    try {
      console.log('\nTrying primary API endpoint...');
      // Prepare request payload based on the curl example
      const payload = {
        pipelineTasks: [
          {
            taskType: "translation",
            config: {
              language: {
                sourceLanguage: SOURCE_LANGUAGE,
                targetLanguage: TARGET_LANGUAGE
              }
            }
          }
        ],
        inputData: {
          input: [
            {
              source: TEST_TEXT
            }
          ]
        }
      };

      console.log('Request payload:', JSON.stringify(payload, null, 2));

      // Make API request
      const response = await axios.post(
        BHASHINI_API_URL,
        payload,
        {
          headers: {
            'userID': BHASHINI_USER_ID,
            'ulcaApiKey': BHASHINI_ULCA_API_KEY,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Translation successful!');
      console.log('Response:', JSON.stringify(response.data, null, 2));

      // Extract translated text
      if (response.data &&
          response.data.pipelineResponse &&
          response.data.pipelineResponse.output &&
          response.data.pipelineResponse.output.length > 0 &&
          response.data.pipelineResponse.output[0].target) {

        const translatedText = response.data.pipelineResponse.output[0].target;
        console.log('Translated text:', translatedText);
      }

      return response.data;
    } catch (error1: any) {
      console.error('First API endpoint failed:', error1.message);
      if (error1.response) {
        console.error('Error response:', error1.response.data);
      }

      // Try the alternative API endpoint
      console.log('\nTrying alternative API endpoint...');
      try {
        // Prepare request payload for alternative API
        const altPayload = {
          input: TEST_TEXT,
          source_language: SOURCE_LANGUAGE,
          target_language: TARGET_LANGUAGE
        };

        console.log('Alternative request payload:', JSON.stringify(altPayload, null, 2));

        // Make API request to alternative endpoint
        const altResponse = await axios.post(
          BHASHINI_ALT_API_URL,
          altPayload,
          {
            headers: {
              'Authorization': `Bearer ${BHASHINI_USER_ID}:${BHASHINI_ULCA_API_KEY}`,
              'Content-Type': 'application/json'
            }
          }
        );

        console.log('Alternative translation successful!');
        console.log('Response:', JSON.stringify(altResponse.data, null, 2));

        return altResponse.data;
      } catch (error2: any) {
        console.error('Alternative API endpoint failed:', error2.message);
        if (error2.response) {
          console.error('Error response:', error2.response.data);
        }

        // Try the compute API endpoint
        console.log('\nTrying compute API endpoint...');
        try {
          // Prepare request payload for compute API
          const computePayload = {
            pipelineTasks: [
              {
                taskType: "translation",
                config: {
                  language: {
                    sourceLanguage: SOURCE_LANGUAGE,
                    targetLanguage: TARGET_LANGUAGE
                  }
                }
              }
            ],
            inputData: {
              input: [
                {
                  source: TEST_TEXT
                }
              ]
            }
          };

          console.log('Compute request payload:', JSON.stringify(computePayload, null, 2));

          // Make API request to compute endpoint
          const computeResponse = await axios.post(
            BHASHINI_COMPUTE_API_URL,
            computePayload,
            {
              headers: {
                'userID': BHASHINI_USER_ID,
                'ulcaApiKey': BHASHINI_ULCA_API_KEY,
                'Content-Type': 'application/json'
              }
            }
          );

          console.log('Compute translation successful!');
          console.log('Response:', JSON.stringify(computeResponse.data, null, 2));

          return computeResponse.data;
        } catch (error3: any) {
          console.error('Compute API endpoint failed:', error3.message);
          if (error3.response) {
            console.error('Error response:', error3.response.data);
          }
          throw error3;
        }
      }
    }
  } catch (error: any) {
    console.error('All translation attempts failed');
    throw error;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log('=== BHASHINI API DIRECT INTEGRATION TEST ===\n');
  console.log('Using credentials:');
  console.log(`- User ID: ${BHASHINI_USER_ID}`);
  console.log(`- ULCA API Key: ${BHASHINI_ULCA_API_KEY}`);

  try {
    // Test translation
    await testTranslation();

    console.log('\n=== ALL TESTS PASSED ===');
  } catch (error) {
    console.error('\n=== TEST FAILED ===');
    process.exit(1);
  }
}

// Run the tests
runTests();
