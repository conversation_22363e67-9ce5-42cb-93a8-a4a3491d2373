import axios from 'axios';
import { AppError } from '../utils/error';

/**
 * Bhashini API Service
 *
 * This service provides integration with the Bhashini API for translation and language processing
 * Bhashini is an AI-based translation service developed by the Government of India
 * that specializes in Indian languages.
 *
 * Documentation: https://bhashini.gov.in/ulca/apis
 */

// Bhashini API endpoints - Updated with new URL as suggested
const BHASHINI_API_BASE_URL = 'https://bhashini.gov.in';
const BHASHINI_COMPUTE_API_URL = 'https://bhashini.gov.in/api/v1/inference/translation';

// Google Translate API endpoint (free version)
const GOOGLE_TRANSLATE_API_URL = 'https://translate.googleapis.com/translate_a/single';

// Bhashini language code mapping
const ISO_TO_BHASHINI_LANGUAGE_MAP: Record<string, string> = {
  'en': 'en',
  'hi': 'hi',
  'mr': 'mr',
  'gu': 'gu',
  'ta': 'ta',
  'te': 'te',
  'bn': 'bn',
  'kn': 'kn',
  'ml': 'ml',
  'pa': 'pa',
  'or': 'or',
  'as': 'as',
  'ur': 'ur',
  'sa': 'sa',
  'ne': 'ne',
  'si': 'si',
  'doi': 'doi',
  'ks': 'ks',
  'sd': 'sd',
  'brx': 'brx',
  'mni': 'mni',
  'sat': 'sat',
  'lus': 'lus',
  'njz': 'njz',
  'pnr': 'pnr',
  'kha': 'kha',
  'grt': 'grt',
  'kok': 'kok',
  'mai': 'mai',
  'bho': 'bho',
  'raj': 'raj',
  'mup': 'mup'
};

/**
 * Convert ISO language code to Bhashini language code
 * @param isoCode ISO language code
 * @returns Bhashini language code
 */
function toBhashiniLanguageCode(isoCode: string): string {
  return ISO_TO_BHASHINI_LANGUAGE_MAP[isoCode.toLowerCase()] || isoCode.toLowerCase();
}

/**
 * Get authentication token from Bhashini API
 * @param userId Bhashini User ID
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Authentication token
 */
async function getAuthToken(userId: string, ulcaApiKey?: string): Promise<string> {
  try {
    console.log(`Getting Bhashini auth token for userId: ${userId.substring(0, 5)}...`);

    // For the updated Bhashini API, we don't need to get a separate auth token
    // Instead, we'll use the userId directly in the API calls
    // This is a placeholder function to maintain compatibility with existing code
    console.log('Using direct authentication with Bhashini API');
    return 'direct-auth';
  } catch (error: any) {
    console.error('Error getting Bhashini auth token:', error?.message || error);
    throw new AppError('Failed to authenticate with Bhashini API', 500);
  }
}

/**
 * Execute model computation using Bhashini API
 * @param modelId Model ID to use for computation
 * @param taskType Type of task (translation, language-detection, etc.)
 * @param inputData Input data for the model
 * @param pipelineParams Additional pipeline parameters
 * @param userId Bhashini User ID
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Model computation result
 */
async function executeModelComputation(
  modelId: string,
  taskType: string,
  inputData: Array<{ source: string }>,
  pipelineParams: any = {},
  userId: string,
  ulcaApiKey?: string
): Promise<any> {
  try {
    // Use the provided credentials
    const bhashiniUserId = userId || 'cee60134c6bb4d179efd3fda48ff32fe';
    const bhashiniUlcaApiKey = ulcaApiKey || '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

    // Prepare request payload for the new Bhashini API endpoint
    const payload = {
      userId: bhashiniUserId,
      ulcaApiKey: bhashiniUlcaApiKey,
      sourceLanguage: pipelineParams.language?.sourceLanguage || 'en',
      targetLanguage: pipelineParams.language?.targetLanguage || 'hi',
      domain: "general",
      text: inputData[0].source
    };

    console.log(`Executing Bhashini model computation for task: ${taskType}`);
    console.log('Payload:', JSON.stringify(payload, null, 2));

    // Make API request with headers for the new Bhashini API endpoint
    const response = await axios.post(
      BHASHINI_COMPUTE_API_URL,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    // Return the response data in a format compatible with the rest of the code
    if (response.data) {
      console.log('Bhashini model computation successful');
      console.log('Response:', JSON.stringify(response.data, null, 2));

      // Format the response to match the expected structure
      return {
        output: [
          {
            target: response.data.translatedText || response.data.text || inputData[0].source,
            source_language: pipelineParams.language?.sourceLanguage || 'en',
            target_language: pipelineParams.language?.targetLanguage || 'hi'
          }
        ]
      };
    }

    throw new Error('Invalid response from Bhashini API');
  } catch (error: any) {
    console.error('Error executing Bhashini model computation:', error?.message || error);
    console.error('Error details:', error.response?.data || error.message);
    throw new AppError('Failed to execute Bhashini model computation', 500);
  }
}

/**
 * Translate text using Bhashini API
 * @param text Text to translate
 * @param sourceLanguage Source language code (ISO)
 * @param targetLanguage Target language code (ISO)
 * @param userId Bhashini User ID
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Translated text
 */
async function translateText(
  text: string,
  sourceLanguage: string,
  targetLanguage: string,
  userId: string,
  ulcaApiKey?: string
): Promise<string> {
  try {
    // If source and target languages are the same, return the original text
    if (sourceLanguage === targetLanguage) {
      return text;
    }

    // Convert ISO language codes to Bhashini language codes
    const bhashiniSourceLang = toBhashiniLanguageCode(sourceLanguage);
    const bhashiniTargetLang = toBhashiniLanguageCode(targetLanguage);

    // Use the executeModelComputation function
    const response = await executeModelComputation(
      'ai4bharat/indictrans-v2-all-gpu',
      'translation',
      [{ source: text }],
      {
        language: {
          sourceLanguage: bhashiniSourceLang,
          targetLanguage: bhashiniTargetLang,
        },
      },
      userId,
      ulcaApiKey
    );

    // Extract and return the translated text
    if (response &&
        response.output &&
        response.output.length > 0 &&
        response.output[0].target) {
      return response.output[0].target;
    }

    throw new Error('Invalid response from Bhashini API');
  } catch (error: any) {
    console.error('Error translating text with Bhashini API:', error?.message || error);
    return `[Translation Error] ${text}`;
  }
}

/**
 * Process chat messages using Bhashini API
 * @param messages Array of chat messages
 * @param apiKey API key (may contain userId:ulcaApiKey format)
 * @param userId Bhashini User ID
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Processed response
 */
async function processChatMessages(
  messages: Array<{ role: 'user' | 'assistant'; content: string }>,
  apiKey: string,
  userId: string,
  ulcaApiKey?: string
): Promise<string> {
  try {
    // Extract the last user message
    const lastUserMessage = messages.filter(m => m.role === 'user').pop();

    if (!lastUserMessage) {
      return "I don't see a question to respond to. How can I help you?";
    }

    // For now, we'll just translate the message to Hindi and back to English
    // as a demonstration of using the Bhashini API
    console.log(`Processing message: "${lastUserMessage.content}"`);

    // Translate to Hindi
    const hindiText = await translateText(
      lastUserMessage.content,
      'en',
      'hi',
      userId,
      ulcaApiKey
    );

    console.log(`Translated to Hindi: "${hindiText}"`);

    // Translate back to English
    const translatedBackText = await translateText(
      hindiText,
      'hi',
      'en',
      userId,
      ulcaApiKey
    );

    console.log(`Translated back to English: "${translatedBackText}"`);

    // Generate a response based on the translated text
    return `I received your message and processed it through our translation system.
    Your message was translated to Hindi as: "${hindiText}"
    And back to English as: "${translatedBackText}"

    How can I help you further with your question?`;
  } catch (error: any) {
    console.error('Error processing chat messages with Bhashini API:', error?.message || error);
    return "I'm sorry, I encountered an error while processing your message. Could you please try again?";
  }
}

/**
 * Execute pipeline tasks using Bhashini API
 * @param pipelineTasks Array of pipeline tasks
 * @param inputText Input text
 * @param userId Bhashini User ID
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Pipeline execution result
 */
async function executePipelineTasks(
  pipelineTasks: Array<{
    taskType: string;
    config: {
      language: {
        sourceLanguage?: string;
        targetLanguage?: string;
      };
    };
  }>,
  inputText: string,
  userId: string,
  ulcaApiKey?: string
): Promise<any> {
  try {
    // Use the provided credentials
    const bhashiniUserId = userId || 'cee60134c6bb4d179efd3fda48ff32fe';
    const bhashiniUlcaApiKey = ulcaApiKey || '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

    // Prepare request payload for the new Bhashini API endpoint
    const payload = {
      userId: bhashiniUserId,
      ulcaApiKey: bhashiniUlcaApiKey,
      sourceLanguage: pipelineTasks[0]?.config?.language?.sourceLanguage || 'en',
      targetLanguage: pipelineTasks[0]?.config?.language?.targetLanguage || 'hi',
      domain: "general",
      text: inputText
    };

    console.log('Executing Bhashini pipeline tasks');
    console.log('Payload:', JSON.stringify(payload, null, 2));

    // Make API request with headers for the new Bhashini API endpoint
    const response = await axios.post(
      BHASHINI_COMPUTE_API_URL,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    // Return the response data
    if (response.data) {
      console.log('Bhashini pipeline execution successful');

      // Format the response to match the expected structure
      return {
        output: [
          {
            target: response.data.translatedText || response.data.text || inputText,
            source_language: pipelineTasks[0]?.config?.language?.sourceLanguage || 'en',
            target_language: pipelineTasks[0]?.config?.language?.targetLanguage || 'hi'
          }
        ]
      };
    }

    throw new Error('Invalid response from Bhashini API');
  } catch (error: any) {
    console.error('Error executing Bhashini pipeline tasks:', error?.message || error);
    console.error('Error details:', error.response?.data || error.message);
    throw new AppError('Failed to execute Bhashini pipeline tasks', 500);
  }
}

/**
 * Translate text using Bhashini API with pipeline
 * @param text Text to translate
 * @param sourceLanguage Source language code (ISO)
 * @param targetLanguage Target language code (ISO)
 * @param userId Bhashini User ID
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Translated text
 */
async function translateTextWithPipeline(
  text: string,
  sourceLanguage: string,
  targetLanguage: string,
  userId: string,
  ulcaApiKey?: string
): Promise<string> {
  try {
    // If source and target languages are the same, return the original text
    if (sourceLanguage === targetLanguage) {
      return text;
    }

    // Convert ISO language codes to Bhashini language codes
    const bhashiniSourceLang = toBhashiniLanguageCode(sourceLanguage);
    const bhashiniTargetLang = toBhashiniLanguageCode(targetLanguage);

    // Define pipeline tasks
    const pipelineTasks = [
      {
        taskType: "translation",
        config: {
          language: {
            sourceLanguage: bhashiniSourceLang,
            targetLanguage: bhashiniTargetLang
          }
        }
      }
    ];

    // Execute pipeline tasks
    const response = await executePipelineTasks(
      pipelineTasks,
      text,
      userId,
      ulcaApiKey
    );

    // Extract and return the translated text
    if (response &&
        response.output &&
        response.output.length > 0 &&
        response.output[0].target) {
      return response.output[0].target;
    }

    throw new Error('Invalid response from Bhashini API');
  } catch (error: any) {
    console.error('Error translating text with Bhashini API pipeline:', error?.message || error);
    return `[Translation Error] ${text}`;
  }
}

/**
 * Translate text using Google Translate API
 * @param text Text to translate
 * @param sourceLanguage Source language code (ISO)
 * @param targetLanguage Target language code (ISO)
 * @returns Translated text
 */
async function translateTextWithGoogle(
  text: string,
  sourceLanguage: string,
  targetLanguage: string
): Promise<string> {
  try {
    // If source and target languages are the same, return the original text
    if (sourceLanguage === targetLanguage) {
      return text;
    }

    console.log(`Translating text with Google Translate API: "${text}" from ${sourceLanguage} to ${targetLanguage}`);

    // Make API request
    const response = await axios.get(
      GOOGLE_TRANSLATE_API_URL,
      {
        params: {
          client: 'gtx',
          sl: sourceLanguage,
          tl: targetLanguage,
          dt: 't',
          q: text
        },
        headers: {
          'Accept': 'application/json'
        }
      }
    );

    // Extract and return the translated text
    if (response.data && response.data[0] && response.data[0][0]) {
      const translatedText = response.data[0][0][0];
      console.log(`Google translation result: "${translatedText}"`);
      return translatedText;
    }

    throw new Error('Invalid response from Google Translate API');
  } catch (error: any) {
    console.error('Error translating text with Google Translate API:', error?.message || error);
    return `[Translation Error] ${text}`;
  }
}

/**
 * Translate text using the best available method
 * @param text Text to translate
 * @param sourceLanguage Source language code (ISO)
 * @param targetLanguage Target language code (ISO)
 * @param userId Bhashini User ID
 * @param ulcaApiKey Bhashini ULCA API Key (optional)
 * @returns Translated text
 */
async function translateTextBest(
  text: string,
  sourceLanguage: string,
  targetLanguage: string,
  userId?: string,
  ulcaApiKey?: string
): Promise<string> {
  try {
    // If source and target languages are the same, return the original text
    if (sourceLanguage === targetLanguage) {
      return text;
    }

    console.log(`Translating text with best available method: "${text}" from ${sourceLanguage} to ${targetLanguage}`);

    // Try Bhashini API first
    try {
      const bhashiniResult = await translateTextWithPipeline(
        text,
        sourceLanguage,
        targetLanguage,
        userId || 'cee60134c6bb4d179efd3fda48ff32fe',
        ulcaApiKey || '13a647c84b-2747-4f0c-afcd-2ac8235f5318'
      );

      // If the result doesn't contain an error message, return it
      if (!bhashiniResult.includes('[Translation Error]')) {
        return bhashiniResult;
      }

      console.log('Bhashini API translation failed, falling back to Google Translate');
    } catch (error) {
      console.error('Error with Bhashini API translation:', error);
      console.log('Falling back to Google Translate');
    }

    // Fall back to Google Translate
    return await translateTextWithGoogle(text, sourceLanguage, targetLanguage);
  } catch (error: any) {
    console.error('Error translating text with best method:', error?.message || error);
    return `[Translation Error] ${text}`;
  }
}

// Export the service
export const bhashiniApiService = {
  getAuthToken,
  executeModelComputation,
  translateText,
  translateTextWithPipeline,
  translateTextWithGoogle,
  translateTextBest,
  executePipelineTasks,
  processChatMessages,
  toBhashiniLanguageCode
};
