{"metadata": {"totalVideos": 9, "lastUpdated": "2025-05-31T11:25:35.955Z", "version": "1.0.0", "syncedAt": "2025-05-31T11:22:49.721Z", "description": "All videos from LawEngaxe platform automatically synced from database"}, "videos": [{"id": "ef74eef39689464d4afa815f0049a1fc", "title": "Basic Structure of C Program in Hindi: C Tutorial In Hindi #4", "description": "In this video, I have explained about the basic structure of C program and how C program works in Hindi. ►This C Lecture is a part of this C Programming Course: <a href=\"https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9aiXlHcLx-mDH1Qul38wD3aR\">https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9aiXlHcLx-mDH1Qul38wD3aR</a> ►Source Code + Notes: <a href=\"https://codewithharry.com/videos/c-language-tutorials-in-hindi-4\">https://codewithharry.com/videos/c-language-tutorials-in-hindi-4</a> ►Click here to subscribe - <a href=\"https://www.youtube.com/channel/UCeVMnSShP_Iviwkknt83cww\">https://www.youtube.com/channel/UCeVMnSShP_Iviwkknt83cww</a> Best Hindi Videos For Learning Programming: ►Learn Python In One Video - <a href=\"https://www.youtube.com/watch%3Fv%3DqHJjMvHLJdg\">https://www.youtube.com/watch%3Fv%3DqHJjMvHLJdg</a> ►Learn JavaScript in One Video - <a href=\"https://www.youtube.com/watch%3Fv%3DonbBV0uFVpo\">https://www.youtube.com/watch%3Fv%3DonbBV0uFVpo</a> ►Learn PHP In One Video - <a href=\"https://www.youtube.com/watch%3Fv%3DxW7ro3lwaCI\">https://www.youtube.com/watch%3Fv%3DxW7ro3lwaCI</a> ►Machine Learning Using Python - <a href=\"https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9ai6fAMHp-acBmJONT7Y4BSG\">https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9ai6fAMHp-acBmJONT7Y4BSG</a> ►Creating &amp; Hosting A Website (Tech Blog) Using Python - <a href=\"https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9agAiWp6Y41ueUKx1VcTRxmf\">https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9agAiWp6Y41ueUKx1VcTRxmf</a> ►Advanced Python Tutorials - <a href=\"https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9aiJWQ7VhY712fuimEpQZYp4\">https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9aiJWQ7VhY712fuimEpQZYp4</a> ►Object Oriented Programming In Python - <a href=\"https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9ahfRrhFcoB-4lpp9YaBmdCP\">https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9ahfRrhFcoB-4lpp9YaBmdCP</a> ►Python Data Science and Big Data Tutorials - <a href=\"https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9agK8pojo23OHiNz3Jm6VQCH\">https://www.youtube.com/playlist%3Flist%3DPLu0W_9lII9agK8pojo23OHiNz3Jm6VQCH</a> Follow Me On Social Media ►Website (created using Flask) - <a href=\"https://www.codewithharry.com\">https://www.codewithharry.com</a> ►Facebook - <a href=\"https://www.facebook.com/CodeWithHarry\">https://www.facebook.com/CodeWithHarry</a> ►Instagram - <a href=\"https://www.instagram.com/CodeWithHarry/\">https://www.instagram.com/CodeWithHarry/</a> ►Personal Facebook A/c - <a href=\"https://www.facebook.com/geekyharis\">https://www.facebook.com/geekyharis</a> Twitter - <a href=\"https://twitter.com/Haris_Is_Here\">https://twitter.com/Haris_Is_Here</a>", "url": "m7PtcY", "thumbnailUrl": "https://i.ytimg.com/vi/5SIBB589fAg/maxresdefault.jpg", "duration": 0, "userId": "57e0d78a-fb6d-4775-8473-bbb3b17ee52e", "channelId": "2b60d5ca-9593-43d5-9e7e-414bdbc3dcce", "visibility": "public", "tags": ["basic structure of c program in hindi", "Basic Structure of C Program (HINDI)", "C PROGRAMMING:Basic Structure", "structure c program", "c program structure", "c tutorials hindi", "learn c in hindi", "c programming", "C programming tutorial", "learn c programming", "c language", "c language tutorials", "Introduction to c", "lecture on introduction to c", "c programming in hindi", "c tutorials beginners", "learn c", "c <PERSON>ho", "c <PERSON><PERSON>", "c programming language", "learn c fast", "c tutorials fast", "c program"], "category": "Education", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 36, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "Basic Structure of C Program in Hindi: C Tutorial In Hindi #4.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "isDefault": true, "url": "m7PtcY", "_id": "6826cc26301da66bde453a30"}, {"code": "hi", "name": "Hindi", "flag": "🇮🇳", "isDefault": false, "url": "m7PtcY", "_id": "6826cc26301da66bde453a31"}], "source": {"type": "embed", "originalUrl": "m7PtcY", "platform": "engaxe", "externalId": "m7PtcY"}, "createdAt": "2025-05-16T05:24:54.148Z", "updatedAt": "2025-05-16T05:24:54.148Z", "searchKeywords": ["basic", "structure", "program", "hindi:", "tutorial", "hindi", "this", "video,", "have", "explained", "about", "the", "and", "how", "works", "hindi.", "►this", "lecture", "part", "programming", "course:", "href=\"https://www.youtube.com/playlist%3flist%3dplu0w_9lii9aixlhclx-mdh1qul38wd3ar\">https://www.youtube.com/playlist%3flist%3dplu0w_9lii9aixlhclx-mdh1qul38wd3ar</a>", "►source", "code", "notes:", "href=\"https://codewithharry.com/videos/c-language-tutorials-in-hindi-4\">https://codewithharry.com/videos/c-language-tutorials-in-hindi-4</a>", "►click", "here", "subscribe", "href=\"https://www.youtube.com/channel/ucevmnsshp_iviwkknt83cww\">https://www.youtube.com/channel/ucevmnsshp_iviwkknt83cww</a>", "best", "videos", "for", "learning", "programming:", "►learn", "python", "one", "video", "href=\"https://www.youtube.com/watch%3fv%3dqhjjmvhljdg\">https://www.youtube.com/watch%3fv%3dqhjjmvhljdg</a>", "javascript", "href=\"https://www.youtube.com/watch%3fv%3donbbv0ufvpo\">https://www.youtube.com/watch%3fv%3donbbv0ufvpo</a>", "php", "href=\"https://www.youtube.com/watch%3fv%3dxw7ro3lwaci\">https://www.youtube.com/watch%3fv%3dxw7ro3lwaci</a>", "►machine", "using", "href=\"https://www.youtube.com/playlist%3flist%3dplu0w_9lii9ai6famhp-acbmjont7y4bsg\">https://www.youtube.com/playlist%3flist%3dplu0w_9lii9ai6famhp-acbmjont7y4bsg</a>", "►creating", "&amp;", "hosting", "website", "(tech", "blog)", "href=\"https://www.youtube.com/playlist%3flist%3dplu0w_9lii9agaiwp6y41ueukx1vctrxmf\">https://www.youtube.com/playlist%3flist%3dplu0w_9lii9agaiwp6y41ueukx1vctrxmf</a>", "►advanced", "tutorials", "href=\"https://www.youtube.com/playlist%3flist%3dplu0w_9lii9aijwq7vhy712fuimepqzyp4\">https://www.youtube.com/playlist%3flist%3dplu0w_9lii9aijwq7vhy712fuimepqzyp4</a>", "►object", "oriented", "href=\"https://www.youtube.com/playlist%3flist%3dplu0w_9lii9ahfrrhfcob-4lpp9yabmdcp\">https://www.youtube.com/playlist%3flist%3dplu0w_9lii9ahfrrhfcob-4lpp9yabmdcp</a>", "►python", "data", "science", "big", "href=\"https://www.youtube.com/playlist%3flist%3dplu0w_9lii9agk8pojo23ohinz3jm6vqch\">https://www.youtube.com/playlist%3flist%3dplu0w_9lii9agk8pojo23ohinz3jm6vqch</a>", "follow", "social", "media", "►website", "(created", "flask)", "href=\"https://www.codewithharry.com\">https://www.codewithharry.com</a>", "►facebook", "href=\"https://www.facebook.com/codewithharry\">https://www.facebook.com/codewithharry</a>", "►instagram", "href=\"https://www.instagram.com/codewithharry/\">https://www.instagram.com/codewithharry/</a>", "►personal", "facebook", "a/c", "href=\"https://www.facebook.com/geekyharis\">https://www.facebook.com/geekyharis</a>", "twitter", "href=\"https://twitter.com/haris_is_here\">https://twitter.com/haris_is_here</a>", "basic structure of c program in hindi", "basic structure of c program (hindi)", "c programming:basic structure", "structure c program", "c program structure", "c tutorials hindi", "learn c in hindi", "c programming", "c programming tutorial", "learn c programming", "c language", "c language tutorials", "introduction to c", "lecture on introduction to c", "c programming in hindi", "c tutorials beginners", "learn c", "c <PERSON>ho", "c <PERSON><PERSON>", "c programming language", "learn c fast", "c tutorials fast", "c program", "education"], "watchUrl": "http://localhost:5173/watch?id=ef74eef39689464d4afa815f0049a1fc"}, {"id": "4de5b8bab5d5905086dff8c2d44435c5", "title": "Basic Concepts in Automata Theory || Mathematical Notations || TOC || FLAT || Theory of Computation", "description": "1. Compiler Design Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfC9pGMWuM6UWE3V4YZ9TZzM ------------------------------------------------------------------------------------ 2. Computer Organization and Architecture Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfDXDRzSLv1FfZ-SSA38SiC0 --------------------------------------------------------------------------------------- 3. Operating Systems Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfDrdQuJTHIPmKMpa7eYVaPm ---------------------------------------------------------------------------------------- 4. C Programming Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfBi_vnP7eo-QayXpzuMuOP6 -------------------------------------------------------------------------------------------------------------------------- 5. Java Programming Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfDlQklXu3Hrtru-bm2dJ9Df --------------------------------------------------------------------------------------------------------------------------------- 6. Data Structures Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfAhaLFnq4fQ5ASOqKd08-Us --------------------------------------------------------------------------------------------------------------------- 7. Web Technologies Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfCwJmQkWF9sBUFLAdonjOMB ---------------------------------------------------------------------------------------------------------------- 8. C++ Programming Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfCmm6ZT-R_33bxLY1fvcm_o -------------------------------------------------------------------------------------------------------------------------- 9. DAA ( Design and Analysis of Algorithms) Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfAG09GbFgMOLACfvbMplHsW ---------------------------------------------------------------------------------------------------------------------------- 10. Python Programming Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfAKSXjEzeQhXdqTflN7qRlo --------------------------------------------------------------------------------------------------------------------------- 11. DMS ( Discrete Mathematical Structures ) Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfBB-4hXp4XI84HOCWkaBD63 ---------------------------------------------------------------------------------------------------------------------------------- 12. C#.net Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfA8sKELgBkkUZxzGK1o5ll0 -------------------------------------------------------------------------------------------------------------------------------------- 13. OOP through Python Playlist: https://www.youtube.com/playlist?list=PLXj4XH7LcRfANIIqVzBRTh0XDgZxu-QO2 ----------------------------------------------------------------------------------------------------------------", "url": "AmbwDK", "thumbnailUrl": "https://i.ytimg.com/vi/O3NU5dLDU2Q/maxresdefault.jpg", "duration": 0, "userId": "57e0d78a-fb6d-4775-8473-bbb3b17ee52e", "channelId": "2b60d5ca-9593-43d5-9e7e-414bdbc3dcce", "visibility": "public", "tags": ["automata theory", "theory of computation", "automata", "abstract machine", "mathematical model", "applications of automata theory", "states", "finite automata", "nfa", "dfa", "complexity", "computations", "formal langugaes", "grammar", "string", "language", "alphabet", "symbols", "transition table", "transition diagram", "kleene star", "kleene closure", "sets", "subsets", "formal language", "informal language", "automaton", "compiler", "compiler design", "basic notations", "representations", "notations in automata theory", "<PERSON><PERSON><PERSON>"], "category": "Film & Animation", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 8, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "Basic Concepts in Automata Theory || Mathematical Notations || TOC || FLAT || Theory of Computation.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "isDefault": true, "url": "AmbwDK", "_id": "6826ccd3301da66bde453b4f"}, {"code": "hi", "name": "Hindi", "flag": "🇮🇳", "isDefault": false, "url": "AmbwDK", "_id": "6826ccd3301da66bde453b50"}], "source": {"type": "embed", "originalUrl": "AmbwDK", "platform": "engaxe", "externalId": "AmbwDK"}, "createdAt": "2025-05-16T05:27:47.772Z", "updatedAt": "2025-05-16T05:27:47.772Z", "searchKeywords": ["basic", "concepts", "automata", "theory", "mathematical", "notations", "toc", "flat", "computation", "compiler", "design", "playlist:", "https://www.youtube.com/playlist?list=plxj4xh7lcrfc9pgmwum6uwe3v4yz9tzzm", "------------------------------------------------------------------------------------", "computer", "organization", "and", "architecture", "https://www.youtube.com/playlist?list=plxj4xh7lcrfdxdrzslv1ffz-ssa38sic0", "---------------------------------------------------------------------------------------", "operating", "systems", "https://www.youtube.com/playlist?list=plxj4xh7lcrfdrdqujthipmkmpa7eyvapm", "----------------------------------------------------------------------------------------", "programming", "https://www.youtube.com/playlist?list=plxj4xh7lcrfbi_vnp7eo-qayxpzumuop6", "--------------------------------------------------------------------------------------------------------------------------", "java", "https://www.youtube.com/playlist?list=plxj4xh7lcrfdlqklxu3hrtru-bm2dj9df", "---------------------------------------------------------------------------------------------------------------------------------", "data", "structures", "https://www.youtube.com/playlist?list=plxj4xh7lcrfahalfnq4fq5asoqkd08-us", "---------------------------------------------------------------------------------------------------------------------", "web", "technologies", "https://www.youtube.com/playlist?list=plxj4xh7lcrfcwjmqkwf9sbufladonjomb", "----------------------------------------------------------------------------------------------------------------", "c++", "https://www.youtube.com/playlist?list=plxj4xh7lcrfcmm6zt-r_33bxly1fvcm_o", "daa", "analysis", "algorithms)", "https://www.youtube.com/playlist?list=plxj4xh7lcrfag09gbfgmolacfvbmplhsw", "----------------------------------------------------------------------------------------------------------------------------", "10.", "python", "https://www.youtube.com/playlist?list=plxj4xh7lcrfaksxjezeqhxdqtfln7qrlo", "---------------------------------------------------------------------------------------------------------------------------", "11.", "dms", "discrete", "https://www.youtube.com/playlist?list=plxj4xh7lcrfbb-4hxp4xi84hocwkabd63", "----------------------------------------------------------------------------------------------------------------------------------", "12.", "c#.net", "https://www.youtube.com/playlist?list=plxj4xh7lcrfa8skelgbkkuzxzgk1o5ll0", "--------------------------------------------------------------------------------------------------------------------------------------", "13.", "oop", "through", "https://www.youtube.com/playlist?list=plxj4xh7lcrfaniiqvzbrth0xdgzxu-qo2", "automata theory", "theory of computation", "abstract machine", "mathematical model", "applications of automata theory", "states", "finite automata", "nfa", "dfa", "complexity", "computations", "formal langugaes", "grammar", "string", "language", "alphabet", "symbols", "transition table", "transition diagram", "kleene star", "kleene closure", "sets", "subsets", "formal language", "informal language", "automaton", "compiler design", "basic notations", "representations", "notations in automata theory", "<PERSON><PERSON><PERSON>", "film & animation"], "watchUrl": "http://localhost:5173/watch?id=4de5b8bab5d5905086dff8c2d44435c5"}, {"id": "fcd5de9c4bee35169e865de6c6fb286b", "title": "बंद करो Basic English Words 😮बोलना | Learn 30 New Vocabulary Words | English Connection by <PERSON><PERSON><PERSON>", "description": "⚡Boost your vocabulary and start speaking confidently with new and advanced English words! 🚀 This video by <PERSON><PERSON><PERSON>&#039;am from English Connection will help you stop using overused words and learn fresh vocabulary to sound more fluent and professional. 💬 ＬＩＫＥ | ＣＯＭＭＥＮＴ | ＳＨＡＲＥ | ＳＵＢＳＣＲＩＢＥ 😊 👉 Subscribe &amp; hit the bell icon: https://cutt.ly/Subscribe_For_Free 👉 Learn English Everyday: 🔗LINK to buy the Book: https://cutt.ly/Best_Book_To_Learn_English Join this channel to get access to the perks: https://www.youtube.com/channel/UCsUBoYj2kqgj72g6kgzGPMg/join ⚡ Free Courses: 👉 Spoken English Course: https://cutt.ly/SpkenEnglish 👉 Free Grammar Course: https://cutt.ly/w4whIj6 Watch my some trendig videos👇 👉 All Tenses in English Grammar : https://cutt.ly/Tenses 👉 जीरो से English पढ़ना-लिखना सीखें: https://cutt.ly/pTUV3Vy 👉 कितनी Grammar आनी चाहिए, English Speaking के लिए: https://cutt.ly/S4whHee Follow me on other social media: 👉 Facebook @: https://www.facebook.com/EnglishConnectionByKanchan 👉 Instagram @: https://www.instagram.com/englishconnectionbykanchan/ ------------------------------------------------- 👉 Download the English Connection App: https://play.google.com/store/apps/details?id=com.appx.english_connection&amp;pli=1 Call us at 8595926123 (For course-related Enquiry) ------------------------------------------------- Pana hai perfection, to follow Kanchan! For course-related Enquiry, call us at 8595926123", "url": "vnvyNi", "thumbnailUrl": "https://i.ytimg.com/vi/YlhXDXkfMVs/maxresdefault.jpg", "duration": 0, "userId": "57e0d78a-fb6d-4775-8473-bbb3b17ee52e", "channelId": "2b60d5ca-9593-43d5-9e7e-414bdbc3dcce", "visibility": "public", "tags": ["english", "english connection", "kanchan english", "vocabulary", "english words", "how to boost vocabulary", "expand your vocabulary", "basic english words", "kanchan keshari", "basic english words for beginners", "learn english vocabulary", "new vocabulary", "kanchan english vocabulary", "new english words", "new words in english", "how to learn new words in english", "vocabulary for students", "30 vocabulary", "kanchan mam", "english vocabulary class", "vocabulary words", "english connection vocabulary", "new vocabs"], "category": "Film & Animation", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 8, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "बंद करो Basic English Words 😮बोलना | Learn 30 New Vocabulary Words | English Connection by <PERSON><PERSON><PERSON>.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "isDefault": true, "url": "vnvyNi", "_id": "6826cde0301da66bde453c53"}, {"code": "hi", "name": "Hindi", "flag": "🇮🇳", "isDefault": false, "url": "vnvyNi", "_id": "6826cde0301da66bde453c54"}], "source": {"type": "embed", "originalUrl": "vnvyNi", "platform": "engaxe", "externalId": "vnvyNi"}, "createdAt": "2025-05-16T05:32:16.066Z", "updatedAt": "2025-05-16T05:32:16.066Z", "searchKeywords": ["बंद", "करो", "basic", "english", "words", "😮बोलना", "learn", "new", "vocabulary", "connection", "kanchan", "⚡boost", "your", "and", "start", "speaking", "confidently", "with", "advanced", "words!", "this", "video", "ma&#039;am", "from", "will", "help", "you", "stop", "using", "overused", "fresh", "sound", "more", "fluent", "professional.", "ｌｉｋｅ", "ｃｏｍｍｅｎｔ", "ｓｈａｒｅ", "ｓｕｂｓｃｒｉｂｅ", "subscribe", "&amp;", "hit", "the", "bell", "icon:", "https://cutt.ly/subscribe_for_free", "everyday:", "🔗link", "buy", "book:", "https://cutt.ly/best_book_to_learn_english", "join", "channel", "get", "access", "perks:", "https://www.youtube.com/channel/ucsuboyj2kqgj72g6kgzgpmg/join", "free", "courses:", "spoken", "course:", "https://cutt.ly/spkenenglish", "grammar", "https://cutt.ly/w4whij6", "watch", "some", "trendig", "videos👇", "all", "tenses", "https://cutt.ly/tenses", "जीरो", "पढ़ना-लिखना", "सीखें:", "https://cutt.ly/ptuv3vy", "कितनी", "आनी", "चाहिए,", "लिए:", "https://cutt.ly/s4whhee", "follow", "other", "social", "media:", "facebook", "https://www.facebook.com/englishconnectionbykanchan", "instagram", "https://www.instagram.com/englishconnectionbykanchan/", "-------------------------------------------------", "download", "app:", "https://play.google.com/store/apps/details?id=com.appx.english_connection&amp;pli=1", "call", "8595926123", "(for", "course-related", "enquiry)", "pana", "hai", "perfection,", "kanchan!", "for", "enquiry,", "english connection", "kanchan english", "english words", "how to boost vocabulary", "expand your vocabulary", "basic english words", "kanchan keshari", "basic english words for beginners", "learn english vocabulary", "new vocabulary", "kanchan english vocabulary", "new english words", "new words in english", "how to learn new words in english", "vocabulary for students", "30 vocabulary", "kanchan mam", "english vocabulary class", "vocabulary words", "english connection vocabulary", "new vocabs", "film & animation"], "watchUrl": "http://localhost:5173/watch?id=fcd5de9c4bee35169e865de6c6fb286b"}, {"id": "9761116acabb0587f14b22872f225b6d", "title": "Nano Technology - Audio Article", "description": "Nanotechnology encompasses the understanding of the fundamental physics, chemistry, biology, and technology of nanometer-scale objects. It is being said that nanotechnology belongs to the 21st century. Keeping this in mind we are presenting an audio article on this topic. Drishti IAS has taken an initiative to provide the best material to Civil Services Aspirants. This information will help you to make a better understanding without any coaching. Inputs are taken from reputed English Newspapers e.g. The Hindu and the Indian Express as well as team Drishti inputs are also included. नैनोतकनीक पदार्थों का नैनोस्केल पर अध्ययन है। इसे 21वीं सदी की तकनीक कहा जा रहा है।इसके अनुप्रयोग आज हर क्षेत्र में है।इसे ध्यान में रखते हुए हम इस मुद्दे पर एक ऑडियो आर्टिकल पेश कर रहे हैं। दृष्टि आईएएस ने सिविल सेवा उम्मीदवारों को सर्वोत्तम सामग्री प्रदान करने के लिए एक पहल की है। ये जानकारियाँ आपको बिना किसी कोचिंग की सहायता के बेहतर समझ बनाने में मदद करेंगी । प्रतिष्ठित अंग्रेजी समाचार पत्र, मसलन- द हिंदू और द इंडियन एक्सप्रेस के साथ-साथ टीम दृष्टि के इनपुट्स भी इस आर्टिकल में शामिल किए गए हैं। =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 𝐎𝐧𝐥𝐢𝐧𝐞 𝐅𝐨𝐮𝐧𝐝𝐚𝐭𝐢𝐨𝐧 𝐂𝐨𝐮𝐫𝐬𝐞 (𝐏𝐫𝐞𝐥𝐢𝐦𝐬 + 𝐌𝐚𝐢𝐧𝐬) 𝐏𝐞𝐧𝐝𝐫𝐢𝐯𝐞 𝐌𝐨𝐝𝐞: 👉 For 𝐟𝐞𝐞, 𝐀𝐝𝐦𝐢𝐬𝐬𝐢𝐨𝐧 and 𝐨𝐭𝐡𝐞𝐫 𝐝𝐞𝐭𝐚𝐢𝐥𝐬 of this course download the 𝐃𝐫𝐢𝐬𝐡𝐭𝐢 𝐋𝐞𝐚𝐫𝐧𝐢𝐧𝐠 𝐀𝐩𝐩 from this link: https://bit.ly/3bqTzjH 👉 To know the 𝐝𝐞𝐭𝐚𝐢𝐥𝐬 𝐨𝐫 𝐭𝐨 𝐫𝐞𝐠𝐢𝐬𝐭𝐞𝐫 through our website click on this link: https://bit.ly/2JF1cJx 👉 𝐓𝐨 𝐠𝐞𝐭 𝐭𝐡𝐞 𝐝𝐞𝐭𝐚𝐢𝐥𝐬 about this 𝐖𝐡𝐚𝐭𝐬𝐀𝐩𝐩 𝐨𝐫 𝐒𝐌𝐒 &quot;𝐆𝐒&quot; on this number: 𝟗𝟑𝟏𝟏𝟒𝟎𝟔𝟒𝟒𝟐 =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 𝐏𝐞𝐧𝐝𝐫𝐢𝐯𝐞 𝐂𝐨𝐮𝐫𝐬𝐞𝐬 (𝐇𝐢𝐧𝐝𝐢 𝐋𝐢𝐭𝐞𝐫𝐚𝐭𝐮𝐫𝐞 𝐚𝐧𝐝 𝐈𝐀𝐒 𝐏𝐫𝐞𝐥𝐢𝐦𝐬): 👉 For 𝐟𝐞𝐞, 𝐀𝐝𝐦𝐢𝐬𝐬𝐢𝐨𝐧 and 𝐨𝐭𝐡𝐞𝐫 𝐝𝐞𝐭𝐚𝐢𝐥𝐬 of 𝐏𝐞𝐧𝐝𝐫𝐢𝐯𝐞 𝐂𝐨𝐮𝐫𝐬𝐞 (Hindi Literature and IAS Prelims): http://bit.ly/2vGNj72 👉 𝐃𝐞𝐦𝐨 𝐜𝐥𝐚𝐬𝐬 playlist- http://bit.ly/2qyY1cW =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 👉 𝐏𝐫𝐞𝐥𝐢𝐦𝐬 𝐏𝐫𝐚𝐜𝐭𝐢𝐜𝐞 𝐒𝐞𝐫𝐢𝐞𝐬 की पुस्तकें (संपूर्ण सेट एक साथ) खरीदने के लिये निम्नलिखित लिंक्स का प्रयोग करें: 𝐃𝐫𝐢𝐬𝐡𝐭𝐢 𝐖𝐞𝐛𝐬𝐢𝐭𝐞: https://bit.ly/30LLMbt 𝐀𝐦𝐚𝐳𝐨𝐧: https://amzn.to/2VqvH8S 𝐅𝐥𝐢𝐩𝐤𝐚𝐫𝐭: https://bit.ly/3odDQtv =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 👉 𝐐𝐮𝐢𝐜𝐤 𝐁𝐨𝐨𝐤 𝐒𝐞𝐫𝐢𝐞𝐬 की पुस्तकें (संपूर्ण सेट एक साथ) खरीदने के लिये निम्नलिखित लिंक्स का प्रयोग करें: 𝐃𝐫𝐢𝐬𝐡𝐭𝐢 𝐖𝐞𝐛𝐬𝐢𝐭𝐞: https://bit.ly/36u5qwD 𝐀𝐦𝐚𝐳𝐨𝐧: https://amzn.to/3lvMjGX 𝐅𝐥𝐢𝐩𝐤𝐚𝐫𝐭: https://bit.ly/37lO3NS =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 👉 𝐌𝐚𝐢𝐧𝐬 𝐂𝐚𝐩𝐬𝐮𝐥𝐞 𝐒𝐞𝐫𝐢𝐞𝐬 की पुस्तकें खरीदने के लिये निम्नलिखित लिंक्स का प्रयोग करें: 𝐃𝐫𝐢𝐬𝐡𝐭𝐢 𝐖𝐞𝐛𝐬𝐢𝐭𝐞: https://bit.ly/2L0efWP 𝐀𝐦𝐚𝐳𝐨𝐧: https://amzn.to/2HYgpF9 =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 👉 𝐑𝐀𝐒 𝐒𝐞𝐫𝐢𝐞𝐬 की पुस्तकें खरीदने के लिये निम्नलिखित लिंक्स का प्रयोग करें: 𝐃𝐫𝐢𝐬𝐡𝐭𝐢 𝐖𝐞𝐛𝐬𝐢𝐭𝐞: http://bit.ly/35hpX6T 𝐅𝐥𝐢𝐩𝐤𝐚𝐫𝐭: https://bit.ly/380zfWu 𝐀𝐦𝐚𝐳𝐨𝐧: http://amzn.to/3bbxw2L =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 𝐅𝐨𝐫 𝐕𝐢𝐝𝐞𝐨 𝐮𝐩𝐝𝐚𝐭𝐞𝐬 𝐟𝐨𝐥𝐥𝐨𝐰 𝐮𝐬 𝐨𝐧 : 👉 Facebook : https://www.facebook.com/DrishtiIASYouTube 👉 Twitter : https://twitter.com/DrishtiVideos 👉 Instagram : https://www.instagram.com/drishtiias 👉 Telegram : https://t.me/drishtiiasofficial =̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵ 👉 𝐎𝐮𝐫 𝐑𝐞𝐠𝐮𝐥𝐚𝐫 𝐘𝐨𝐮𝐓𝐮𝐛𝐞 𝐏𝐫𝐨𝐠𝐫𝐚𝐦𝐬&#039; 𝐏𝐥𝐚𝐲𝐥𝐢𝐬𝐭𝐬: 1. Videos by Dr. Vikas Divyakriti: http://bit.ly/2TF34TZ To 2. Concept Talk- By Dr. Vikas Divyakirti: https://goo.gl/xNVMrm 3. Current News Bulletin: https://goo.gl/TRiKpS 4. In News: https://bit.ly/2VsYdqt 5. Today&#039;s GK: https://goo.gl/qYva9T 6. To The Point- Special: http://bit.ly/2UwSbpv 7. Audio Article: https://goo.gl/MV1BRW 8. UPSC PT Prelims ...", "url": "UnqRml", "thumbnailUrl": "https://i.ytimg.com/vi/CWyDk33Sc8U/maxresdefault.jpg", "duration": 0, "userId": "57e0d78a-fb6d-4775-8473-bbb3b17ee52e", "channelId": "e8a48d64-55b3-466e-aa55-4c72c2bf27a8", "visibility": "public", "tags": ["nanotechnology upsc", "nanotechnology only ias", "nanotechnology upsc mains", "application of nanotechnology in hindi. nanotechnology study iq", "upsc mains paper 3", "upsc one more attempt", "upsc one more attempt news", "upsc one more chance", "supreme court decision on upsc exam", "upsc last attempt", "bpsc mains syllabus", "nanotechnology mppsc mains", "nanotechnology in hindi", "nanotechnology courses after 12th", "nanotechnology engineering lectures", "nanotechnology lectures"], "category": "Film & Animation", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 85, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "Nano Technology - Audio Article.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "hi", "name": "Hindi", "flag": "🇮🇳", "isDefault": true, "url": "UnqRml", "_id": "68272f49a6593e82179cd200"}], "source": {"type": "embed", "originalUrl": "UnqRml", "platform": "engaxe", "externalId": "UnqRml"}, "createdAt": "2025-05-16T12:27:53.549Z", "updatedAt": "2025-05-16T12:27:53.549Z", "searchKeywords": ["nano", "technology", "audio", "article", "nanotechnology", "encompasses", "the", "understanding", "fundamental", "physics,", "chemistry,", "biology,", "and", "nanometer-scale", "objects.", "being", "said", "that", "belongs", "21st", "century.", "keeping", "this", "mind", "are", "presenting", "topic.", "drishti", "ias", "has", "taken", "initiative", "provide", "best", "material", "civil", "services", "aspirants.", "information", "will", "help", "you", "make", "better", "without", "any", "coaching.", "inputs", "from", "reputed", "english", "newspapers", "e.g.", "hindu", "indian", "express", "well", "team", "also", "included.", "नैनोतकनीक", "पदार्थों", "नैनोस्केल", "अध्ययन", "है।", "इसे", "21वीं", "सदी", "तकनीक", "कहा", "र<PERSON><PERSON>", "है।इसके", "अनुप्रयोग", "क्षेत्र", "में", "है।इसे", "ध्यान", "रखते", "हुए", "मुद्दे", "ऑडियो", "आर्टिकल", "पेश", "रहे", "हैं।", "दृष्टि", "आईएएस", "सिविल", "सेवा", "उम्मीदवारों", "सर्वोत्तम", "सामग्री", "प्रदान", "करने", "लिए", "पहल", "जानकारियाँ", "आपको", "बिना", "किसी", "कोचिंग", "सहायता", "बेहतर", "समझ", "बनाने", "मदद", "करेंगी", "प्रतिष्ठित", "अंग्रेजी", "समाचार", "पत्र,", "मसलन-", "हिंदू", "इंडियन", "एक्सप्रेस", "साथ-साथ", "टीम", "इनपुट्स", "शामिल", "किए", "=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵=̵", "𝐎𝐧𝐥𝐢𝐧𝐞", "𝐅𝐨𝐮𝐧𝐝𝐚𝐭𝐢𝐨𝐧", "𝐂𝐨𝐮𝐫𝐬𝐞", "(𝐏𝐫𝐞𝐥𝐢𝐦𝐬", "𝐌𝐚𝐢𝐧𝐬)", "𝐏𝐞𝐧𝐝𝐫𝐢𝐯𝐞", "𝐌𝐨𝐝𝐞:", "for", "𝐟𝐞𝐞,", "𝐀𝐝𝐦𝐢𝐬𝐬𝐢𝐨𝐧", "𝐨𝐭𝐡𝐞𝐫", "𝐝𝐞𝐭𝐚𝐢𝐥𝐬", "course", "download", "𝐃𝐫𝐢𝐬𝐡𝐭𝐢", "𝐋𝐞𝐚𝐫𝐧𝐢𝐧𝐠", "𝐀𝐩𝐩", "link:", "https://bit.ly/3bqtzjh", "know", "𝐨𝐫", "𝐭𝐨", "𝐫𝐞𝐠𝐢𝐬𝐭𝐞𝐫", "through", "our", "website", "click", "https://bit.ly/2jf1cjx", "𝐓𝐨", "𝐠𝐞𝐭", "𝐭𝐡𝐞", "about", "𝐖𝐡𝐚𝐭𝐬𝐀𝐩𝐩", "𝐒𝐌𝐒", "&quot;𝐆𝐒&quot;", "number:", "𝟗𝟑𝟏𝟏𝟒𝟎𝟔𝟒𝟒𝟐", "𝐂𝐨𝐮𝐫𝐬𝐞𝐬", "(𝐇𝐢𝐧𝐝𝐢", "𝐋𝐢𝐭𝐞𝐫𝐚𝐭𝐮𝐫𝐞", "𝐚𝐧𝐝", "𝐈𝐀𝐒", "𝐏𝐫𝐞𝐥𝐢𝐦𝐬):", "(hindi", "literature", "prelims):", "http://bit.ly/2vgnj72", "𝐃𝐞𝐦𝐨", "𝐜𝐥𝐚𝐬𝐬", "playlist-", "http://bit.ly/2qyy1cw", "𝐏𝐫𝐞𝐥𝐢𝐦𝐬", "𝐏𝐫𝐚𝐜𝐭𝐢𝐜𝐞", "𝐒𝐞𝐫𝐢𝐞𝐬", "पुस्तकें", "(संपूर्ण", "सेट", "साथ)", "खरीदने", "लिये", "निम्नलिखित", "लिंक्स", "प्रयोग", "करें:", "𝐖𝐞𝐛𝐬𝐢𝐭𝐞:", "https://bit.ly/30llmbt", "𝐀𝐦𝐚𝐳𝐨𝐧:", "https://amzn.to/2vqvh8s", "𝐅𝐥𝐢𝐩𝐤𝐚𝐫𝐭:", "https://bit.ly/3oddqtv", "𝐐𝐮𝐢𝐜𝐤", "𝐁𝐨𝐨𝐤", "https://bit.ly/36u5qwd", "https://amzn.to/3lvmjgx", "https://bit.ly/37lo3ns", "𝐌𝐚𝐢𝐧𝐬", "𝐂𝐚𝐩𝐬𝐮𝐥𝐞", "https://bit.ly/2l0efwp", "https://amzn.to/2hygpf9", "𝐑𝐀𝐒", "http://bit.ly/35hpx6t", "https://bit.ly/380zfwu", "http://amzn.to/3bbxw2l", "𝐅𝐨𝐫", "𝐕𝐢𝐝𝐞𝐨", "𝐮𝐩𝐝𝐚𝐭𝐞𝐬", "𝐟𝐨𝐥𝐥𝐨𝐰", "𝐮𝐬", "𝐨𝐧", "facebook", "https://www.facebook.com/drishtiiasyoutube", "twitter", "https://twitter.com/drishtivideos", "instagram", "https://www.instagram.com/drishtiias", "telegram", "https://t.me/drishtiiasofficial", "𝐎𝐮𝐫", "𝐑𝐞𝐠𝐮𝐥𝐚𝐫", "𝐘𝐨𝐮𝐓𝐮𝐛𝐞", "𝐏𝐫𝐨𝐠𝐫𝐚𝐦𝐬&#039;", "𝐏𝐥𝐚𝐲𝐥𝐢𝐬𝐭𝐬:", "videos", "dr.", "vikas", "divy<PERSON>riti:", "http://bit.ly/2tf34tz", "concept", "talk-", "divy<PERSON><PERSON>i:", "https://goo.gl/xnvmrm", "current", "news", "bulletin:", "https://goo.gl/trikps", "news:", "https://bit.ly/2vsydqt", "today&#039;s", "gk:", "https://goo.gl/qyva9t", "point-", "special:", "http://bit.ly/2uwsbpv", "article:", "https://goo.gl/mv1brw", "upsc", "prelims", "...", "nanotechnology upsc", "nanotechnology only ias", "nanotechnology upsc mains", "application of nanotechnology in hindi. nanotechnology study iq", "upsc mains paper 3", "upsc one more attempt", "upsc one more attempt news", "upsc one more chance", "supreme court decision on upsc exam", "upsc last attempt", "bpsc mains syllabus", "nanotechnology mppsc mains", "nanotechnology in hindi", "nanotechnology courses after 12th", "nanotechnology engineering lectures", "nanotechnology lectures", "film & animation"], "watchUrl": "http://localhost:5173/watch?id=9761116acabb0587f14b22872f225b6d"}, {"id": "e3a89d1af562e48278610839b7beac67", "title": "Game Theory In Operations Research | Operations Research | Saddle Point | Rajnish Sharma Classes", "description": "App - <a href=\"https://play.google.com/store/apps/details%3Fid%3Dcom.rajnish.sharma.classes\">https://play.google.com/store/apps/details%3Fid%3Dcom.rajnish.sharma.classes</a> Follow Us On Instagram - <a href=\"https://instagram.com/rajnish_sharma_class\">https://instagram.com/rajnish_sharma_class</a> Game Theory In Operations Research | Operations Research | Saddle Point | Rajnish Sharma Classes | Raj<PERSON> Sharma Sir | MBA OR | mba operations research Tags - Operations Research Models Transportation Model in Operation Research North West Corner Rule north west corner rule in operation research <PERSON><PERSON> Sharma Classes <PERSON><PERSON> Sharma Sir mba operations research MBA OR mca operation research operations research or transportation problem transportation model transportation problem in operation research operation research lecture operation research methods of transportation problem mba 2023 davv mba mba maths mba DAVV MCA maths", "url": "suZKhW", "thumbnailUrl": "https://i.ytimg.com/vi/Novs_21DiM8/maxresdefault.jpg", "duration": 0, "userId": "57e0d78a-fb6d-4775-8473-bbb3b17ee52e", "channelId": "e8a48d64-55b3-466e-aa55-4c72c2bf27a8", "visibility": "public", "tags": ["Game Theory In Operations Research", "Operations Research", "Saddle Point", "<PERSON><PERSON> Classes", "game theory in operation research", "game theory in operations research in hindi", "game theory", "<PERSON><PERSON>", "MBA OR", "mba", "mba 2023", "saddle point game theory", "saddle point in hindi", "pure strategy game theory", "2 person 0 sum games", "game theory operations research", "two person zero sum game", "mba exam", "mba 2nd sem", "mba 2nd sem classes", "operations research mba 2nd sem", "davv indore", "OR", "Game Theory In Operations Research", "Operations Research", "Saddle Point", "<PERSON><PERSON> Classes", "game theory in operation research", "game theory in operations research in hindi", "game theory", "<PERSON><PERSON>", "MBA OR", "mba", "mba 2023", "saddle point game theory", "saddle point in hindi", "pure strategy game theory", "2 person 0 sum games", "game theory operations research", "two person zero sum game", "mba exam", "mba 2nd sem", "mba 2nd sem classes", "operations research mba 2nd sem", "davv indore", "OR"], "category": "Education", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 95, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "Game Theory In Operations Research | Operations Research | Saddle Point | Rajnish Sharma Classes.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "isDefault": true, "url": "suZKhW", "_id": "682b1d4122a97204386e988f"}, {"code": "hi", "name": "Hindi", "flag": "🇮🇳", "isDefault": false, "url": "suZKhW", "_id": "682b1d4122a97204386e9890"}], "source": {"type": "embed", "originalUrl": "suZKhW", "platform": "engaxe", "externalId": "suZKhW"}, "createdAt": "2025-05-19T12:00:01.695Z", "updatedAt": "2025-05-19T12:00:01.695Z", "searchKeywords": ["game", "theory", "operations", "research", "saddle", "point", "raj<PERSON>", "sharma", "classes", "app", "href=\"https://play.google.com/store/apps/details%3fid%3dcom.rajnish.sharma.classes\">https://play.google.com/store/apps/details%3fid%3dcom.rajnish.sharma.classes</a>", "follow", "instagram", "href=\"https://instagram.com/rajnish_sharma_class\">https://instagram.com/rajnish_sharma_class</a>", "sir", "mba", "tags", "models", "transportation", "model", "operation", "north", "west", "corner", "rule", "mca", "problem", "lecture", "methods", "2023", "davv", "maths", "game theory in operations research", "operations research", "saddle point", "rajnish sharma classes", "game theory in operation research", "game theory in operations research in hindi", "game theory", "rajnish sharma sir", "mba or", "mba 2023", "saddle point game theory", "saddle point in hindi", "pure strategy game theory", "2 person 0 sum games", "game theory operations research", "two person zero sum game", "mba exam", "mba 2nd sem", "mba 2nd sem classes", "operations research mba 2nd sem", "davv indore", "or", "education"], "watchUrl": "http://localhost:5173/watch?id=e3a89d1af562e48278610839b7beac67"}, {"id": "6dcb0abece51806246febab0eddad0c5", "title": "Logarithm Introduction | Laws Of Logarithm |Existence Of Log | Mathematics  Concept Series", "description": "👉🏻In this video sir explain important concepts of basic Mathematics. So let&#039;s start with concepts and then apply them to few questions from JEE &amp; then see the miracle. It helps aspirants for JEE 2023 preparation and offers excellent actionable tips to make it happen. 👉🏻 The Captivating Mentor for IIT-JEE Prof. <PERSON><PERSON><PERSON>arg Sir, describes some JEE Mains &amp; Advance Concepts. Surely you don&#039;t know these concepts as any coaching or teacher do not teach to that depth other than CatalyseR. 👉🏻 This video is very helpful for your upcoming JEE examination. Sir also described some few important points that are to be noted for your exam ….. 👉🏻 Through this video you will definitely enhance your marks. This video provides a brief explanation of all the points. We hope this video will help you. Thanks for watching this video. Do not forget to Like | Comment | Share BOSON Test Enroll link https://events.catalyser.in/bosontest CatalyseR Google link https://www.catalyser.in/ CatalyseR Insta Link https://www.instagram.com/catalyser_o... CatalyseR Facebook Link https://www.facebook.com/CatalyserEdu/ CatalyseR Youtube Link https://www.youtube.com/@CatalyseR CatalyseR app Link https://play.google.com/store/apps/de... CatalyseR Linkdin link https://in.linkedin.com/company/catal... CatalyseR Twitter Link https://mobile.twitter.com/catalyseri... IIT Safalta Link https://iitsafalta.in/ IIT Safalta app Link https://play.google.com/store/apps/de... Ntseguru Link https://www.ntseguru.in/ For any Enquiry Contact 9109120115 Drop Your Concern https://www.catalyser.in/contact-us &#039;tknow # CBSEboardexam", "url": "sf8Sy2", "thumbnailUrl": "https://i.ytimg.com/vi/-pg9T2a9k8w/maxresdefault.jpg", "duration": 0, "userId": "126f3d3e-450c-419b-93e0-b8f60da97360", "channelId": "750d390b-1d23-462f-a655-f5da4230eedc", "visibility": "public", "tags": ["#maths", "#log"], "category": "Education", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 161, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "Logarithm Introduction | Laws Of Logarithm |Existence Of Log | Mathematics  Concept Series.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "isDefault": true, "url": "sf8Sy2", "_id": "682c3715126d336667359782"}], "source": {"type": "embed", "originalUrl": "sf8Sy2", "platform": "engaxe", "externalId": "sf8Sy2"}, "createdAt": "2025-05-20T08:02:29.176Z", "updatedAt": "2025-05-20T08:02:29.176Z", "searchKeywords": ["logarithm", "introduction", "laws", "|existence", "log", "mathematics", "concept", "series", "👉🏻in", "this", "video", "sir", "explain", "important", "concepts", "basic", "mathematics.", "let&#039;s", "start", "with", "and", "then", "apply", "them", "few", "questions", "from", "jee", "&amp;", "see", "the", "miracle.", "helps", "aspirants", "for", "2023", "preparation", "offers", "excellent", "actionable", "tips", "make", "happen.", "👉🏻", "captivating", "mentor", "iit-jee", "prof.", "sumeet", "garg", "sir,", "describes", "some", "mains", "advance", "concepts.", "surely", "you", "don&#039;t", "know", "these", "any", "coaching", "teacher", "not", "teach", "that", "depth", "other", "than", "catalyser.", "very", "helpful", "your", "upcoming", "examination.", "also", "described", "points", "are", "noted", "exam", "…..", "through", "will", "definitely", "enhance", "marks.", "provides", "brief", "explanation", "all", "points.", "hope", "help", "you.", "thanks", "watching", "video.", "forget", "like", "comment", "share", "boson", "test", "enroll", "link", "https://events.catalyser.in/bosontest", "catalyser", "google", "https://www.catalyser.in/", "insta", "https://www.instagram.com/catalyser_o...", "facebook", "https://www.facebook.com/catalyseredu/", "youtube", "https://www.youtube.com/@catalyser", "app", "https://play.google.com/store/apps/de...", "linkdin", "https://in.linkedin.com/company/catal...", "twitter", "https://mobile.twitter.com/catalyseri...", "iit", "safalta", "https://iitsafalta.in/", "ntseguru", "https://www.ntseguru.in/", "enquiry", "contact", "9109120115", "drop", "concern", "https://www.catalyser.in/contact-us", "&#039;tknow", "cbseboardexam", "#maths", "#log", "education"], "watchUrl": "http://localhost:5173/watch?id=6dcb0abece51806246febab0eddad0c5"}, {"id": "f0e88530d58b981e01df96f115735f8a", "title": "PLAY UNO WITH YOUR KEYBOARD", "description": "Play UNO with your keyboard! Use the keys onscreen to choose your cards, grab cards from the deck, and win this new, heart-pounding, interactive, challenging edition in the &quot;UNO: YOU PLAY!&quot; series! Only YOU have the reflexes and skills required to outsmart your opponents! But can you pull through and win for good?! Every card could spell your doom, so MAKE YOUR CHOICES COUNT! 0:00 - INTRO 0:40 - BLUE 0 1:22 - BLUE 7 2:12 - GREEN SKIP 2:29 - DRAW A CARD 2:56 - YELLOW REVERSE 4:24 - RED 5 4:43 - RED ****:40 - GREEN 5 6:36 - GREEN SKIP 7:42 - BLUE 8 7:59 - SAY &quot;UNO&quot; 8:49 - RED +2 9:23 - RED 5 9:49 - BLUE 7 10:32 - <PERSON><PERSON><PERSON> 5 11:00 - RED 5 11:32 - RED +2 12:01 - RED +2 12:40 - GREEN SKIP 13:12 - GREEN 5 13:31 - YELLOW REVERSE 14:02 - RED 5 14:26 - RED +2 15:24 - <PERSON><PERSON><PERSON> 7 15:49 - <PERSON><PERSON><PERSON> 0 16:29 - DRAW A CARD 16:53 - <PERSON><PERSON><PERSON> 1 17:36 - RED +2 18:31 - BLUE 9 19:10 - DRAW A CARD 19:32 - RED +2 19:48 - GREEN SKIP 21:10 - BLUE 4 BECOME A PATREON MEMBER: https://www.patreon.com/LagartoFilms FOLLOW US: TikTok: https://www.tiktok.com/@lagartofilms Facebook: https://www.facebook.com/lagartofilms Instagram: https://www.instagram.com/lagartofilmss/ Play UNO With Your Keyboard | CAST: You - Adrián Rivera Richard - Manolo Gutiérrez Derrick - Luis Benitez CREW: Director &amp; Editor - Adrián Rivera Scriptwriter - Adrián Rivera LAGARTO FILMS 2025", "url": "Dk6fJJ", "thumbnailUrl": "https://i.ytimg.com/vi/w_yJ2iA5zR0/maxresdefault.jpg", "duration": 0, "userId": "57e0d78a-fb6d-4775-8473-bbb3b17ee52e", "channelId": "2b60d5ca-9593-43d5-9e7e-414bdbc3dcce", "visibility": "public", "tags": ["UNO"], "category": "Gaming", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 8, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "PLAY UNO WITH YOUR KEYBOARD.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "isDefault": true, "url": "Dk6fJJ", "_id": "682d9204e43e8a28a37cf83b"}], "source": {"type": "embed", "originalUrl": "Dk6fJJ", "platform": "engaxe", "externalId": "Dk6fJJ"}, "createdAt": "2025-05-21T08:42:45.032Z", "updatedAt": "2025-05-21T08:42:45.032Z", "searchKeywords": ["play", "uno", "with", "your", "keyboard", "keyboard!", "use", "the", "keys", "onscreen", "choose", "cards,", "grab", "cards", "from", "deck,", "and", "win", "this", "new,", "heart-pounding,", "interactive,", "challenging", "edition", "&quot;uno:", "you", "play!&quot;", "series!", "only", "have", "reflexes", "skills", "required", "outsmart", "opponents!", "but", "can", "pull", "through", "for", "good?!", "every", "card", "could", "spell", "doom,", "make", "choices", "count!", "0:00", "intro", "0:40", "blue", "1:22", "2:12", "green", "skip", "2:29", "draw", "2:56", "yellow", "reverse", "4:24", "red", "4:43", "5:40", "6:36", "7:42", "7:59", "say", "&quot;uno&quot;", "8:49", "9:23", "9:49", "10:32", "11:00", "11:32", "12:01", "12:40", "13:12", "13:31", "14:02", "14:26", "15:24", "15:49", "16:29", "16:53", "17:36", "18:31", "19:10", "19:32", "19:48", "21:10", "become", "patreon", "member:", "https://www.patreon.com/lagartofilms", "follow", "us:", "tiktok:", "https://www.tiktok.com/@lagartofilms", "facebook:", "https://www.facebook.com/lagartofilms", "instagram:", "https://www.instagram.com/lagartofilmss/", "cast:", "<PERSON><PERSON><PERSON>", "rivera", "richard", "manolo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "derrick", "luis", "ben<PERSON><PERSON>", "crew:", "director", "&amp;", "editor", "scriptwriter", "lagarto", "films", "2025", "gaming"], "watchUrl": "http://localhost:5173/watch?id=f0e88530d58b981e01df96f115735f8a"}, {"id": "b9866821c4aa1011bc90af0ff43a82c8", "title": "Maze - Interactive Video", "description": "<PERSON><PERSON>", "url": "s6SkuJ", "thumbnailUrl": "https://i.ytimg.com/vi/XLigv6taT50/hqdefault.jpg", "duration": 0, "userId": "57e0d78a-fb6d-4775-8473-bbb3b17ee52e", "channelId": "2b60d5ca-9593-43d5-9e7e-414bdbc3dcce", "visibility": "public", "tags": ["<PERSON><PERSON>"], "category": "Gaming", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 20, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "Maze - Interactive Video.mp4", "size": 1024, "mimeType": "video/mp4", "codec": "", "resolution": "", "bitrate": 0, "frameRate": 0}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "isDefault": true, "url": "s6SkuJ", "_id": "68300920c5984b6a15c01c72"}], "source": {"type": "embed", "originalUrl": "s6SkuJ", "platform": "engaxe", "externalId": "s6SkuJ"}, "createdAt": "2025-05-23T05:35:28.903Z", "updatedAt": "2025-05-23T05:35:28.903Z", "searchKeywords": ["maze", "interactive", "video", "gaming"], "watchUrl": "http://localhost:5173/watch?id=b9866821c4aa1011bc90af0ff43a82c8"}, {"id": "new_video_1748690735953", "title": "New Test Video - 5/31/2025, 4:55:35 PM", "description": "This is a new video that was automatically added to demonstrate the automatic JSON storage system. Every time you upload a video, it gets automatically stored here!", "url": "XLcMq2", "thumbnailUrl": "https://via.placeholder.com/480x360/4CAF50/FFFFFF?text=New+Video", "duration": 240, "userId": "test_user_123", "channelId": "test_channel_123", "visibility": "public", "tags": ["new", "automatic", "storage", "test", "demo"], "category": "Technology", "contentRating": "general", "processingStatus": "ready", "stats": {"views": 0, "likes": 0, "dislikes": 0, "comments": 0, "shares": 0, "playlistAdds": 0, "averageWatchTime": 0, "retentionRate": 0}, "file": {"originalName": "new-test-video.mp4", "size": 25165824, "mimeType": "video/mp4"}, "languages": [{"code": "en", "name": "English", "flag": "🇺🇸", "url": "XLcMq2", "isDefault": true}], "source": {"type": "upload", "originalUrl": "", "platform": "lawengaxe"}, "createdAt": "2025-05-31T11:25:35.953Z", "updatedAt": "2025-05-31T11:25:35.953Z", "searchKeywords": ["new", "test", "video", "automatic", "storage", "demo", "technology"], "watchUrl": "http://localhost:5173/watch?id=new_video_1748690735953"}]}