import { UserModel, RoleModel } from '../models';
import bcrypt from 'bcrypt';

/**
 * Seed users data
 */
async function seedUsers() {
  try {
    // Clear existing users
    await UserModel.deleteMany({});
    
    // Get role IDs
    const adminRole = await RoleModel.findOne({ code: 'admin' });
    const moderatorRole = await RoleModel.findOne({ code: 'moderator' });
    const creatorRole = await RoleModel.findOne({ code: 'creator' });
    const userRole = await RoleModel.findOne({ code: 'user' });
    
    if (!adminRole || !moderatorRole || !creatorRole || !userRole) {
      throw new Error('Required roles not found. Please seed roles first.');
    }
    
    // Hash passwords
    const salt = await bcrypt.genSalt(10);
    const adminPassword = await bcrypt.hash('admin123', salt);
    const moderatorPassword = await bcrypt.hash('moderator123', salt);
    const creatorPassword = await bcrypt.hash('creator123', salt);
    const userPassword = await bcrypt.hash('user123', salt);
    
    // Define users to seed
    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: adminPassword,
        firstName: 'Admin',
        lastName: 'User',
        displayName: 'System Admin',
        status: 'active',
        emailVerified: true,
        phoneVerified: true,
        roles: [adminRole.id],
        permissions: [],
        lastLoginAt: new Date(),
        lastLoginIp: '127.0.0.1',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        username: 'moderator',
        email: '<EMAIL>',
        password: moderatorPassword,
        firstName: 'Content',
        lastName: 'Moderator',
        displayName: 'Content Moderator',
        status: 'active',
        emailVerified: true,
        phoneVerified: true,
        roles: [moderatorRole.id],
        permissions: [],
        lastLoginAt: new Date(),
        lastLoginIp: '127.0.0.1',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        username: 'creator',
        email: '<EMAIL>',
        password: creatorPassword,
        firstName: 'Content',
        lastName: 'Creator',
        displayName: 'Legal Expert',
        status: 'active',
        emailVerified: true,
        phoneVerified: true,
        roles: [creatorRole.id],
        permissions: [],
        lastLoginAt: new Date(),
        lastLoginIp: '127.0.0.1',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        username: 'user',
        email: '<EMAIL>',
        password: userPassword,
        firstName: 'Regular',
        lastName: 'User',
        displayName: 'Regular User',
        status: 'active',
        emailVerified: true,
        phoneVerified: false,
        roles: [userRole.id],
        permissions: [],
        lastLoginAt: new Date(),
        lastLoginIp: '127.0.0.1',
        createdBy: 'system',
        updatedBy: 'system',
      },
    ];
    
    // Insert users
    await UserModel.insertMany(users);
    
    console.log(`✅ ${users.length} users seeded successfully`);
  } catch (error) {
    console.error('❌ Error seeding users:', error);
    throw error;
  }
}

export default seedUsers;
