import mongoose, { Schem<PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Channel interface extending the base entity
 */
export interface IChannel extends IBaseEntity {
  /**
   * Unique name for the channel
   * Used for URLs and @mentions
   */
  name: string;

  /**
   * Display name shown publicly
   */
  displayName: string;

  /**
   * Channel description
   */
  description: string;

  /**
   * URL to channel's banner image
   */
  banner?: string;

  /**
   * URL to channel's avatar/logo
   */
  avatar?: string;

  /**
   * Channel owner user ID
   */
  ownerId: string;

  /**
   * Array of user IDs who are moderators
   */
  moderators: string[];

  /**
   * Channel category
   */
  category?: string;

  /**
   * Channel tags for discovery
   */
  tags: string[];

  /**
   * Whether the channel is verified
   */
  isVerified: boolean;

  /**
   * Whether the channel is featured
   */
  isFeatured: boolean;

  /**
   * Channel visibility
   */
  visibility?: 'public' | 'private' | 'unlisted';

  /**
   * Channel status (active, suspended, etc.)
   */
  status: 'active' | 'suspended' | 'pending' | 'banned';

  /**
   * Reason for status if not active
   */
  statusReason?: string;

  /**
   * Channel statistics
   */
  stats: {
    /**
     * Number of subscribers
     */
    subscribers: number;

    /**
     * Total views across all videos
     */
    totalViews: number;

    /**
     * Number of videos published
     */
    videoCount: number;
  };

  /**
   * Social media links
   */
  socialLinks?: {
    website?: string;
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
    youtube?: string;
  };

  /**
   * Custom channel settings
   */
  settings?: {
    /**
     * Allow comments on videos by default
     */
    defaultCommentsEnabled: boolean;

    /**
     * Auto-approve comments or require moderation
     */
    moderateComments: boolean;

    /**
     * Show subscriber count publicly
     */
    showSubscriberCount: boolean;
  };
}

/**
 * Channel schema definition
 */
const ChannelSchema = new Schema<IChannel>(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true,
      minlength: 3,
      maxlength: 30,
      match: /^[a-z0-9_.-]+$/,
    },
    displayName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 5000,
    },
    banner: {
      type: String,
    },
    avatar: {
      type: String,
    },
    ownerId: {
      type: String,
      required: true,
      ref: 'User',
    },
    moderators: {
      type: [String],
      default: [],
      ref: 'User',
    },
    category: {
      type: String,
      ref: 'Category',
    },
    tags: {
      type: [String],
      default: [],
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    isFeatured: {
      type: Boolean,
      default: false,
    },
    visibility: {
      type: String,
      enum: ['public', 'private', 'unlisted'],
      default: 'public',
    },
    status: {
      type: String,
      enum: ['active', 'suspended', 'pending', 'banned'],
      default: 'active',
    },
    statusReason: {
      type: String,
    },
    stats: {
      subscribers: {
        type: Number,
        default: 0,
      },
      totalViews: {
        type: Number,
        default: 0,
      },
      videoCount: {
        type: Number,
        default: 0,
      },
    },
    socialLinks: {
      website: String,
      facebook: String,
      twitter: String,
      instagram: String,
      linkedin: String,
      youtube: String,
    },
    settings: {
      defaultCommentsEnabled: {
        type: Boolean,
        default: true,
      },
      moderateComments: {
        type: Boolean,
        default: false,
      },
      showSubscriberCount: {
        type: Boolean,
        default: true,
      },
    },
  },
  { timestamps: true }
);

// Merge with base schema
ChannelSchema.add(BaseSchema);

// Add compound indexes for unique fields with soft delete support
ChannelSchema.index({ name: 1, deletedAt: 1 }, { unique: true, sparse: true });

// Create and export the Channel model
const ChannelModel = mongoose.model<IChannel>('Channel', ChannelSchema);
export default ChannelModel;
