import React, { useState, useEffect, useRef } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useMessages } from '@/context/MessageContext';
import { useToast } from '@/hooks/use-toast';
import Layout from '@/components/layout/Layout';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Send, ArrowLeft } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { Message, User } from '@/types';
import { channelAPI } from '@/services/channel-api';
import api from '@/services/api';

const ChannelChatPage: React.FC = () => {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const channelId = searchParams.get('channelId');
  const { conversations, messages, sendTextMessage, createConversation, setActiveConversation } = useMessages();

  const [messageText, setMessageText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [channel, setChannel] = useState<any>(null);
  const [activeConversationId, setActiveConversationId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch channel data
  useEffect(() => {
    const fetchChannelData = async () => {
      if (!channelId) {
        toast({
          title: "Error",
          description: "Channel ID is missing",
          variant: "destructive"
        });
        navigate('/');
        return;
      }

      try {
        // Fetch channel data from the API
        const response = await channelAPI.getChannelById(channelId);

        if (response.success && response.channel) {
          // Get the channel data
          const channelData = response.channel;

          try {
            // Fetch the owner's information using the ownerId
            const ownerResponse = await api.get(`/users/${channelData.ownerId}`);

            let ownerName = channelData.displayName || "Channel Owner";
            let ownerAvatar = channelData.avatar || "/placeholder-owner-avatar.jpg";

            if (ownerResponse.data && ownerResponse.data.success) {
              const ownerData = ownerResponse.data.user;
              ownerName = ownerData.displayName || ownerData.username || `${ownerData.firstName} ${ownerData.lastName}`.trim();
              ownerAvatar = ownerData.avatar || "/placeholder-owner-avatar.jpg";
            } else {
              // If we can't get the owner's information, use the channel's display name
              console.log("Using channel display name as owner name:", ownerName);
            }

            // Create a channel object with owner information
            const channelWithOwner = {
              id: channelData.id,
              name: channelData.name,
              displayName: channelData.displayName,
              ownerId: channelData.ownerId,
              ownerName: ownerName,
              ownerAvatar: ownerAvatar,
              avatar: channelData.avatar || "/placeholder-avatar.jpg"
            };

            setChannel(channelWithOwner);
          } catch (ownerError) {
            console.error('Error fetching owner information:', ownerError);

            // Create a channel object with default owner information
            // Use the channel's display name as the owner name
            const channelWithDefaultOwner = {
              id: channelData.id,
              name: channelData.name,
              displayName: channelData.displayName,
              ownerId: channelData.ownerId,
              ownerName: channelData.displayName || "Channel Owner", // Use channel display name as owner name
              ownerAvatar: channelData.avatar || "/placeholder-owner-avatar.jpg",
              avatar: channelData.avatar || "/placeholder-avatar.jpg"
            };

            setChannel(channelWithDefaultOwner);
          }
        } else {
          throw new Error("Failed to fetch channel data");
        }
      } catch (error) {
        console.error('Error fetching channel:', error);

        // Fallback to mock data if API fails
        // Use a more descriptive channel name that will also serve as the owner name
        const channelDisplayName = "Channel " + channelId.substring(0, 4);
        const mockChannel = {
          id: channelId,
          name: "channel-" + channelId.substring(0, 4).toLowerCase(),
          displayName: channelDisplayName,
          ownerId: "owner-id",
          ownerName: channelDisplayName, // Use the channel name as the owner name
          ownerAvatar: "/placeholder-owner-avatar.jpg",
          avatar: "/placeholder-avatar.jpg"
        };

        setChannel(mockChannel);

        toast({
          title: "Warning",
          description: "Using mock data - couldn't connect to server",
          variant: "default"
        });
      }
    };

    fetchChannelData();
  }, [channelId, toast, navigate]);

  // Find or create conversation between user and channel owner
  useEffect(() => {
    const initializeConversation = async () => {
      if (!channel) return;

      try {
        setIsLoading(true);

        // If user is logged in, check for existing conversation
        if (currentUser) {
          // Check if a conversation already exists with this channel owner
          const existingConversation = conversations.find(
            conv =>
              (conv.participants.some(p => p.id === channel.ownerId) &&
               conv.participants.some(p => p.id === currentUser.id))
          );

          if (existingConversation) {
            setActiveConversation(existingConversation.id);
            setActiveConversationId(existingConversation.id);
            return;
          }
        }

        // Create a new conversation if one doesn't exist or user is not logged in
        // For non-logged in users, we'll create a temporary conversation
        const tempUserId = localStorage.getItem('temp-user-id') || `guest-${Date.now()}`;
        if (!localStorage.getItem('temp-user-id')) {
          localStorage.setItem('temp-user-id', tempUserId);
        }

        const result = await createConversation({
          creatorId: channel.ownerId,
          subject: `Chat with ${channel.displayName}`,
          initialMessage: "",
          guestId: currentUser ? undefined : tempUserId
        });

        if (result && result.data) {
          setActiveConversationId(result.data.id);
        }
      } catch (error) {
        console.error('Error initializing conversation:', error);
        toast({
          title: "Error",
          description: "Failed to initialize conversation",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    initializeConversation();
  }, [channel, currentUser, conversations, createConversation, setActiveConversation]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!messageText.trim() || !activeConversationId) return;

    try {
      // For non-authenticated users, we'll use the temp user ID
      const tempUserId = !currentUser ? localStorage.getItem('temp-user-id') : undefined;

      await sendTextMessage(messageText, activeConversationId, tempUserId);
      setMessageText('');
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      });
    }
  };

  // Handle key press (Enter to send)
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Check if a message is from the current user
  const isCurrentUserMessage = (message: Message) => {
    if (currentUser) {
      return message.sender.id === currentUser.id;
    } else {
      // For non-authenticated users, check against the temp user ID
      const tempUserId = localStorage.getItem('temp-user-id');
      return message.sender.id === tempUserId || message.sender.guestId === tempUserId;
    }
  };

  return (
    <Layout>
      <div className="container py-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <Button
              variant="ghost"
              onClick={() => navigate(-1)}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>

            {channel && (
              <h1 className="text-2xl font-bold truncate">
                {channel.ownerName}
              </h1>
            )}
          </div>

          {channel && (
            <div className="border rounded-lg overflow-hidden bg-card">
              {/* Channel owner header */}
              <div className="p-4 border-b flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={channel.ownerAvatar} alt={channel.ownerName} />
                  <AvatarFallback className="bg-white text-black text-xl border border-black">{getInitials(channel.ownerName)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <h2 className="text-lg font-bold truncate">{channel.ownerName}</h2>
                  </div>
                  <p className="text-sm text-muted-foreground">Channel Owner</p>
                </div>
              </div>

              {/* Channel chat notice */}
              <div className="bg-muted/50 p-3 text-sm border-b">
                <p className="text-center">
                  <span className="font-medium">Chat with {channel.ownerName}</span>
                </p>
              </div>

              {/* Messages area */}
              <ScrollArea className="h-[calc(100vh-300px)] p-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <p className="mb-2">This is your private chat with <span className="font-semibold">{channel.ownerName}</span></p>
                      <p>No messages yet. Start the conversation!</p>
                    </div>
                  ) : (
                    messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${
                          isCurrentUserMessage(message) ? 'justify-end' : 'justify-start'
                        }`}
                      >
                        <div
                          className={`max-w-[80%] ${
                            isCurrentUserMessage(message)
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted'
                          } rounded-lg p-3`}
                        >
                          {!isCurrentUserMessage(message) ? (
                            <div className="flex items-center gap-2 mb-1">
                              <Avatar className="h-6 w-6">
                                <AvatarImage src={message.sender.avatar} alt={message.sender.username} />
                                <AvatarFallback>{getInitials(message.sender.username)}</AvatarFallback>
                              </Avatar>
                              <span className="text-xs font-medium">{message.sender.username}</span>
                            </div>
                          ) : (
                            <div className="flex justify-end mb-1">
                              <span className="text-xs font-medium text-primary-foreground">{message.sender.username}</span>
                            </div>
                          )}
                          <p>{message.content}</p>
                          <div
                            className={`text-xs mt-1 ${
                              isCurrentUserMessage(message)
                                ? 'text-primary-foreground/70'
                                : 'text-muted-foreground'
                            }`}
                          >
                            {formatDistanceToNow(new Date(message.timestamp), { addSuffix: true })}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Message input */}
              <div className="p-4 border-t">
                <div className="flex gap-2">
                  <Input
                    value={messageText}
                    onChange={(e) => setMessageText(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder={`Message to ${channel.ownerName}...`}
                    disabled={isLoading || !activeConversationId}
                    className="flex-1"
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={isLoading || !messageText.trim() || !activeConversationId}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default ChannelChatPage;
