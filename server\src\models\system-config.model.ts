import mongoose, { <PERSON><PERSON><PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * SystemConfig interface extending the base entity
 */
export interface ISystemConfig extends IBaseEntity {
  /** Maintenance mode settings */
  maintenance: {
    /** Whether maintenance mode is currently active */
    enabled: boolean;

    /** Message to display to users during maintenance */
    message: string;

    /** IP addresses that can still access the system during maintenance */
    allowedIPs: string[];

    /** When maintenance mode will start (for scheduled maintenance) */
    startTime?: Date;

    /** When maintenance mode will end (for scheduled maintenance) */
    endTime?: Date;
  };

  /** Localization settings */
  localization: {
    /** Default language for the system */
    defaultLanguage: string;

    /** Available languages */
    availableLanguages: string[];

    /** Date format */
    dateFormat: string;

    /** Time format */
    timeFormat: string;

    /** First day of the week (0 = Sunday, 1 = Monday, etc.) */
    firstDayOfWeek: number;

    /** Automatic translation settings */
    autoTranslation: {
      /** Whether automatic translation is enabled */
      enabled: boolean;

      /**
       * Translation service provider
       *
       * Possible values:
       * - google: Google Translate API
       * - azure: Azure Translator
       * - deepl: DeepL API
       * - openai: OpenAI API
       * - custom: Custom translation service
       */
      provider: 'google' | 'azure' | 'deepl' | 'openai' | 'custom';

      /** Provider-specific API settings */
      apiSettings: Record<string, any>;

      /** Minimum confidence score to accept automatic translations (0-100) */
      minimumConfidence: number;

      /** Whether to require human review for automatic translations */
      requireReview: boolean;

      /** Content types eligible for automatic translation */
      eligibleContentTypes: string[];
    };
  };

  /** Security settings */
  security: {
    /** Number of failed login attempts before account lockout */
    maxLoginAttempts: number;

    /** Session timeout in minutes */
    sessionTimeout: number;

    /** Password requirements */
    passwordPolicy: {
      /** Minimum number of characters required */
      minLength: number;

      /** Whether special characters are required */
      requireSpecialChars: boolean;

      /** Whether numbers are required */
      requireNumbers: boolean;

      /** Whether uppercase letters are required */
      requireUppercase: boolean;

      /** Number of days after which passwords expire */
      expiryDays: number;
    };

    /** CORS settings */
    cors: {
      /** Allowed origins */
      allowedOrigins: string[];

      /** Allowed methods */
      allowedMethods: string[];

      /** Allowed headers */
      allowedHeaders: string[];

      /** Whether credentials are allowed */
      allowCredentials: boolean;
    };

    /** Content Security Policy settings */
    contentSecurityPolicy: {
      /** Whether CSP is enabled */
      enabled: boolean;

      /** CSP directives */
      directives: Record<string, string[]>;
    };

    /** Rate limiting settings */
    rateLimit: {
      /** Whether rate limiting is enabled */
      enabled: boolean;

      /** Maximum number of requests per window */
      maxRequests: number;

      /** Time window in seconds */
      windowMs: number;

      /** Whether to skip rate limiting for trusted IPs */
      skipTrustedIPs: boolean;

      /** Trusted IP addresses */
      trustedIPs: string[];
    };
  };

  /** Email system configuration */
  email: {
    /** Map of template identifiers to template content */
    templates: Record<string, string>;

    /** Provider-specific email settings */
    settings: Record<string, any>;

    /** Notification email settings */
    notifications: {
      /** Types of notifications that can be sent */
      types: string[];

      /** Whether notifications are enabled by default */
      defaultEnabled: boolean;
    };

    /**
     * Email delivery service provider
     *
     * Possible values:
     * - smtp: Standard SMTP server
     * - sendgrid: SendGrid email service
     * - aws-ses: Amazon Simple Email Service
     * - mailchimp: Mailchimp/Mandrill
     * - postmark: Postmark App
     */
    provider: 'smtp' | 'sendgrid' | 'aws-ses' | 'mailchimp' | 'postmark';

    /** SMTP server configuration (when provider is 'smtp') */
    smtp: {
      /** SMTP server hostname */
      host: string;

      /** SMTP server port (typically 25, 465, or 587) */
      port: number;

      /** SMTP authentication username */
      username: string;

      /** SMTP authentication password */
      password: string;

      /**
       * Connection encryption type
       *
       * Possible values:
       * - tls: Transport Layer Security (typically port 587)
       * - ssl: Secure Sockets Layer (typically port 465)
       * - none: No encryption (typically port 25, not recommended)
       */
      encryption: 'tls' | 'ssl' | 'none';
    };
  };

  /** File storage configuration */
  storage: {
    /**
     * Storage provider
     *
     * Possible values:
     * - local: Local file system
     * - s3: Amazon S3
     * - gcs: Google Cloud Storage
     */
    provider: 'local' | 's3' | 'gcs';

    /** Provider-specific settings */
    settings: Record<string, any>;

    /** Storage limitations */
    limits: {
      /** Maximum file size in bytes */
      maxFileSize: number;

      /** Array of allowed MIME types */
      allowedTypes: string[];

      /** Total storage space allowed in bytes */
      totalSpace: number;
    };
  };

  /** Caching system configuration */
  cache: {
    /** Whether caching is enabled */
    enabled: boolean;

    /**
     * Cache provider
     *
     * Possible values:
     * - redis: Redis cache
     * - memcached: Memcached cache
     */
    provider: 'redis' | 'memcached';

    /** Default time-to-live in seconds */
    ttl: number;

    /** Provider-specific settings */
    settings: Record<string, any>;
  };

  /** Background job queue configuration */
  queue: {
    /**
     * Queue provider
     *
     * Possible values:
     * - redis: Redis-based queue
     * - rabbitmq: RabbitMQ message broker
     * - beanstalkd: Beanstalkd queue
     * - kafka: Apache Kafka
     * - azure: Azure Service Bus
     * - aws-sqs: Amazon Simple Queue Service
     * - google-pubsub: Google Cloud Pub/Sub
     * - bullmq: BullMQ (Redis-based)
     */
    provider: 'redis' | 'rabbitmq' | 'beanstalkd' | 'kafka' | 'azure' | 'aws-sqs' | 'google-pubsub' | 'bullmq';

    /** Provider-specific settings */
    settings: Record<string, any>;

    /** Job processing settings */
    jobs: {
      /** Number of jobs to process simultaneously */
      concurrency: number;

      /** Job timeout in seconds */
      timeout: number;

      /** Number of retry attempts for failed jobs */
      retries: number;
    };
  };

  /** System monitoring configuration */
  monitoring: {
    /** Whether monitoring is enabled */
    enabled: boolean;

    /**
     * Monitoring provider
     *
     * Possible values:
     * - prometheus: Prometheus monitoring system
     * - datadog: Datadog monitoring service
     */
    provider: 'prometheus' | 'datadog';

    /** Metrics to collect */
    metrics: string[];

    /** Alert configuration */
    alerting: {
      /** Whether alerting is enabled */
      enabled: boolean;

      /** Notification channels for sending alerts */
      channels: string[];

      /** Threshold values that trigger alerts */
      thresholds: Record<string, number>;
    };
  };
}

/**
 * SystemConfig schema definition
 */
const SystemConfigSchema = new Schema<ISystemConfig>(
  {
    maintenance: {
      enabled: {
        type: Boolean,
        default: false,
      },
      message: {
        type: String,
        default: 'The system is currently undergoing maintenance. Please try again later.',
      },
      allowedIPs: {
        type: [String],
        default: [],
      },
      startTime: {
        type: Date,
      },
      endTime: {
        type: Date,
      },
    },
    localization: {
      defaultLanguage: {
        type: String,
        default: 'en',
      },
      availableLanguages: {
        type: [String],
        default: ['en'],
      },
      dateFormat: {
        type: String,
        default: 'MM/DD/YYYY',
      },
      timeFormat: {
        type: String,
        default: 'HH:mm',
      },
      firstDayOfWeek: {
        type: Number,
        default: 0,
        min: 0,
        max: 6,
      },
      autoTranslation: {
        enabled: {
          type: Boolean,
          default: false,
        },
        provider: {
          type: String,
          enum: ['google', 'azure', 'deepl', 'openai', 'custom'],
          default: 'google',
        },
        apiSettings: {
          type: Schema.Types.Mixed,
          default: {},
        },
        minimumConfidence: {
          type: Number,
          default: 70,
          min: 0,
          max: 100,
        },
        requireReview: {
          type: Boolean,
          default: true,
        },
        eligibleContentTypes: {
          type: [String],
          default: ['video.title', 'video.description'],
        },
      },
    },
    security: {
      maxLoginAttempts: {
        type: Number,
        default: 5,
        min: 1,
      },
      sessionTimeout: {
        type: Number,
        default: 60,
        min: 1,
      },
      passwordPolicy: {
        minLength: {
          type: Number,
          default: 8,
          min: 6,
        },
        requireSpecialChars: {
          type: Boolean,
          default: true,
        },
        requireNumbers: {
          type: Boolean,
          default: true,
        },
        requireUppercase: {
          type: Boolean,
          default: true,
        },
        expiryDays: {
          type: Number,
          default: 90,
          min: 0,
        },
      },
      cors: {
        allowedOrigins: {
          type: [String],
          default: ['*'],
        },
        allowedMethods: {
          type: [String],
          default: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        },
        allowedHeaders: {
          type: [String],
          default: ['Content-Type', 'Authorization'],
        },
        allowCredentials: {
          type: Boolean,
          default: true,
        },
      },
      contentSecurityPolicy: {
        enabled: {
          type: Boolean,
          default: true,
        },
        directives: {
          type: Schema.Types.Mixed,
          default: {
            'default-src': ["'self'"],
            'script-src': ["'self'"],
            'style-src': ["'self'", "'unsafe-inline'"],
            'img-src': ["'self'", 'data:'],
            'connect-src': ["'self'"],
          },
        },
      },
      rateLimit: {
        enabled: {
          type: Boolean,
          default: true,
        },
        maxRequests: {
          type: Number,
          default: 100,
          min: 1,
        },
        windowMs: {
          type: Number,
          default: 60000,
          min: 1000,
        },
        skipTrustedIPs: {
          type: Boolean,
          default: true,
        },
        trustedIPs: {
          type: [String],
          default: ['127.0.0.1', '::1'],
        },
      },
    },
    email: {
      templates: {
        type: Schema.Types.Mixed,
        default: {},
      },
      settings: {
        type: Schema.Types.Mixed,
        default: {},
      },
      notifications: {
        types: {
          type: [String],
          default: ['account', 'security', 'marketing'],
        },
        defaultEnabled: {
          type: Boolean,
          default: true,
        },
      },
      provider: {
        type: String,
        enum: ['smtp', 'sendgrid', 'aws-ses', 'mailchimp', 'postmark'],
        default: 'smtp',
      },
      smtp: {
        host: {
          type: String,
          default: 'smtp.example.com',
        },
        port: {
          type: Number,
          default: 587,
        },
        username: {
          type: String,
          default: '',
        },
        password: {
          type: String,
          default: '',
        },
        encryption: {
          type: String,
          enum: ['tls', 'ssl', 'none'],
          default: 'tls',
        },
      },
    },
    storage: {
      provider: {
        type: String,
        enum: ['local', 's3', 'gcs'],
        default: 'local',
      },
      settings: {
        type: Schema.Types.Mixed,
        default: {},
      },
      limits: {
        maxFileSize: {
          type: Number,
          default: 104857600, // 100MB in bytes
        },
        allowedTypes: {
          type: [String],
          default: ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'],
        },
        totalSpace: {
          type: Number,
          default: 10737418240, // 10GB in bytes
        },
      },
    },
    cache: {
      enabled: {
        type: Boolean,
        default: true,
      },
      provider: {
        type: String,
        enum: ['redis', 'memcached'],
        default: 'redis',
      },
      ttl: {
        type: Number,
        default: 3600, // 1 hour in seconds
      },
      settings: {
        type: Schema.Types.Mixed,
        default: {},
      },
    },
    queue: {
      provider: {
        type: String,
        enum: ['redis', 'rabbitmq', 'beanstalkd', 'kafka', 'azure', 'aws-sqs', 'google-pubsub', 'bullmq'],
        default: 'redis',
      },
      settings: {
        type: Schema.Types.Mixed,
        default: {},
      },
      jobs: {
        concurrency: {
          type: Number,
          default: 5,
          min: 1,
        },
        timeout: {
          type: Number,
          default: 300, // 5 minutes in seconds
        },
        retries: {
          type: Number,
          default: 3,
          min: 0,
        },
      },
    },
    monitoring: {
      enabled: {
        type: Boolean,
        default: true,
      },
      provider: {
        type: String,
        enum: ['prometheus', 'datadog'],
        default: 'prometheus',
      },
      metrics: {
        type: [String],
        default: ['cpu', 'memory', 'requests', 'errors'],
      },
      alerting: {
        enabled: {
          type: Boolean,
          default: true,
        },
        channels: {
          type: [String],
          default: ['email'],
        },
        thresholds: {
          type: Schema.Types.Mixed,
          default: {
            cpu: 80,
            memory: 80,
            errorRate: 5,
          },
        },
      },
    },
  }
);

// Merge with base schema
SystemConfigSchema.add(BaseSchema);

// Create and export the SystemConfig model
const SystemConfigModel = mongoose.model<ISystemConfig>('SystemConfig', SystemConfigSchema);
export default SystemConfigModel;
