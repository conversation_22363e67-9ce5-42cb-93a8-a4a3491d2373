import React, { useState } from 'react';
import { useNavigate, Link, useParams } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Home, ChevronRight, User, Shield, Users, Edit } from 'lucide-react';

export default function UserPermissionsPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const { userId } = useParams();
  const [selectedPermission, setSelectedPermission] = useState('Normal');

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  const permissionOptions = [
    {
      id: 'normal',
      name: 'Normal',
      icon: <User className="h-8 w-8" />,
      active: selectedPermission === 'Normal'
    },
    {
      id: 'editor',
      name: 'Editor',
      icon: <Edit className="h-8 w-8" />,
      active: selectedPermission === 'Editor'
    },
    {
      id: 'moderator',
      name: 'Moderator',
      icon: <Users className="h-8 w-8" />,
      active: selectedPermission === 'Moderator'
    },
    {
      id: 'admin',
      name: 'Admin',
      icon: <Shield className="h-8 w-8" />,
      active: selectedPermission === 'Admin'
    }
  ];

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <div className="flex-1 bg-gray-100 p-6 overflow-y-auto">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 mb-4 text-sm">
            <Link to="/admin" className="flex items-center text-blue-600">
              <Home size={16} className="mr-1" />
              Home
            </Link>
            <ChevronRight size={14} className="text-gray-500" />
            <Link to="/admin/users" className="text-blue-600">
              Users
            </Link>
            <ChevronRight size={14} className="text-gray-500" />
            <span className="text-blue-600">Manage user permissions</span>
          </div>

          <div className="mb-6">
            <h1 className="text-2xl font-bold text-black">Manage user permissions</h1>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-bold text-black mb-6">User Permission</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {permissionOptions.map((permission) => (
                <div
                  key={permission.id}
                  className={`rounded-lg overflow-hidden cursor-pointer ${permission.active ? 'bg-blue-500' : 'bg-gray-100'}`}
                  onClick={() => setSelectedPermission(permission.name)}
                >
                  <div className="flex flex-col items-center justify-center p-6">
                    <div className={`h-16 w-16 rounded-full ${permission.active ? 'bg-blue-500' : 'bg-white'} flex items-center justify-center mb-3`}>
                      <span className={permission.active ? 'text-white' : 'text-blue-500'}>
                        {permission.icon}
                      </span>
                    </div>
                    <h3 className={`text-lg font-medium ${permission.active ? 'text-white' : 'text-black'}`}>{permission.name}</h3>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end mt-6">
              <button
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={() => navigate('/admin/users')}
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
