import { UserModel, RoleModel } from '../models';
import { connectDB } from '../config/database';
import mongoose from 'mongoose';

async function addAdminRole() {
  try {
    // Connect to the database
    await connectDB();
    
    // Find your user
    const user = await UserModel.findOne({ email: '<EMAIL>' });
    
    if (!user) {
      console.log('User not found');
      return;
    }
    
    console.log(`Found user: ${user.username} (${user.email})`);
    console.log(`Current roles: ${user.roles}`);
    
    // Find admin role
    const adminRole = await RoleModel.findOne({ code: 'admin' });
    
    if (!adminRole) {
      console.log('Admin role not found');
      return;
    }
    
    console.log(`Found admin role: ${adminRole.name} (${adminRole.id})`);
    
    // Update user roles to include admin role
    user.roles = [adminRole.id];
    await user.save();
    
    console.log(`Updated user roles: ${user.roles}`);
    console.log('User now has admin privileges');
    
    // Close the database connection
    await mongoose.connection.close();
    
  } catch (error) {
    console.error('Error adding admin role:', error);
    process.exit(1);
  }
}

// Run the function
addAdminRole().catch(console.error);
