import { FastifyInstance, FastifyPluginAsync } from 'fastify';
import fastifyPlugin from 'fastify-plugin';
import { UserModel, MessageModel, ConversationModel } from '../models';
import { messageService } from '../services/message.service';
import { conversationService } from '../services/conversation.service';
import { logger } from '../utils/logger';
import WebSocket from 'ws';

// Define types for WebSocket connection
interface WebSocketConnection {
  socket: WebSocket;
}

// Define JWT payload type
interface JwtPayload {
  id: string;
  email: string;
  roles?: string[];
}

// Store active connections
const activeConnections: Map<string, any> = new Map();

/**
 * WebSocket plugin for Fastify
 * Handles real-time messaging and user presence
 */
const websocketPlugin: FastifyPluginAsync = async (fastify: FastifyInstance) => {
  // Create WebSocket server
  const wss = new WebSocket.Server({ noServer: true });

  // Store active connections
  const activeConnections: Map<string, WebSocketConnection> = new Map();

  // Handle WebSocket connection
  wss.on('connection', (socket: WebSocket) => {
    let userId: string | null = null;
    let isAuthenticated = false;

    // Create connection object
    const connection: WebSocketConnection = { socket };

    // Handle messages
    socket.on('message', async (message: WebSocket.Data) => {
      try {
        const data = JSON.parse(message.toString());
        const { type, payload } = data;

        // Handle different message types
        switch (type) {
          case 'auth':
            // Authenticate user with JWT token
            try {
              const decoded = await fastify.jwt.verify(payload.token) as JwtPayload;
              userId = decoded.id;
              isAuthenticated = true;

              // Store connection
              if (userId) {
                activeConnections.set(userId, connection);
              }

              // Send authenticated event
              socket.send(
                JSON.stringify({
                  type: 'auth_success',
                  payload: {
                    userId,
                  },
                })
              );

              // Update user's online status
              if (userId) {
                await UserModel.updateOne(
                  { id: userId },
                  { 'presence.isOnline': true, 'presence.lastActiveAt': new Date() }
                );

                // Broadcast user online status to relevant users
                broadcastUserStatus(userId, true);
              }

              logger.info(`User ${userId} connected to WebSocket`);
            } catch (error: any) {
              // Authentication failed
              socket.send(
                JSON.stringify({
                  type: 'auth_error',
                  payload: {
                    message: 'Authentication failed',
                  },
                })
              );
              logger.error(`WebSocket authentication failed: ${error.message}`);
            }
            break;

            case 'message':
              // Handle new message
              if (!isAuthenticated || !userId) {
                socket.send(
                  JSON.stringify({
                    type: 'error',
                    payload: {
                      message: 'Not authenticated',
                    },
                  })
                );
                return;
              }

              try {
                // Get conversation to determine sender type
                const conversation = await conversationService.getConversationById(
                  payload.conversationId,
                  userId
                );

                const senderType = conversation.creatorId === userId ? 'creator' : 'user';

                // Create message based on content type
                let message;
                if (payload.contentType === 'text') {
                  message = await messageService.sendTextMessage({
                    conversationId: payload.conversationId,
                    senderId: userId,
                    senderType,
                    content: payload.content,
                    clientId: payload.clientId,
                    metadata: payload.metadata,
                  });
                } else {
                  message = await messageService.sendMediaMessage({
                    conversationId: payload.conversationId,
                    senderId: userId,
                    senderType,
                    contentType: payload.contentType,
                    mediaUrl: payload.mediaUrl,
                    mediaMimeType: payload.mediaMimeType,
                    content: payload.content,
                    clientId: payload.clientId,
                    metadata: payload.metadata,
                  });
                }

                // Get sender details
                const sender = await UserModel.findOne(
                  { id: userId, deletedAt: null },
                  'id username displayName avatar'
                ).lean();

                // Prepare message for sending
                const messageWithSender = {
                  ...message.toObject(),
                  sender,
                };

                // Send message to sender for confirmation
                socket.send(
                  JSON.stringify({
                    type: 'message_sent',
                    payload: {
                      message: messageWithSender,
                      clientId: payload.clientId,
                    },
                  })
                );

                // Send message to recipient if online
                const recipientId =
                  senderType === 'user' ? conversation.creatorId : conversation.userId;
                const recipientConnection = activeConnections.get(recipientId);

                if (recipientConnection) {
                  try {
                    recipientConnection.socket.send(
                      JSON.stringify({
                        type: 'new_message',
                        payload: {
                          message: messageWithSender,
                        },
                      })
                    );
                    logger.info(`Message delivered to recipient ${recipientId} in real-time`);
                  } catch (error: any) {
                    logger.error(`Failed to deliver message to recipient ${recipientId} in real-time: ${error?.message || 'Unknown error'}`);
                  }
                } else {
                  logger.info(`Recipient ${recipientId} is offline, message will be delivered when they connect`);
                }

                logger.info(
                  `Message sent from ${userId} to ${recipientId} in conversation ${payload.conversationId}`
                );
              } catch (error: any) {
                socket.send(
                  JSON.stringify({
                    type: 'error',
                    payload: {
                      message: 'Failed to send message',
                      error: error.message,
                    },
                  })
                );
                logger.error(`Error sending message: ${error.message}`);
              }
              break;

            case 'typing':
              // Handle typing indicator
              if (!isAuthenticated || !userId) {
                return;
              }

              try {
                const { conversationId, isTyping } = payload;

                // Get conversation
                const conversation = await ConversationModel.findOne({
                  id: conversationId,
                  deletedAt: null,
                  $or: [{ userId }, { creatorId: userId }],
                });

                if (!conversation) {
                  return;
                }

                // Determine recipient
                const recipientId =
                  conversation.userId === userId ? conversation.creatorId : conversation.userId;
                const recipientConnection = activeConnections.get(recipientId);

                // Send typing indicator to recipient if online
                if (recipientConnection) {
                  recipientConnection.socket.send(
                    JSON.stringify({
                      type: 'typing_indicator',
                      payload: {
                        conversationId,
                        userId,
                        isTyping,
                      },
                    })
                  );
                }
              } catch (error: any) {
                logger.error(`Error handling typing indicator: ${error.message}`);
              }
              break;

            case 'read_receipt':
              // Handle read receipt
              if (!isAuthenticated || !userId) {
                return;
              }

              try {
                const { messageId } = payload;

                // Mark message as read
                await messageService.markMessageAsRead(messageId, userId);

                // Get message
                const message = await MessageModel.findOne({
                  id: messageId,
                  deletedAt: null,
                });

                if (!message) {
                  return;
                }

                // Get conversation
                const conversation = await ConversationModel.findOne({
                  id: message.conversationId,
                  deletedAt: null,
                });

                if (!conversation) {
                  return;
                }

                // Determine sender
                const senderId = message.senderId;
                const senderConnection = activeConnections.get(senderId);

                // Send read receipt to sender if online
                if (senderConnection) {
                  senderConnection.socket.send(
                    JSON.stringify({
                      type: 'read_receipt',
                      payload: {
                        messageId,
                        conversationId: message.conversationId,
                        readBy: userId,
                        readAt: new Date(),
                      },
                    })
                  );
                }
              } catch (error: any) {
                logger.error(`Error handling read receipt: ${error.message}`);
              }
              break;

            case 'ping':
              // Handle ping (keep-alive)
              socket.send(
                JSON.stringify({
                  type: 'pong',
                  payload: {
                    timestamp: new Date(),
                  },
                })
              );
              break;

            default:
              // Unknown message type
              socket.send(
                JSON.stringify({
                  type: 'error',
                  payload: {
                    message: `Unknown message type: ${type}`,
                  },
                })
              );
          }
        } catch (error: any) {
          // Error parsing message
          socket.send(
            JSON.stringify({
              type: 'error',
              payload: {
                message: 'Invalid message format',
              },
            })
          );
          logger.error(`WebSocket error: ${error.message}`);
        }
      });

      // Handle disconnection
      socket.on('close', async () => {
        if (userId) {
          // Remove connection
          activeConnections.delete(userId);

          // Update user's online status
          await UserModel.updateOne(
            { id: userId },
            { 'presence.isOnline': false, 'presence.lastActiveAt': new Date() }
          );

          // Broadcast user offline status to relevant users
          broadcastUserStatus(userId, false);

          logger.info(`User ${userId} disconnected from WebSocket`);
        }
      });
    });

    // Set up HTTP server upgrade handler
    fastify.server.on('upgrade', (request, socket, head) => {
      wss.handleUpgrade(request, socket, head, (ws) => {
        wss.emit('connection', ws, request);
      });
    });

  // Helper function to broadcast user status changes
  async function broadcastUserStatus(userId: string, isOnline: boolean) {
    try {
      // Find conversations where this user is a participant
      const conversations = await ConversationModel.find({
        $or: [{ userId }, { creatorId: userId }],
        status: 'active',
        deletedAt: null,
      });

      // Get unique participant IDs
      const participantIds = new Set<string>();
      conversations.forEach((conversation) => {
        if (conversation.userId !== userId) {
          participantIds.add(conversation.userId);
        }
        if (conversation.creatorId !== userId) {
          participantIds.add(conversation.creatorId);
        }
      });

      // Broadcast status to all participants
      participantIds.forEach((participantId) => {
        const connection = activeConnections.get(participantId);
        if (connection) {
          connection.socket.send(
            JSON.stringify({
              type: 'user_status',
              payload: {
                userId,
                isOnline,
                lastActiveAt: new Date(),
              },
            })
          );
        }
      });
    } catch (error: any) {
      logger.error(`Error broadcasting user status: ${error.message}`);
    }
  }

  // Create a route for WebSocket health check
  fastify.get('/api/v1/ws/health', async (request, reply) => {
    return { status: 'ok', connections: wss.clients.size };
  });
};

export default fastifyPlugin(websocketPlugin);
