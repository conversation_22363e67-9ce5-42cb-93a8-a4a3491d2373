import { createClient, RedisClientType } from 'redis';
import { config } from 'dotenv';

// Load environment variables
config();

// Redis connection options
const redisOptions = {
  socket: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT ? parseInt(process.env.REDIS_PORT, 10) : 6379,
  },
  password: process.env.REDIS_PASSWORD || undefined,
};

// Create Redis client
let redisClient: RedisClientType;

// Connect to Redis
export const connectRedis = async (): Promise<RedisClientType> => {
  try {
    if (!redisClient) {
      redisClient = createClient(redisOptions);
      
      // Handle Redis events
      redisClient.on('error', (err) => {
        console.error(`Redis error: ${err}`);
      });
      
      redisClient.on('connect', () => {
        console.log('Redis connected successfully');
      });
      
      redisClient.on('reconnecting', () => {
        console.warn('Redis reconnecting...');
      });
      
      redisClient.on('end', () => {
        console.warn('Redis connection closed');
      });
      
      // Connect to Redis
      await redisClient.connect();
    }
    
    return redisClient;
  } catch (error) {
    console.error(`Error connecting to Redis: ${error}`);
    throw error;
  }
};

// Get Redis client
export const getRedisClient = (): RedisClientType => {
  if (!redisClient) {
    throw new Error('Redis client not initialized');
  }
  return redisClient;
};

// Close Redis connection
export const closeRedis = async (): Promise<void> => {
  if (redisClient) {
    await redisClient.quit();
    console.log('Redis connection closed');
  }
};

export default {
  connectRedis,
  getRedisClient,
  closeRedis,
};
