import { FastifyInstance, FastifyPluginOptions, FastifyRequest, FastifyReply } from 'fastify';
import { Type } from '@sinclair/typebox';
import { aiAssistantController } from '../controllers/ai-assistant.controller';
import { optionalAuthenticate } from '../middleware/auth';

/**
 * AI Assistant routes
 */
export default async function aiAssistantRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Send message to AI Assistant - no authentication required
  fastify.post<{
    Body: {
      message: string;
      provider: string;
      apiKey: string;
      endpoint?: string;
      model?: string;
      conversationId?: string;
      creatorId: string;
      creatorName?: string;
      videoId?: string;
      chatHistory?: Array<{
        role: 'user' | 'assistant';
        content: string;
      }>;
    };
  }>('/message', {
    preHandler: optionalAuthenticate, // Use optional authentication instead of required authentication
    // Log request details for debugging
    onRequest: async (request, reply) => {
      console.log('AI Assistant message request received');
      console.log('Headers:', request.headers);
      // The body might not be parsed yet at this point, so we'll just log that the request was received
      console.log('Request received, body will be parsed in the handler');
    },
    schema: {
      tags: ['AI Assistant'],
      description: 'Send a message to the AI Assistant',
      body: Type.Object({
        message: Type.String(),
        provider: Type.Enum({ openai: 'openai', deepseek: 'deepseek', bhashini: 'bhashini', custom: 'custom', openrouter: 'openrouter' }),
        apiKey: Type.String(),
        endpoint: Type.Optional(Type.String()),
        model: Type.Optional(Type.String()), // Added model parameter for providers that support multiple models
        conversationId: Type.Optional(Type.String()),
        creatorId: Type.String(),
        creatorName: Type.Optional(Type.String()), // Added creator name for personalized responses
        videoId: Type.Optional(Type.String()),
        chatHistory: Type.Optional(Type.Array(Type.Object({
          role: Type.Enum({ user: 'user', assistant: 'assistant' }),
          content: Type.String()
        })))
      }),
      response: {
        200: Type.Object({
          success: Type.Boolean(),
          data: Type.Object({
            response: Type.Optional(Type.String()),
            content: Type.Optional(Type.String()),
            conversationId: Type.String(),
            timestamp: Type.String({ format: 'date-time' })
          })
        }),
        400: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          errorType: Type.Optional(Type.String())
        }),
        401: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          errorType: Type.Optional(Type.String())
        }),
        403: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          errorType: Type.Optional(Type.String())
        }),
        429: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          errorType: Type.Optional(Type.String())
        }),
        500: Type.Object({
          success: Type.Boolean(),
          message: Type.String(),
          errorDetails: Type.Optional(Type.String())
        })
      }
    }
  }, aiAssistantController.sendMessage);
}
