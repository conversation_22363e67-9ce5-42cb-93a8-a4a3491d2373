import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, Plus, Edit, Trash2, Search, ExternalLink, MoreHorizontal } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { useTheme } from '@/context/ThemeContext';

interface Tenant {
  id: string;
  name: string;
  domain: string;
  status: 'active' | 'inactive' | 'pending';
  users: number;
  createdAt: string;
}

export default function TenantManagementPage() {
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTenants, setSelectedTenants] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // Sample data for tenants
  const tenants: Tenant[] = [
    {
      id: '1',
      name: 'LegalAid Corp',
      domain: 'legalaid.example.com',
      status: 'active',
      users: 245,
      createdAt: '2023-10-15'
    },
    {
      id: '2',
      name: 'Smith & Associates',
      domain: 'smithlaw.example.com',
      status: 'active',
      users: 128,
      createdAt: '2023-11-22'
    },
    {
      id: '3',
      name: 'Johnson Legal Services',
      domain: 'johnsonlegal.example.com',
      status: 'pending',
      users: 56,
      createdAt: '2024-01-05'
    },
    {
      id: '4',
      name: 'Pacific Law Group',
      domain: 'pacificlaw.example.com',
      status: 'inactive',
      users: 89,
      createdAt: '2023-08-30'
    }
  ];

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedTenants([]);
    } else {
      setSelectedTenants(tenants.map(tenant => tenant.id));
    }
    setSelectAll(!selectAll);
  };

  const handleSelectTenant = (id: string) => {
    if (selectedTenants.includes(id)) {
      setSelectedTenants(selectedTenants.filter(tenantId => tenantId !== id));
      setSelectAll(false);
    } else {
      setSelectedTenants([...selectedTenants, id]);
      if (selectedTenants.length + 1 === tenants.length) {
        setSelectAll(true);
      }
    }
  };

  const getStatusBadgeClass = (status: 'active' | 'inactive' | 'pending') => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredTenants = tenants.filter(tenant => 
    tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tenant.domain.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex h-screen bg-[#f5f7fb] overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl font-bold mb-4">Tenant Management</h1>
            
            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-gray-600 hover:text-gray-900 flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-gray-500">&gt;</span>
              <span className="text-[#38bdf8]">Tenant Management</span>
            </div>

            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-bold">Manage Tenants</h2>
                  <Link 
                    to="/admin/tenants/add" 
                    className="bg-[#38bdf8] text-white rounded-md px-4 py-2 flex items-center hover:bg-blue-600 transition-colors"
                  >
                    <Plus size={16} className="mr-2" />
                    Add New Tenant
                  </Link>
                </div>
                
                <div className="mb-6">
                  <div className="flex">
                    <div className="flex-1 relative">
                      <input
                        type="text"
                        placeholder="Search tenants..."
                        className="w-full border border-gray-300 rounded-l-md px-4 py-2 pl-10"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <Search className="absolute left-3 top-2.5 text-gray-400" size={18} />
                    </div>
                    <button className="bg-[#38bdf8] text-white rounded-r-md px-6 py-2 hover:bg-blue-500 transition-colors">
                      Search
                    </button>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full border-collapse">
                    <thead>
                      <tr className="bg-gray-100 border-b border-gray-200">
                        <th className="p-3 text-left w-10">
                          <input 
                            type="checkbox" 
                            className="h-4 w-4"
                            checked={selectAll}
                            onChange={handleSelectAll}
                          />
                        </th>
                        <th className="p-3 text-left">Tenant Name</th>
                        <th className="p-3 text-left">Domain</th>
                        <th className="p-3 text-left">Status</th>
                        <th className="p-3 text-left">Users</th>
                        <th className="p-3 text-left">Created</th>
                        <th className="p-3 text-left">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredTenants.map((tenant) => (
                        <tr key={tenant.id} className="border-b border-gray-200 hover:bg-gray-50">
                          <td className="p-3">
                            <input 
                              type="checkbox" 
                              className="h-4 w-4"
                              checked={selectedTenants.includes(tenant.id)}
                              onChange={() => handleSelectTenant(tenant.id)}
                            />
                          </td>
                          <td className="p-3 font-medium">{tenant.name}</td>
                          <td className="p-3">
                            <div className="flex items-center">
                              {tenant.domain}
                              <a href={`https://${tenant.domain}`} target="_blank" rel="noopener noreferrer" className="ml-2 text-blue-500 hover:text-blue-700">
                                <ExternalLink size={14} />
                              </a>
                            </div>
                          </td>
                          <td className="p-3">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(tenant.status)}`}>
                              {tenant.status.charAt(0).toUpperCase() + tenant.status.slice(1)}
                            </span>
                          </td>
                          <td className="p-3">{tenant.users}</td>
                          <td className="p-3">{new Date(tenant.createdAt).toLocaleDateString()}</td>
                          <td className="p-3">
                            <div className="flex space-x-2">
                              <Link to={`/admin/tenants/${tenant.id}/edit`} className="text-blue-500 hover:text-blue-700">
                                <Edit size={16} />
                              </Link>
                              <Link to={`/admin/tenants/${tenant.id}/configure`} className="text-green-500 hover:text-green-700">
                                <MoreHorizontal size={16} />
                              </Link>
                              <button className="text-red-500 hover:text-red-700">
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-600">Showing {filteredTenants.length} out of {tenants.length} tenants</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="border border-gray-300 rounded-md px-3 py-1 text-gray-600">
                      ⟨
                    </button>
                    <button className="bg-[#38bdf8] text-white rounded-md px-3 py-1">
                      1
                    </button>
                    <button className="border border-gray-300 rounded-md px-3 py-1 text-gray-600">
                      ⟩
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
