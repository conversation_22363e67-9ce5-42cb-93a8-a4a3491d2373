import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ExternalLink } from "lucide-react";
import { useChatbot } from '@/context/ChatbotContext';

/**
 * OpenRouterSetupGuide component
 * 
 * This component provides a guide for users to set up their OpenRouter API key
 * when they encounter authentication errors.
 */
export function OpenRouterSetupGuide() {
  const { updateAIProvider } = useChatbot();

  const handleOpenRouterSignup = () => {
    window.open('https://openrouter.ai/keys', '_blank');
  };

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle>OpenRouter API Authentication Error</CardTitle>
        <CardDescription>
          We detected an issue with your OpenRouter API key. Follow these steps to fix it:
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">What happened?</h3>
          <p>
            The application tried to connect to OpenRouter AI services but received an authentication error.
            This means your API key is either missing, invalid, or has expired.
          </p>
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-semibold">How to fix it:</h3>
          <ol className="list-decimal pl-5 space-y-2">
            <li>
              <span className="font-medium">Get an OpenRouter API key</span>: Visit{" "}
              <a 
                href="https://openrouter.ai/keys" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                openrouter.ai/keys
              </a>{" "}
              to create an account and generate an API key.
            </li>
            <li>
              <span className="font-medium">Copy your API key</span>: After generating a key, copy it to your clipboard.
            </li>
            <li>
              <span className="font-medium">Update your settings</span>: Go to Settings → AI Providers → OpenRouter and paste your API key.
            </li>
            <li>
              <span className="font-medium">Test the connection</span>: Use the "Test API Key" button to verify your key works.
            </li>
          </ol>
        </div>

        <div className="bg-amber-50 p-4 rounded-md border border-amber-200">
          <h3 className="text-amber-800 font-semibold">Important Note</h3>
          <p className="text-amber-700">
            OpenRouter API keys start with "sk-or-" and should be kept private. Never share your API key with others.
          </p>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={() => window.history.back()}>
          Go Back
        </Button>
        <Button onClick={handleOpenRouterSignup}>
          Get OpenRouter API Key <ExternalLink className="ml-2 h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
}

export default OpenRouterSetupGuide;
