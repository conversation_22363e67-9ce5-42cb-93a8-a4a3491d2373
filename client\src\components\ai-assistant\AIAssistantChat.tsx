import { useState, useEffect, useRef } from 'react';
import { MessageCircle, X, Send, Bot } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext';
import { useChatbot } from '@/context/ChatbotContext';
import { useToast } from '@/hooks/use-toast';
import { User, Video } from '@/types';
import aiAssistantAPI from '@/services/aiAssistantApi';

// Renamed to CreatorChat but keeping the file name for compatibility

interface CreatorChatProps {
  isOpen?: boolean;
  onClose?: () => void;
  creator: User;
  video?: Video;
}

export default function CreatorChat({ isOpen = false, onClose, creator, video }: CreatorChatProps) {
  const { currentUser } = useAuth();
  const {
    isAIAssistantEnabled,
    aiAssistantProvider,
    aiAssistantApiKey,
    aiAssistantEndpoint,
    aiProviders,
    getEnabledProviders
  } = useChatbot();

  // Get the first enabled provider or fall back to the legacy provider
  const enabledProviders = getEnabledProviders();
  // Prioritize OpenRouter if it's enabled
  const openRouterEnabled = aiProviders.openrouter.enabled;
  const activeProvider = openRouterEnabled ? 'openrouter' :
                         (enabledProviders.length > 0 ? enabledProviders[0] : aiAssistantProvider);
  const providerConfig = aiProviders[activeProvider];
  const { toast } = useToast();
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [conversationId, setConversationId] = useState<string | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Get the best name to display for the creator
  const creatorDisplayName = creator.displayName || creator.username || 'Video Creator';

  const [chatHistory, setChatHistory] = useState<Array<{
    role: 'user' | 'assistant';
    content: string;
    timestamp: string;
  }>>([
    {
      role: 'assistant',
      content: `Hello! Thanks for reaching out. How can I help you with questions about ${video?.title || 'this content'}?`,
      timestamp: new Date().toISOString()
    }
  ]);

  // Update expanded state when isOpen prop changes
  useEffect(() => {
    setIsExpanded(isOpen);
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  // Scroll to bottom when chat history changes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory]);

  const handleClose = () => {
    setIsExpanded(false);
    if (onClose) onClose();
  };

  const handleSendMessage = async () => {
    if (!message.trim() || isSending) return;

    // Add user message to chat
    const userMessage = {
      role: 'user' as const,
      content: message,
      timestamp: new Date().toISOString()
    };

    setChatHistory(prev => [...prev, userMessage]);
    setMessage('');
    setIsSending(true);

    try {
      // Format chat history for API
      const formattedHistory = chatHistory.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Send message to AI Assistant using the active provider
      console.log(`Sending message using provider: ${activeProvider}`);
      console.log(`API Key (masked): ${providerConfig.apiKey ? `${providerConfig.apiKey.substring(0, 5)}...` : 'none'}`);
      console.log(`Model: ${providerConfig.model || 'default'}`);
      console.log(`Creator ID: ${creator.id}`);
      console.log(`Creator Name: ${creatorDisplayName}`);
      console.log(`Video ID: ${video?.id || 'none'}`);
      console.log(`Chat history length: ${formattedHistory.length} messages`);
      console.log(`Message content: ${message}`);

      const response = await aiAssistantAPI.sendMessage({
        message: message,
        provider: activeProvider,
        apiKey: providerConfig.apiKey || aiAssistantApiKey, // Fall back to legacy key if needed
        endpoint: activeProvider === 'custom' ? (providerConfig.endpoint || aiAssistantEndpoint) : undefined,
        model: providerConfig.model, // Pass the model for providers that support multiple models
        conversationId: conversationId || undefined,
        creatorId: creator.id,
        creatorName: creatorDisplayName, // Pass the creator's display name
        videoId: video?.id,
        chatHistory: formattedHistory
      });

      if (response.success) {
        // Add assistant response to chat
        // Handle different response formats
        const responseContent = response.data.response || response.data.content || "I'm here to help!";
        const responseTimestamp = response.data.timestamp || new Date().toISOString();

        console.log('Response data:', response.data);

        setChatHistory(prev => [
          ...prev,
          {
            role: 'assistant',
            content: responseContent,
            timestamp: responseTimestamp
          }
        ]);

        // Save conversation ID for future messages
        if (!conversationId && response.data.conversationId) {
          setConversationId(response.data.conversationId);
        }
      } else {
        // Show error message
        toast({
          title: "Error",
          description: "Failed to send message. Please try again.",
          variant: "destructive"
        });

        // Add error message to chat
        setChatHistory(prev => [
          ...prev,
          {
            role: 'assistant',
            content: "I'm sorry, I couldn't process your request. Please try again later.",
            timestamp: new Date().toISOString()
          }
        ]);
      }
    } catch (error) {
      console.error('Error sending message to AI Assistant:', error);

      // Get a more specific error message if available
      let errorMessage = "An error occurred while sending your message. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      // Show error message
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      });

      // Add error message to chat with a more helpful response
      setChatHistory(prev => [
        ...prev,
        {
          role: 'assistant',
          content: "I'm sorry, I'm having trouble connecting to my AI service right now. This could be due to network issues or API configuration. Please check your internet connection and try again in a moment.",
          timestamp: new Date().toISOString()
        }
      ]);

      // Log additional diagnostic information
      console.log('Active provider:', activeProvider);
      console.log('Provider config:', {
        ...providerConfig,
        apiKey: providerConfig.apiKey ? '***masked***' : 'none'
      });
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Collapsed state (chat bubble)
  if (!isExpanded) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          className="h-14 w-14 rounded-full bg-orange-500 shadow-lg hover:bg-orange-600 text-white"
          onClick={() => setIsExpanded(true)}
        >
          <MessageCircle className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  // Expanded state (chat window)
  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className="w-80 h-96 shadow-xl flex flex-col">
        <CardHeader className="p-3 border-b flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={creator.avatar} alt={creator.username} />
              <AvatarFallback>{creator.username[0]}</AvatarFallback>
            </Avatar>
            <div>
              <span className="font-medium">{creatorDisplayName}</span>
              <div className="text-xs text-muted-foreground">
                <span>Online</span>
              </div>
            </div>
          </div>
          <Button variant="ghost" size="icon" onClick={handleClose}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>

        <CardContent className="flex-1 overflow-y-auto p-3 space-y-4">
          {chatHistory.map((msg, index) => (
            <div
              key={index}
              className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-2 rounded-lg ${
                  msg.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                {msg.content}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </CardContent>

        <CardFooter className="p-3 border-t">
          <div className="flex w-full gap-2">
            <Input
              ref={inputRef}
              placeholder="Type a message..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              disabled={isSending}
              className="flex-1"
            />
            <Button
              size="icon"
              onClick={handleSendMessage}
              disabled={!message.trim() || isSending}
              className="bg-orange-500 hover:bg-orange-600 text-white"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
