import axios from 'axios';

/**
 * Bhashini Direct API Service
 *
 * This service provides direct integration with the Bhashini API for translation
 * based on the provided Python implementation.
 *
 * Bhashini is an AI-based translation service developed by the Government of India
 * that specializes in Indian languages.
 */

// API endpoints and credentials - Using both the Python code values and the server values
// Primary endpoint from Python code
const PYTHON_INFERENCE_URL = "https://dhruva-api.bhashini.gov.in/services/inference/pipeline";
// Fallback endpoint from server implementation
const SERVER_INFERENCE_URL = "https://bhashini.gov.in/api/v1/inference/translation";
// Use the Python endpoint first, then fall back to server endpoint if needed
const INFERENCE_URL = PYTHON_INFERENCE_URL;
const DEFAULT_USER_ID = "cee60134c6bb4d179efd3fda48ff32fe";
const DEFAULT_API_KEY = "13a647c84b-2747-4f0c-afcd-2ac8235f5318";
const DEFAULT_AUTHORIZATION = "W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun";

// Service and model IDs from the pipeline configuration
const ASR_SERVICE_ID = "ai4bharat/conformer-hi-gpu--t4";
const ASR_MODEL_ID = "648025f27cdd753e77f461a9";
const NMT_SERVICE_ID = "ai4bharat/indictrans-v2-all-gpu--t4";
const NMT_MODEL_ID = "641d1d7892a6a31751ff1f5a";
const TTS_SERVICE_ID = "ai4bharat/indic-tts-coqui-indo_aryan-gpu--t4";
const TTS_MODEL_ID = "636e60ef86369150cb00432b";

// Supported languages with their names
export const SUPPORTED_LANGUAGES: Record<string, string> = {
  "hi": "हिंदी (Hindi)",
  "en": "अंग्रेज़ी (English)",
  "gu": "गुजराती (Gujarati)",
  "bn": "बंगाली (Bengali)",
  "ta": "तमिल (Tamil)",
  "te": "तेलुगु (Telugu)",
  "mr": "मराठी (Marathi)",
  "kn": "कन्नड़ (Kannada)",
  "ml": "मलयालम (Malayalam)",
  "pa": "पंजाबी (Punjabi)",
  "or": "उड़िया (Odia)",
  "as": "असमिया (Assamese)",
  "ur": "उर्दू (Urdu)"
};

// Script code mapping for Indian languages
export function getScriptCode(languageCode: string): string {
  const scriptMapping: Record<string, string> = {
    "hi": "Deva",  // Hindi - Devanagari
    "en": "Latn",  // English - Latin
    "gu": "Gujr",  // Gujarati
    "bn": "Beng",  // Bengali
    "ta": "Taml",  // Tamil
    "te": "Telu",  // Telugu
    "mr": "Deva",  // Marathi - Devanagari
    "kn": "Knda",  // Kannada
    "ml": "Mlym",  // Malayalam
    "pa": "Guru",  // Punjabi - Gurmukhi
    "or": "Orya",  // Odia
    "as": "Beng",  // Assamese - Bengali
    "ur": "Arab",  // Urdu - Arabic
  };
  return scriptMapping[languageCode] || "Deva";  // Default to Devanagari if not found
}

/**
 * Interface for translation options
 */
export interface TranslationOptions {
  userId?: string;
  apiKey?: string;
  authorization?: string;
}

/**
 * Translate text using Bhashini API
 * @param text Text to translate
 * @param sourceLang Source language code
 * @param targetLang Target language code
 * @param options Optional configuration
 * @returns Translated text
 */
export async function translateText(
  text: string,
  sourceLang: string = "hi",
  targetLang: string = "en",
  options: TranslationOptions = {}
): Promise<string> {
  // Use default or provided credentials
  const userId = options.userId || DEFAULT_USER_ID;
  const apiKey = options.apiKey || DEFAULT_API_KEY;
  const authorization = options.authorization || DEFAULT_AUTHORIZATION;

  // Headers for the API request
  const headers = {
    "Content-Type": "application/json",
    "userID": userId,
    "ulcaApiKey": apiKey,
    "Authorization": authorization
  };

  // Payload for the translation request based on the Python implementation
  const payload = {
    "pipelineTasks": [
      {
        "taskType": "translation",
        "config": {
          "language": {
            "sourceLanguage": sourceLang,
            "targetLanguage": targetLang,
            "sourceScriptCode": getScriptCode(sourceLang),
            "targetScriptCode": getScriptCode(targetLang)
          },
          "serviceId": NMT_SERVICE_ID,
          "modelId": NMT_MODEL_ID
        }
      }
    ],
    "inputData": {
      "input": [
        {
          "source": text
        }
      ]
    }
  };

  console.log("Sending request to Bhashini API with payload:", JSON.stringify(payload, null, 2));
  console.log("Headers:", JSON.stringify(headers, null, 2));

  try {
    // First try the Python-style endpoint
    try {
      console.log("Trying Python-style Bhashini API endpoint...");
      const response = await axios.post(
        PYTHON_INFERENCE_URL,
        payload,
        { headers }
      );

      console.log("Python Bhashini API Status Code:", response.status);
      console.log("Python Bhashini API Response:", JSON.stringify(response.data, null, 2));

      // Process the response
      if (response.status === 200) {
        // Extract the translated text from the response using Python-style path
        const outputText = response.data?.pipelineResponse?.[0]?.output?.[0]?.target;
        if (outputText) {
          console.log("Successfully translated using Python-style endpoint");
          return outputText;
        }
      }
      console.log("No translation found in Python-style response, trying server endpoint...");
    } catch (pythonError) {
      console.error(`Python-style Bhashini API failed: ${pythonError.message}`);
      console.log("Falling back to server-style endpoint...");
    }

    // If Python-style endpoint fails, try the server-style endpoint
    // Create server-style payload
    const serverPayload = {
      userId: userId,
      ulcaApiKey: apiKey,
      sourceLanguage: sourceLang,
      targetLanguage: targetLang,
      domain: "general",
      text: text
    };

    console.log("Trying server-style Bhashini API endpoint...");
    console.log("Server payload:", JSON.stringify(serverPayload, null, 2));

    const serverResponse = await axios.post(
      SERVER_INFERENCE_URL,
      serverPayload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    console.log("Server Bhashini API Status Code:", serverResponse.status);
    console.log("Server Bhashini API Response:", JSON.stringify(serverResponse.data, null, 2));

    // Process the server response
    if (serverResponse.status === 200) {
      // Extract the translated text from the server response
      const translatedText = serverResponse.data?.translatedText || serverResponse.data?.text;
      if (translatedText) {
        console.log("Successfully translated using server-style endpoint");
        return translatedText;
      }
      throw new Error("No translation found in server response");
    } else {
      return `Error: ${serverResponse.status}, ${JSON.stringify(serverResponse.data)}`;
    }
  } catch (e: any) {
    console.error(`Exception occurred with Bhashini API: ${e.message}`);
    if (e.response) {
      console.error("Response data:", e.response.data);
      console.error("Response status:", e.response.status);
    }
    return `Error: ${e.message}`;
  }
}

export default {
  translateText,
  SUPPORTED_LANGUAGES,
  getScriptCode
};
