import React, { useState } from 'react';
import { useTheme } from '@/context/ThemeContext';
import { Circle } from 'lucide-react';

interface UsersChartProps {
  timeRange?: 'Today' | 'Yesterday' | 'This Week' | 'This Month' | 'Last Month' | 'This Year';
}

export default function UsersChart({ timeRange = 'This Year' }: UsersChartProps) {
  const { theme } = useTheme();
  const [hoveredMonth, setHoveredMonth] = useState<number | null>(null);

  // Define different data sets based on time range
  const getChartData = () => {
    switch (timeRange) {
      case 'Today':
        return {
          labels: ['12am', '4am', '8am', '12pm', '4pm', '8pm'],
          data: [2, 1, 5, 8, 3, 1],
          maxY: 10
        };
      case 'Yesterday':
        return {
          labels: ['12am', '4am', '8am', '12pm', '4pm', '8pm'],
          data: [1, 0, 3, 6, 2, 0],
          maxY: 10
        };
      case 'This Week':
        return {
          labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          data: [8, 12, 5, 7, 10, 6, 4],
          maxY: 15
        };
      case 'This Month':
        return {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          data: [22, 18, 25, 15],
          maxY: 30
        };
      case 'Last Month':
        return {
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
          data: [18, 15, 20, 12],
          maxY: 30
        };
      case 'This Year':
      default:
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          data: [21, 53, 30, 10, 0, 0, 0, 0, 0, 0, 0, 0],
          maxY: 60
        };
    }
  };

  const { labels, data, maxY } = getChartData();

  const handleBarHover = (index: number | null) => {
    setHoveredMonth(index);
  };

  return (
    <div className={`p-6 ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-800'} rounded-lg shadow`}>
      <h3 className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>Dashboard</h3>
      <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} mb-6`}>Users Chart</div>

      <div className="relative">
        {hoveredMonth !== null && data[hoveredMonth] > 0 && (
          <div
            className={`absolute ${theme === 'dark' ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-200 text-gray-800'} border shadow-md rounded-md p-3 z-10`}
            style={{
              top: '0',
              left: '50%',
              transform: 'translateX(-50%)',
            }}
          >
            <div className="text-sm font-medium mb-1">{labels[hoveredMonth]}</div>
            <div className="flex items-center gap-2">
              <Circle size={8} className="text-indigo-500 fill-indigo-500" />
              <div className="text-sm">Users: <span className="font-bold">{data[hoveredMonth]}</span></div>
            </div>
          </div>
        )}

        <div className="flex">
          {/* Y-axis labels */}
          <div className="flex flex-col justify-between text-xs text-gray-500 py-2 pr-2">
            <div>60</div>
            <div>50</div>
            <div>40</div>
            <div>30</div>
            <div>20</div>
            <div>10</div>
            <div>0</div>
          </div>

          <div className="flex-1">
            {/* Chart grid */}
            <div className="relative h-60">
              {/* Grid lines */}
              <div className="absolute inset-0 flex flex-col justify-between border-b border-gray-200">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="border-t border-gray-200 w-full h-0"></div>
                ))}
              </div>

              {/* Bars */}
              <div className="absolute inset-0 flex items-end justify-between px-4">
                {data.map((value, index) => (
                  <div
                    key={index}
                    className="flex flex-col items-center justify-end"
                    onMouseEnter={() => handleBarHover(index)}
                    onMouseLeave={() => handleBarHover(null)}
                  >
                    <div
                      className={`w-8 ${value > 0 ? 'bg-indigo-500 hover:bg-indigo-600' : 'bg-transparent'} rounded-t cursor-pointer transition-all`}
                      style={{
                        height: `${value > 0 ? (value / maxY) * 100 : 0}%`,
                        minHeight: value > 0 ? '4px' : '0'
                      }}
                    ></div>
                  </div>
                ))}
              </div>
            </div>

            {/* X-axis values and labels */}
            <div className="flex justify-between px-4 mt-2">
              {labels.map((label, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="text-xs font-medium text-gray-600 mb-1">
                    {data[index] > 0 ? data[index] : ''}
                  </div>
                  <div className="text-xs text-gray-500 text-center">{label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
