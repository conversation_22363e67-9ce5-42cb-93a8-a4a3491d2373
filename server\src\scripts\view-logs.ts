import { config } from 'dotenv';
import { connectDB } from '../config/database';
import { LogModel } from '../models';
import mongoose from 'mongoose';

// Load environment variables
config();

/**
 * Simple script to view logs from the command line
 */
async function viewLogs() {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    const limit = parseInt(args.find(arg => arg.startsWith('--limit='))?.split('=')[1] || '10');
    const action = args.find(arg => arg.startsWith('--action='))?.split('=')[1];
    const category = args.find(arg => arg.startsWith('--category='))?.split('=')[1];
    const resourceType = args.find(arg => arg.startsWith('--resourceType='))?.split('=')[1];
    const userId = args.find(arg => arg.startsWith('--userId='))?.split('=')[1];
    
    // Build query
    const query: any = {};
    
    if (action) {
      query.action = action;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (resourceType) {
      query.resourceType = resourceType;
    }
    
    if (userId) {
      query.userId = userId;
    }
    
    // Get logs
    const logs = await LogModel.find(query)
      .sort({ createdAt: -1 })
      .limit(limit);
    
    // Display logs
    console.log(`\n📋 Found ${logs.length} logs:\n`);
    
    logs.forEach((log, index) => {
      console.log(`--- Log #${index + 1} ---`);
      console.log(`ID: ${log.id}`);
      console.log(`Action: ${log.action}`);
      console.log(`Category: ${log.category}`);
      console.log(`Resource: ${log.resourceType}${log.resourceId ? ` (${log.resourceId})` : ''}`);
      console.log(`User: ${log.userId}`);
      console.log(`Status: ${log.status}`);
      console.log(`Severity: ${log.severity}`);
      console.log(`Time: ${log.createdAt}`);
      
      if (log.details) {
        console.log(`Details: ${log.details}`);
      }
      
      if (log.previousState) {
        console.log('Previous State:', JSON.stringify(log.previousState, null, 2));
      }
      
      if (log.newState) {
        console.log('New State:', JSON.stringify(log.newState, null, 2));
      }
      
      console.log('\n');
    });
    
    // Close database connection
    await mongoose.disconnect();
    
    console.log('✅ Done!');
  } catch (error) {
    console.error('❌ Error viewing logs:', error);
    process.exit(1);
  }
}

// Run the script
viewLogs();
