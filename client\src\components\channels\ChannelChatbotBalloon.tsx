import React, { useState, useEffect, useRef } from 'react';
import { X, Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { videoAPI } from '@/services/api';
import { Link } from 'react-router-dom';
import { useVideos } from '@/context/VideoContext';

interface ChannelChatbotBalloonProps {
  channel: {
    id: string;
    name: string;
    displayName: string;
    description: string;
    avatar?: string;
    banner?: string;
    isVerified: boolean;
    ownerId: string;
    category?: string;
    tags?: string[];
    stats: {
      subscribers: number;
      totalViews: number;
      videoCount: number;
    };
  };
  isOpen?: boolean;
  onClose?: () => void;
}

interface Message {
  id: string;
  sender: 'user' | 'bot';
  content: string;
  timestamp: Date;
}

const ChannelChatbotBalloon: React.FC<ChannelChatbotBalloonProps> = ({
  channel,
  isOpen = false,
  onClose
}) => {
  const { toast } = useToast(); // Keep for potential future use
  const { videos } = useVideos();
  const [isExpanded, setIsExpanded] = useState(isOpen);
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [relatedVideos, setRelatedVideos] = useState<any[]>([]);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Fetch related videos based on channel category and tags
  const fetchRelatedVideos = async () => {
    try {
      // Get videos with the same category or tags
      const allVideos = videos.filter(video => {
        // Safe access to properties that might not exist in the Video type
        const videoChannelId = (video as any).channelId;
        const videoCategory = video.category;

        return (
          // Not from the same channel (if channelId exists)
          (videoChannelId === undefined || videoChannelId !== channel.id) &&
          // Same category (if both have categories)
          (channel.category && videoCategory && videoCategory === channel.category)
        );
      });

      // If we don't have enough videos from context, try to fetch from API
      if (allVideos.length < 5) {
        try {
          // Only fetch by category if channel has a category
          const params: any = {
            limit: 10
          };

          if (channel.category) {
            params.category = channel.category;
          }

          const response = await videoAPI.getVideos(params);

          if (response.success) {
            // Filter out videos from the same channel if possible
            const apiVideos = response.videos.filter((v: any) =>
              v.channelId === undefined || v.channelId !== channel.id
            );

            // Combine and deduplicate videos
            const combinedVideos = [...allVideos];

            apiVideos.forEach((video: any) => {
              if (!combinedVideos.some(v => v.id === video.id)) {
                combinedVideos.push(video);
              }
            });

            setRelatedVideos(combinedVideos.slice(0, 5));
          } else {
            setRelatedVideos(allVideos.slice(0, 5));
          }
        } catch (error) {
          console.error('Error fetching related videos:', error);
          setRelatedVideos(allVideos.slice(0, 5));
        }
      } else {
        setRelatedVideos(allVideos.slice(0, 5));
      }
    } catch (error) {
      console.error('Error processing related videos:', error);
      setRelatedVideos([]);
    }
  };

  // Get all videos on the platform
  const getAllVideos = () => {
    // Return all videos from the VideoContext
    // Since visibility might not be in the Video type, we'll return all videos
    return videos;
  };

  // Initialize with a welcome message
  useEffect(() => {
    if (isExpanded && messages.length === 0) {
      // Fetch related videos when chatbot is opened
      fetchRelatedVideos();

      // Single short welcome message
      const welcomeMessage = {
        id: Date.now().toString(),
        sender: 'bot' as const,
        content: `👋 Hi! How can I help you with ${channel.displayName}'s channel?`,
        timestamp: new Date()
      };

      setMessages([welcomeMessage]);
    }
  }, [isExpanded, channel.displayName, messages.length, channel.stats, channel.id, channel.category, channel.tags, videos]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded) {
      inputRef.current?.focus();
    }
  }, [isExpanded]);

  const handleClose = () => {
    setIsExpanded(false);
    if (onClose) {
      onClose();
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      sender: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsTyping(true);

    // Simulate bot thinking
    setTimeout(() => {
      const botResponse = generateBotResponse(message.trim().toLowerCase(), channel);
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        sender: 'bot',
        content: botResponse,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);
      setIsTyping(false);
    }, 1000);
  };

  const generateBotResponse = (query: string, channel: ChannelChatbotBalloonProps['channel']): string => {
    // Format related videos as a list with links
    const formatRelatedVideos = () => {
      if (relatedVideos.length === 0) {
        return "I couldn't find any related videos at the moment. Please try again later.";
      }

      let response = "Here are some related videos you might enjoy:\n\n";
      relatedVideos.forEach((video, index) => {
        response += `${index + 1}. "${video.title}" - ${video.creator?.username || 'Unknown creator'}\n   🔗 /watch/${video.url || video.id}\n\n`;
      });

      return response;
    };

    // Format all videos as a list with links
    const formatAllVideos = () => {
      const allVideos = getAllVideos();

      if (allVideos.length === 0) {
        return "There are no videos available on LawEngaxe at the moment. Please check back later.";
      }

      let response = "Here are all the videos currently available on LawEngaxe:\n\n";

      // Group videos by category for better organization
      const videosByCategory: Record<string, any[]> = {};

      allVideos.forEach(video => {
        const category = video.category || 'Uncategorized';
        if (!videosByCategory[category]) {
          videosByCategory[category] = [];
        }
        videosByCategory[category].push(video);
      });

      // List videos by category
      Object.keys(videosByCategory).sort().forEach(category => {
        response += `📚 ${category}:\n`;

        videosByCategory[category].forEach((video, index) => {
          response += `${index + 1}. "${video.title}" - ${video.creator?.username || 'Unknown creator'}\n   🔗 /watch/${video.url || video.id}\n\n`;
        });
      });

      return response;
    };

    // Platform information
    const platformInfo = `LawEngaxe is a specialized video platform focused on legal education and resources. It offers:
• Video content from legal experts and educators
• Channels dedicated to different areas of law
• Interactive features like AI-powered chat assistance
• Voice and video translation capabilities
• Community features for legal professionals and students`;

    // Check for keywords in the query
    if (query.includes('hello') || query.includes('hi') || query.includes('hey')) {
      return `Hello! How can I help you with information about LawEngaxe or ${channel.displayName}'s channel?`;
    }

    // Platform-related queries
    if (query.includes('platform') || query.includes('lawengaxe') || query.includes('engaxe')) {
      return platformInfo;
    }

    if (query.includes('feature') || query.includes('capabilities') || query.includes('what can')) {
      return `LawEngaxe offers several powerful features:
• Video content on various legal topics
• AI-powered chat assistance for personalized help
• Voice calling with AI assistants
• Video translation to multiple languages
• Transcription and captioning services
• Community forums and direct messaging
• Channel subscriptions to follow your favorite creators`;
    }

    // All videos query
    if (query.includes('all videos') || query.includes('list videos') || query.includes('show videos') ||
        query.includes('all content') || query.includes('jitni video') || query.includes('sare video') ||
        query.includes('saare video') || query.includes('sabhi video')) {
      return formatAllVideos();
    }

    // Related videos queries
    if (query.includes('related') || query.includes('recommend') || query.includes('similar') ||
        query.includes('suggest') || query.includes('more like')) {
      return formatRelatedVideos();
    }

    // Channel-specific queries
    if (query.includes('owner') || query.includes('who')) {
      return `This channel is owned by ${channel.displayName}. They've been creating great content on LawEngaxe!`;
    }

    if (query.includes('video') || query.includes('videos') || query.includes('content')) {
      return `${channel.displayName} has uploaded ${channel.stats.videoCount} videos so far. Their content has received a total of ${channel.stats.totalViews} views! Would you like me to suggest some related videos?`;
    }

    if (query.includes('subscriber') || query.includes('followers') || query.includes('following')) {
      return `This channel has ${channel.stats.subscribers} subscribers. Join them by clicking the Subscribe button!`;
    }

    if (query.includes('about') || query.includes('description') || query.includes('info')) {
      if (query.includes('channel')) {
        return `About ${channel.displayName}: ${channel.description}`;
      } else {
        return platformInfo;
      }
    }

    if (query.includes('view') || query.includes('views') || query.includes('popular')) {
      return `Videos on this channel have been viewed ${channel.stats.totalViews} times in total. That's impressive!`;
    }

    if (query.includes('verified') || query.includes('official')) {
      return channel.isVerified
        ? `Yes, ${channel.displayName} is a verified channel on LawEngaxe.`
        : `${channel.displayName} is not currently verified on LawEngaxe.`;
    }

    if (query.includes('contact') || query.includes('message') || query.includes('chat')) {
      return `You can contact ${channel.displayName} directly by clicking the Chat button on their channel page.`;
    }

    // Legal topics
    if (query.includes('legal') || query.includes('law') || query.includes('topic')) {
      return `LawEngaxe covers various legal topics including:
• Constitutional Law
• Criminal Law
• Civil Law
• Corporate Law
• International Law
• Human Rights
• Legal Technology
• Legal Education

You can search for specific topics using the search bar at the top of the page, or browse channels by category.`;
    }

    // Tips for using the platform
    if (query.includes('tip') || query.includes('how to use') || query.includes('guide')) {
      return `Here are some tips for using LawEngaxe effectively:
• Subscribe to channels that match your interests
• Use the AI assistant (that's me!) to get personalized help
• Try the voice calling feature for hands-free assistance
• Enable video translations if you prefer content in other languages
• Create playlists to organize videos by topic
• Join the community forums to connect with other legal professionals and students`;
    }

    if (query.includes('help') || query.includes('what') || query.includes('can you')) {
      return `I can help you with:
• Information about the LawEngaxe platform
• Details about ${channel.displayName}'s channel
• Listing all videos available on LawEngaxe
• Finding related videos you might enjoy
• Discovering content on specific legal topics
• Tips for using LawEngaxe effectively
• Answering questions about features and capabilities

Just ask me anything you'd like to know! Try asking "Show me all videos on LawEngaxe" to see a complete list.`;
    }

    // Default response if no keywords match
    return `I'm here to help with information about ${channel.displayName}'s channel and the LawEngaxe platform. You can ask about videos, legal topics, or platform features. How can I assist you today?`;
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Collapsed state
  if (!isExpanded) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          className="p-0 h-14 w-14 rounded-full bg-gradient-to-r from-primary to-primary/80 shadow-xl hover:shadow-xl text-white transition-all duration-300 border-2 border-primary-foreground/10 flex items-center justify-center"
          onClick={() => setIsExpanded(true)}
        >
          <svg viewBox="0 0 24 24" width="100%" height="100%" className="h-10 w-10">
            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" strokeWidth="1.5" />
            <path d="M12 3L14 9L20 12L14 15L12 21L10 15L4 12L10 9L12 3Z" fill="currentColor" />
            <path d="M6 6L7 9L10 10L7 11L6 14L5 11L2 10L5 9L6 6Z" fill="currentColor" />
            <path d="M18 18L19 21L22 22L19 23L18 24L17 23L14 22L17 21L18 18Z" fill="currentColor" />
          </svg>
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className="w-72 md:w-80 shadow-2xl border-border/50 overflow-hidden animate-in slide-in-from-bottom-10 duration-300 rounded-2xl">
        <CardHeader className="p-3 border-b flex flex-row justify-between items-center bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Avatar className="h-9 w-9 ring-2 ring-primary-foreground/30 shadow-md">
                <AvatarImage
                  src={channel.avatar || '/placeholder.svg'}
                  alt={channel.displayName}
                  onError={(e) => {
                    console.warn(`Failed to load avatar for channel ${channel.name}`);
                    // Error is handled by AvatarFallback
                  }}
                />
                <AvatarFallback className="bg-white text-black text-xs font-bold border border-black">
                  {channel.displayName.substring(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <span className="absolute bottom-0 right-0 h-3 w-3 bg-green-500 rounded-full border-2 border-primary animate-pulse"></span>
            </div>
            <div>
              <h3 className="font-bold text-sm">LawEngaxe Assistant</h3>
              <p className="text-[11px] text-primary-foreground/90 flex items-center">
                {channel.displayName}'s Channel
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-7 w-7 rounded-full text-primary-foreground hover:bg-primary-foreground/20 p-0 transition-all duration-200 hover:rotate-90"
            onClick={handleClose}
          >
            <X className="h-3.5 w-3.5" />
          </Button>
        </CardHeader>

        <CardContent className="p-0">
          <ScrollArea className="h-60 p-3">
            <div className="space-y-2">
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${
                    msg.sender === 'user' ? 'justify-end' : 'justify-start'
                  } mb-2`}
                >
                  {msg.sender === 'bot' && (
                    <div className="flex-shrink-0 mr-1.5">
                      <Avatar className="h-6 w-6">
                        <AvatarImage
                          src={channel.avatar || '/placeholder.svg'}
                          alt={channel.displayName}
                          onError={(e) => {
                            console.warn(`Failed to load avatar for channel ${channel.name}`);
                            // Error is handled by AvatarFallback
                          }}
                        />
                        <AvatarFallback className="bg-white text-black text-[10px] border border-black">
                          {channel.displayName.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  )}
                  <div
                    className={`max-w-[85%] rounded-2xl shadow-md ${
                      msg.sender === 'user'
                        ? 'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-br-none transform transition-all duration-200 hover:shadow-lg'
                        : 'bg-muted/90 border border-border/50 rounded-bl-none transform transition-all duration-200 hover:shadow-lg'
                    } px-4 py-2.5`}
                  >
                    <p className={`text-sm leading-relaxed ${msg.sender === 'user' ? 'text-primary-foreground' : 'text-foreground'}`}>
                      {msg.content}
                    </p>
                    <div className="flex items-center justify-between mt-1.5">
                      <p
                        className={`text-[10px] ${
                          msg.sender === 'user'
                            ? 'text-primary-foreground/80'
                            : 'text-muted-foreground'
                        }`}
                      >
                        {formatDistanceToNow(msg.timestamp, { addSuffix: true })}
                      </p>
                      {msg.sender === 'user' && (
                        <div className="flex-shrink-0 ml-1.5">
                          <div className="h-3.5 w-3.5 rounded-full bg-primary-foreground/30 flex items-center justify-center">
                            <span className="text-[7px] text-primary-foreground">✓</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              {isTyping && (
                <div className="flex justify-start mb-3">
                  <div className="flex-shrink-0 mr-2">
                    <Avatar className="h-7 w-7 ring-1 ring-primary/20">
                      <AvatarImage
                        src={channel.avatar || '/placeholder.svg'}
                        alt={channel.displayName}
                        onError={(e) => {
                          console.warn(`Failed to load avatar for channel ${channel.name}`);
                          // Error is handled by AvatarFallback
                        }}
                      />
                      <AvatarFallback className="bg-white text-black text-xs font-semibold border border-black">
                        {channel.displayName.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </div>
                  <div className="max-w-[80%] rounded-2xl shadow-md bg-muted/90 border border-border/50 rounded-bl-none px-4 py-2.5">
                    <div className="flex space-x-2 items-center">
                      <div className="h-2.5 w-2.5 rounded-full bg-primary/80 animate-pulse" style={{ animationDelay: '0ms' }}></div>
                      <div className="h-2.5 w-2.5 rounded-full bg-primary/80 animate-pulse" style={{ animationDelay: '300ms' }}></div>
                      <div className="h-2.5 w-2.5 rounded-full bg-primary/80 animate-pulse" style={{ animationDelay: '600ms' }}></div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>
        </CardContent>

        <CardFooter className="p-3 border-t bg-background/90 backdrop-blur-sm">
          <div className="flex w-full gap-3 items-center">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Ask a question..."
              className="flex-1 rounded-full border-muted-foreground/30 focus-visible:ring-primary/60 bg-muted/60 px-4 py-2 h-12 shadow-inner text-sm transition-all duration-200 hover:border-primary/30"
            />
            <Button
              size="icon"
              onClick={handleSendMessage}
              disabled={!message.trim() || isTyping}
              className={`rounded-full h-11 w-11 ${
                !message.trim() || isTyping
                  ? 'opacity-50'
                  : 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105'
              }`}
            >
              <Send className="h-5 w-5" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ChannelChatbotBalloon;
