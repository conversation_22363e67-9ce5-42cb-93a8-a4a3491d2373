import api from './api';
import { Conversation, Message } from '@/types';

/**
 * Message API service
 * Handles all API calls related to messaging
 */
export const messageAPI = {
  /**
   * Get all conversations for the current user
   */
  getConversations: async (params?: {
    page?: number;
    limit?: number;
    sort?: string;
    archived?: boolean;
    pinned?: boolean;
    search?: string;
  }) => {
    const response = await api.get('/api/conversations', { params });
    return response.data;
  },

  /**
   * Get a conversation by ID
   */
  getConversationById: async (id: string) => {
    const response = await api.get(`/api/conversations/${id}`);
    return response.data;
  },

  /**
   * Create a new conversation
   */
  createConversation: async (data: {
    creatorId: string;
    subject: string;
    initialMessage: string;
    settings?: {
      notifications?: boolean;
      encryption?: boolean;
      retention?: number;
      readReceipts?: boolean;
    };
    metadata?: {
      tags?: string[];
      customFields?: Record<string, any>;
    };
  }) => {
    const response = await api.post('/api/conversations', data);
    return response.data;
  },

  /**
   * Get messages for a conversation
   */
  getMessagesByConversation: async (
    conversationId: string,
    params?: {
      page?: number;
      limit?: number;
      sort?: string;
    }
  ) => {
    const response = await api.get(`/api/messages/conversation/${conversationId}`, { params });
    return response.data;
  },

  /**
   * Send a text message
   */
  sendTextMessage: async (data: {
    conversationId: string;
    content: string;
    contentType?: 'text' | 'html' | 'markdown';
    attachments?: Array<{
      type: string;
      url: string;
      name: string;
      size: number;
    }>;
  }) => {
    const response = await api.post('/api/messages', data);
    return response.data;
  },

  /**
   * Mark a message as read
   */
  markMessageAsRead: async (id: string) => {
    const response = await api.post(`/api/messages/${id}/read`);
    return response.data;
  },

  /**
   * Mark a conversation as read
   */
  markConversationAsRead: async (id: string) => {
    const response = await api.post(`/api/conversations/${id}/read`);
    return response.data;
  },

  /**
   * Archive a conversation
   */
  archiveConversation: async (id: string) => {
    const response = await api.post(`/api/conversations/${id}/archive`);
    return response.data;
  },

  /**
   * Unarchive a conversation
   */
  unarchiveConversation: async (id: string) => {
    const response = await api.post(`/api/conversations/${id}/unarchive`);
    return response.data;
  },

  /**
   * Pin a conversation
   */
  pinConversation: async (id: string) => {
    const response = await api.post(`/api/conversations/${id}/pin`);
    return response.data;
  },

  /**
   * Unpin a conversation
   */
  unpinConversation: async (id: string) => {
    const response = await api.post(`/api/conversations/${id}/unpin`);
    return response.data;
  }
};

export default messageAPI;
