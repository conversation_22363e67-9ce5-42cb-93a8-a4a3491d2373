import { FastifyRequest, FastifyReply } from 'fastify';
import permissionService from '../services/permission.service';
import { IPermission } from '../models/permission.model';
import { AuthenticatedUser } from '../types/user';

/**
 * Permission controller for handling permission-related requests
 */
export class PermissionController {
  /**
   * Create a new permission
   */
  async createPermission(
    request: FastifyRequest<{
      Body: Partial<IPermission>;
    }>,
    reply: FastifyReply
  ) {
    try {
      const permissionData = request.body;
      const createdBy = (request.user as AuthenticatedUser).id;

      const permission = await permissionService.createPermission(permissionData, createdBy);

      return reply.code(201).send({
        success: true,
        message: 'Permission created successfully',
        permission: {
          id: permission.id,
          name: permission.name,
          description: permission.description,
          code: permission.code,
          category: permission.category,
          isActive: permission.isActive,
          resourceType: permission.resourceType,
          action: permission.action,
          scope: permission.scope,
          createdAt: permission.createdAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to create permission',
      });
    }
  }

  /**
   * Get all permissions
   */
  async getAllPermissions(
    request: FastifyRequest<{
      Querystring: {
        page?: number;
        limit?: number;
        search?: string;
        category?: string;
        resourceType?: string;
        action?: string;
        scope?: string;
        isActive?: boolean;
        sortBy?: string;
        sortOrder?: 'asc' | 'desc';
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const options = request.query;
      const result = await permissionService.getAllPermissions(options);

      // Map permissions to include only necessary fields
      const permissions = result.permissions.map(permission => ({
        id: permission.id,
        name: permission.name,
        description: permission.description,
        code: permission.code,
        category: permission.category,
        isActive: permission.isActive,
        resourceType: permission.resourceType,
        action: permission.action,
        scope: permission.scope,
        createdAt: permission.createdAt,
      }));

      return reply.code(200).send({
        success: true,
        permissions,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          pages: result.pages,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: (error as Error).message || 'Failed to get permissions',
      });
    }
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const permission = await permissionService.getPermissionById(id);

      return reply.code(200).send({
        success: true,
        permission: {
          id: permission.id,
          name: permission.name,
          description: permission.description,
          code: permission.code,
          category: permission.category,
          isActive: permission.isActive,
          resourceType: permission.resourceType,
          action: permission.action,
          scope: permission.scope,
          conditions: permission.conditions,
          dependencies: permission.dependencies,
          conflicts: permission.conflicts,
          createdAt: permission.createdAt,
          updatedAt: permission.updatedAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(404).send({
        success: false,
        message: (error as Error).message || 'Permission not found',
      });
    }
  }

  /**
   * Update permission
   */
  async updatePermission(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
      Body: Partial<IPermission>;
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const permissionData = request.body;
      const updatedBy = (request.user as AuthenticatedUser).id;

      const permission = await permissionService.updatePermission(id, permissionData, updatedBy);

      return reply.code(200).send({
        success: true,
        message: 'Permission updated successfully',
        permission: {
          id: permission.id,
          name: permission.name,
          description: permission.description,
          code: permission.code,
          category: permission.category,
          isActive: permission.isActive,
          resourceType: permission.resourceType,
          action: permission.action,
          scope: permission.scope,
          updatedAt: permission.updatedAt,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to update permission',
      });
    }
  }

  /**
   * Delete permission
   */
  async deletePermission(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const deletedBy = (request.user as AuthenticatedUser).id;

      await permissionService.deletePermission(id, deletedBy);

      return reply.code(200).send({
        success: true,
        message: 'Permission deleted successfully',
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to delete permission',
      });
    }
  }

  /**
   * Assign permissions to user
   */
  async assignPermissionsToUser(
    request: FastifyRequest<{
      Params: {
        userId: string;
      };
      Body: {
        permissionIds: string[];
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { userId } = request.params;
      const { permissionIds } = request.body;
      const updatedBy = (request.user as AuthenticatedUser).id;

      const user = await permissionService.assignPermissionsToUser(userId, permissionIds, updatedBy);

      return reply.code(200).send({
        success: true,
        message: 'Permissions assigned successfully',
        user: {
          id: user.id,
          username: user.username,
          permissions: user.permissions,
        },
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to assign permissions',
      });
    }
  }

  /**
   * Get user permissions
   */
  async getUserPermissions(
    request: FastifyRequest<{
      Params: {
        userId: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { userId } = request.params;
      const permissions = await permissionService.getUserPermissions(userId);

      return reply.code(200).send({
        success: true,
        permissions,
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to get user permissions',
      });
    }
  }

  /**
   * Check if user has permission
   */
  async checkUserPermission(
    request: FastifyRequest<{
      Params: {
        userId: string;
        permissionCode: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const { userId, permissionCode } = request.params;
      const hasPermission = await permissionService.userHasPermission(userId, permissionCode);

      return reply.code(200).send({
        success: true,
        hasPermission,
      });
    } catch (error) {
      request.log.error(error);
      return reply.code(400).send({
        success: false,
        message: (error as Error).message || 'Failed to check permission',
      });
    }
  }
}

export default new PermissionController();
