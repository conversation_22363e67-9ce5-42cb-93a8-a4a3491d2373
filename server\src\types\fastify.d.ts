import 'fastify';

declare module 'fastify' {
  interface FastifyRequest {
    /**
     * User object attached by authentication middleware
     */
    user: {
      id: string;
      username: string;
      email: string;
      roles: string[];
      permissions: string[];
      [key: string]: any;
    };

    /**
     * Function to check if user has a specific permission
     * Attached by RBAC middleware
     */
    hasPermission: (permission: string) => Promise<boolean> | boolean;

    /**
     * Function to check if user has a specific role
     * Attached by RBAC middleware
     */
    hasRole: (role: string) => Promise<boolean> | boolean;
  }

  interface FastifyInstance {
    /**
     * Generate access token
     */
    generateAccessToken: (payload: any) => string;

    /**
     * Generate refresh token
     */
    generateRefreshToken: (payload: any) => string;

    /**
     * Verify refresh token
     */
    verifyRefreshToken: (token: string) => Promise<any>;

    /**
     * Get current user
     */
    getCurrentUser: (request: FastifyRequest) => Promise<any>;
  }
}
