import { FastifyRequest, FastifyReply } from 'fastify';
import { createForbiddenError, ErrorCodes } from '../utils/errors';
import permissionService from '../services/permission.service';

/**
 * Middleware to secure Swagger documentation
 * Only allows access to users with the 'docs:read' permission
 */
export async function secureSwagger(request: FastifyRequest, reply: FastifyReply): Promise<void> {
  try {
    // Skip authentication for non-documentation routes
    if (!request.url.startsWith('/documentation')) {
      return;
    }

    // Check if user is authenticated
    if (!request.user || !(request.user as any).id) {
      // Redirect to login page
      return reply.redirect('/api/v1/auth/login-docs');
    }

    // Check if user has the required permission
    const userId = (request.user as any).id;
    const hasPermission = await permissionService.userHasPermission(userId, 'docs:read');

    if (!hasPermission) {
      throw createForbiddenError(
        'You do not have permission to access the API documentation',
        ErrorCodes.INSUFFICIENT_PERMISSIONS
      );
    }
  } catch (error: unknown) {
    // If it's a redirect, don't handle the error
    if (reply.sent) return;

    // Handle the error
    const err = error as { message?: string; errorCode?: string };
    reply.code(403).send({
      success: false,
      error: {
        message: err.message || 'Access denied',
        code: err.errorCode || ErrorCodes.FORBIDDEN,
      },
    });
  }
}
