import { FastifyRequest, FastifyReply } from 'fastify';
import { Type } from '@sinclair/typebox';

/**
 * Standard error response schema for Swagger documentation
 */
export const ErrorResponseSchema = Type.Object({
  success: Type.Boolean({ default: false }),
  error: Type.Object({
    message: Type.String(),
    code: Type.String(),
    data: Type.Optional(Type.Any()),
  }),
});

/**
 * Standard success response schema for Swagger documentation
 */
export const SuccessResponseSchema = Type.Object({
  success: Type.Boolean({ default: true }),
  message: Type.String(),
});

/**
 * Pagination response schema for Swagger documentation
 */
export const PaginationSchema = Type.Object({
  page: Type.Number(),
  limit: Type.Number(),
  total: Type.Number(),
  pages: Type.Number(),
});

/**
 * Common query parameters for pagination
 */
export const PaginationQuerySchema = Type.Object({
  page: Type.Optional(Type.Number({ minimum: 1, default: 1 })),
  limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100, default: 10 })),
  sortBy: Type.Optional(Type.String()),
  sortOrder: Type.Optional(Type.Union([Type.Literal('asc'), Type.Literal('desc')])),
});

/**
 * Common query parameters for search
 */
export const SearchQuerySchema = Type.Object({
  search: Type.Optional(Type.String()),
});

/**
 * Common query parameters for filtering by status
 */
export const StatusFilterQuerySchema = Type.Object({
  status: Type.Optional(Type.Union([
    Type.Literal('active'),
    Type.Literal('pending'),
    Type.Literal('suspended'),
    Type.Literal('banned'),
  ])),
});

/**
 * User schema for Swagger documentation
 */
export const UserSchema = Type.Object({
  id: Type.String(),
  username: Type.String(),
  email: Type.String(),
  firstName: Type.String(),
  lastName: Type.String(),
  displayName: Type.String(),
  avatar: Type.Optional(Type.String()),
  roles: Type.Array(Type.String()),
  permissions: Type.Optional(Type.Array(Type.String())),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' }),
});

/**
 * Role schema for Swagger documentation
 */
export const RoleSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  code: Type.String(),
  description: Type.Optional(Type.String()),
  permissions: Type.Array(Type.String()),
  isActive: Type.Boolean(),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' }),
});

/**
 * Permission schema for Swagger documentation
 */
export const PermissionSchema = Type.Object({
  id: Type.String(),
  name: Type.String(),
  code: Type.String(),
  description: Type.Optional(Type.String()),
  category: Type.String(),
  resourceType: Type.String(),
  action: Type.String(),
  scope: Type.String(),
  isActive: Type.Boolean(),
  createdAt: Type.String({ format: 'date-time' }),
  updatedAt: Type.String({ format: 'date-time' }),
});
