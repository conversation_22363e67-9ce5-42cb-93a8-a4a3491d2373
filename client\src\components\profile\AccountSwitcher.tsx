import { useAuth } from '@/context/AuthContext';
import { useLanguage } from '@/context/LanguageContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ChevronDown, PlusCircle, SwitchCamera } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface AccountSwitcherProps {
  onAddAccount: () => void;
}

export default function AccountSwitcher({ onAddAccount }: AccountSwitcherProps) {
  const { currentUser, userAccounts, switchAccount } = useAuth();
  const { t } = useLanguage();

  if (!currentUser) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center gap-2 cursor-pointer p-2 rounded hover:bg-lingstream-card/50">
          <div className="flex flex-col">
            <span className="font-medium">{currentUser.email || currentUser.username}</span>
          </div>
          <ChevronDown className="h-4 w-4 ml-auto" />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56 bg-lingstream-card border-gray-700">
        <div className="p-2">
          <Button
            variant="outline"
            className="w-full flex items-center justify-center gap-2"
            onClick={() => {
              // This button is just a label, the actual switching happens when clicking on an account
            }}
          >
            <SwitchCamera className="h-4 w-4" />
            <span>{t('profile.switch_account')}</span>
          </Button>
        </div>

        <DropdownMenuSeparator />

        {userAccounts.map((account) => (
          <DropdownMenuItem
            key={account.id}
            className={`flex items-center gap-2 ${account.id === currentUser.id ? 'bg-lingstream-accent/20' : ''}`}
            onSelect={() => switchAccount(account.id)}
          >
            <Avatar className="h-6 w-6">
              <AvatarImage src={account.avatar} alt={account.username} />
              <AvatarFallback>{account.username.charAt(0)}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="font-medium">{account.username}</span>
              <span className="text-xs text-muted-foreground">{account.email}</span>
            </div>
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <div className="p-2">
          <Button variant="outline" className="w-full flex items-center justify-center gap-2" onClick={onAddAccount}>
            <PlusCircle className="h-4 w-4" />
            <span>{t('profile.add_account')}</span>
          </Button>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
