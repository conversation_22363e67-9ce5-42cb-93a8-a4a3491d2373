import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Link } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Loader2, ExternalLink } from 'lucide-react';
import { videoAPI } from '@/services/video-api';

export default function AdminToolsPage() {
  const { currentUser } = useAuth();
  const { toast } = useToast();
  const [isUpdatingLanguages, setIsUpdatingLanguages] = useState(false);
  const [updateResult, setUpdateResult] = useState<any>(null);

  // Function to update all video languages
  const handleUpdateLanguages = async () => {
    try {
      setIsUpdatingLanguages(true);
      setUpdateResult(null);

      const response = await videoAPI.updateAllVideoLanguages();

      console.log('Update languages response:', response);

      setUpdateResult(response);

      toast({
        title: "Languages Updated",
        description: response.message,
        variant: "default"
      });
    } catch (error) {
      console.error('Error updating languages:', error);

      toast({
        title: "Update Failed",
        description: error.response?.data?.message || error.message || "Failed to update video languages",
        variant: "destructive"
      });
    } finally {
      setIsUpdatingLanguages(false);
    }
  };

  if (!currentUser) {
    return (
      <Layout>
        <div className="container py-8">
          <Card>
            <CardHeader>
              <CardTitle>Admin Tools</CardTitle>
              <CardDescription>
                You need to be signed in to access admin tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p>Please sign in to continue</p>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-8">
        <Card>
          <CardHeader>
            <CardTitle>Admin Tools</CardTitle>
            <CardDescription>
              Manage your video database and fix issues
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Video Language Management</h3>
              <p className="text-sm text-lingstream-muted">
                Update all videos to ensure they have proper language information.
                This will add default languages to videos that don't have any and
                ensure all language URLs are valid Engaxe IDs.
              </p>

              <Button
                onClick={handleUpdateLanguages}
                disabled={isUpdatingLanguages}
                className="gap-2"
              >
                {isUpdatingLanguages ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Updating Languages...
                  </>
                ) : (
                  'Update All Video Languages'
                )}
              </Button>

              {updateResult && (
                <div className="mt-4 p-4 bg-lingstream-card rounded-md">
                  <h4 className="font-medium mb-2">Update Results:</h4>
                  <p>Total videos: {updateResult.stats.total}</p>
                  <p>Updated: {updateResult.stats.updated}</p>
                  <p>Skipped: {updateResult.stats.skipped}</p>
                  <p>Errors: {updateResult.stats.errors}</p>
                </div>
              )}
            </div>

            <div className="space-y-4 pt-6 border-t">
              <h3 className="text-lg font-medium">Advanced Tools</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Fix Video Languages</CardTitle>
                    <CardDescription className="text-xs">
                      Dedicated tool to fix language issues in videos
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-lingstream-muted mb-4">
                      Use this dedicated tool to fix language issues in all videos.
                      It provides a more comprehensive fix than the quick update button.
                    </p>
                    <Link to="/admin/videos/fix">
                      <Button variant="outline" className="w-full gap-2">
                        <ExternalLink className="h-4 w-4" />
                        Open Fix Videos Tool
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
}
