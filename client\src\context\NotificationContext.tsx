
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Notification } from '@/types';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

// Mock notifications
const mockNotifications: Notification[] = [
  {
    id: 'n1',
    title: 'New mention',
    content: 'Code<PERSON>aster mentioned you in chat',
    timestamp: new Date(Date.now() - 60 * 60000).toISOString(),
    read: false,
    type: 'mention',
    user: { id: 'u1', username: '<PERSON>Master', avatar: '/placeholder.svg' }
  },
  {
    id: 'n2',
    title: 'Like received',
    content: 'Your comment received 5 likes',
    timestamp: new Date(Date.now() - 2 * 60 * 60000).toISOString(),
    read: false,
    type: 'like'
  },
  {
    id: 'n3',
    title: 'Leaderboard update',
    content: 'You moved up to 3rd place on the leaderboard!',
    timestamp: new Date(Date.now() - 24 * 60 * 60000).toISOString(),
    read: false,
    type: 'achievement'
  }
];

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [unreadCount, setUnreadCount] = useState<number>(0);

  useEffect(() => {
    // Count unread notifications
    const count = notifications.filter(notification => !notification.read).length;
    setUnreadCount(count);
  }, [notifications]);

  const markAsRead = (notificationId: string) => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => 
        notification.id === notificationId ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prevNotifications => 
      prevNotifications.map(notification => ({ ...notification, read: true }))
    );
  };

  return (
    <NotificationContext.Provider value={{
      notifications,
      unreadCount,
      markAsRead,
      markAllAsRead
    }}>
      {children}
    </NotificationContext.Provider>
  );
};
