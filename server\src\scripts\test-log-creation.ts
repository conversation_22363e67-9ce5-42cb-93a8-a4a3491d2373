import { config } from 'dotenv';
import mongoose from 'mongoose';
import { connectDB } from '../config/database';
import { LogModel } from '../models';

// Load environment variables
config();

/**
 * Test script to create a log entry directly in MongoDB
 */
async function testLogCreation() {
  try {
    console.log('🔍 Connecting to database...');
    await connectDB();
    console.log('MongoDB connected successfully');

    // Create a test log entry
    const log = new LogModel({
      action: 'test',
      category: 'system',
      resourceType: 'test',
      userId: 'system',
      userIp: '127.0.0.1',
      userAgent: 'Test Script',
      status: 'success',
      severity: 'low',
      details: 'Test log entry',
      createdBy: 'system',
      updatedBy: 'system',
    });

    // Save the log to the database
    await log.save();
    console.log(`✅ Test log created successfully with ID: ${log.id}`);

    // Retrieve the log to verify it was created
    const retrievedLog = await LogModel.findOne({ id: log.id });
    console.log('Retrieved log:', retrievedLog);

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('MongoDB disconnected');
  } catch (error) {
    console.error('❌ Error creating test log:', error);
    process.exit(1);
  }
}

// Run the test
testLogCreation();
