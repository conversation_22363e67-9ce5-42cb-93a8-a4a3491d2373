import { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  Home,
  Bell,
  BellRing,
  BellOff,
  Info,
  ExternalLink,
  HelpCircle,
  Send,
  Settings,
  Check,
  Smartphone,
  Globe
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';

// Import shadcn components
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
// import { Badge } from '@/components/ui/badge';
// import { Separator } from '@/components/ui/separator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

export default function PushNotificationsPage() {
  const [isPushEnabled, setIsPushEnabled] = useState(true);
  const [oneSignalAppId, setOneSignalAppId] = useState('');
  const [restApiKey, setRestApiKey] = useState('');
  const [fcmServerKey, setFcmServerKey] = useState('');
  const [fcmSenderId, setFcmSenderId] = useState('');
  const [activeTab, setActiveTab] = useState('onesignal');
  const [isSaved, setIsSaved] = useState(false);

  // No longer needed as we use the Switch component's onCheckedChange
  // const handleTogglePush = () => {
  //   setIsPushEnabled(!isPushEnabled);
  // };

  const handleSaveSettings = () => {
    // In a real app, this would save the settings to the server
    setIsSaved(true);
    setTimeout(() => setIsSaved(false), 3000);
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto space-y-6">
            {/* Header and Breadcrumb */}
            <div>
              <h1 className="text-2xl font-bold mb-2">Push Notifications Settings</h1>

              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="/admin" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Admin Panel
                      </Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbLink asChild>
                      <Link to="#">Mobile & API Settings</Link>
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Push Notifications Settings</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>

            {/* Info Alert */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>About Push Notifications</AlertTitle>
              <AlertDescription>
                Push notifications allow you to engage with your users even when they're not actively using your application.
                Configure your settings below to start sending notifications.
              </AlertDescription>
            </Alert>

            {/* Status Card */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle>Push Notification Status</CardTitle>
                <CardDescription>
                  Enable or disable push notifications for your application
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col space-y-8">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="text-base">
                        Push Notifications System
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        Enable this feature and users will get notified on their browser / app while the app is closed.
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={isPushEnabled}
                        onCheckedChange={setIsPushEnabled}
                        id="push-notifications"
                      />
                      <Label htmlFor="push-notifications" className="text-sm font-medium">
                        {isPushEnabled ? (
                          <span className="text-green-600 flex items-center">
                            <BellRing className="h-4 w-4 mr-1" /> Enabled
                          </span>
                        ) : (
                          <span className="text-gray-500 flex items-center">
                            <BellOff className="h-4 w-4 mr-1" /> Disabled
                          </span>
                        )}
                      </Label>
                    </div>
                  </div>

                  {isPushEnabled && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Card className="bg-green-50 border-green-200">
                        <CardHeader className="pb-2 pt-4">
                          <CardTitle className="text-sm font-medium text-green-800 flex items-center">
                            <Bell className="h-4 w-4 mr-2" />
                            Ready to Send
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0 pb-4">
                          <p className="text-xs text-green-700">
                            Your push notification system is configured and ready to use.
                          </p>
                        </CardContent>
                      </Card>

                      <Card className="bg-blue-50 border-blue-200">
                        <CardHeader className="pb-2 pt-4">
                          <CardTitle className="text-sm font-medium text-blue-800 flex items-center">
                            <Smartphone className="h-4 w-4 mr-2" />
                            Mobile Support
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0 pb-4">
                          <p className="text-xs text-blue-700">
                            Notifications will be delivered to both iOS and Android devices.
                          </p>
                        </CardContent>
                      </Card>

                      <Card className="bg-purple-50 border-purple-200">
                        <CardHeader className="pb-2 pt-4">
                          <CardTitle className="text-sm font-medium text-purple-800 flex items-center">
                            <Globe className="h-4 w-4 mr-2" />
                            Web Support
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0 pb-4">
                          <p className="text-xs text-purple-700">
                            Web push notifications are enabled for Chrome, Firefox, and Safari browsers.
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" asChild>
                  <Link to="#" className="flex items-center text-amber-600 hover:text-amber-700">
                    <HelpCircle className="h-4 w-4 mr-1" />
                    Need Help? Read The Documentation
                  </Link>
                </Button>
              </CardFooter>
            </Card>

            {/* Configuration Card */}
            <Card>
              <CardHeader>
                <CardTitle>Push Notification Configuration</CardTitle>
                <CardDescription>
                  Configure your push notification service provider
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue={activeTab} onValueChange={setActiveTab} className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="onesignal" className="flex items-center">
                      <img src="https://onesignal.com/assets/common/logo_onesignal_color.svg" alt="OneSignal" className="h-4 mr-2" />
                      OneSignal
                    </TabsTrigger>
                    <TabsTrigger value="firebase" className="flex items-center">
                      <img src="https://www.gstatic.com/devrel-devsite/prod/v2210deb8920cd4a55bd580441aa58e7853afc04b39a9d9ac4198e1cd7fbe04ef/firebase/images/touchicon-180.png" alt="Firebase" className="h-4 mr-2" />
                      Firebase (FCM)
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="onesignal" className="space-y-4 py-4">
                    <div className="grid gap-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="onesignal-app-id" className="text-right">
                          OneSignal APP ID
                        </Label>
                        <Input
                          id="onesignal-app-id"
                          value={oneSignalAppId}
                          onChange={(e) => setOneSignalAppId(e.target.value)}
                          placeholder="Enter your OneSignal APP ID"
                          className="col-span-3"
                        />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="rest-api-key" className="text-right">
                          REST API Key
                        </Label>
                        <Input
                          id="rest-api-key"
                          value={restApiKey}
                          onChange={(e) => setRestApiKey(e.target.value)}
                          placeholder="Enter your REST API Key"
                          className="col-span-3"
                        />
                      </div>

                      <div className="grid grid-cols-4 items-start gap-4">
                        <div className="text-right pt-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">You can find your OneSignal APP ID and REST API Key in your OneSignal dashboard under Settings {"&gt;"} Keys & IDs.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <div className="col-span-3">
                          <Alert className="bg-blue-50 border-blue-200">
                            <Info className="h-4 w-4 text-blue-600" />
                            <AlertDescription className="text-blue-700 text-xs">
                              Don't have a OneSignal account yet?
                              <a
                                href="https://onesignal.com/"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-1 text-blue-600 hover:underline inline-flex items-center"
                              >
                                Register here
                                <ExternalLink className="h-3 w-3 ml-1" />
                              </a>
                            </AlertDescription>
                          </Alert>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="firebase" className="space-y-4 py-4">
                    <div className="grid gap-4">
                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="fcm-server-key" className="text-right">
                          FCM Server Key
                        </Label>
                        <Input
                          id="fcm-server-key"
                          value={fcmServerKey}
                          onChange={(e) => setFcmServerKey(e.target.value)}
                          placeholder="Enter your Firebase Cloud Messaging Server Key"
                          className="col-span-3"
                        />
                      </div>

                      <div className="grid grid-cols-4 items-center gap-4">
                        <Label htmlFor="fcm-sender-id" className="text-right">
                          FCM Sender ID
                        </Label>
                        <Input
                          id="fcm-sender-id"
                          value={fcmSenderId}
                          onChange={(e) => setFcmSenderId(e.target.value)}
                          placeholder="Enter your Firebase Cloud Messaging Sender ID"
                          className="col-span-3"
                        />
                      </div>

                      <div className="grid grid-cols-4 items-start gap-4">
                        <div className="text-right pt-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <HelpCircle className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">You can find your FCM Server Key and Sender ID in your Firebase Console under Project Settings {"&gt;"} Cloud Messaging.</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <div className="col-span-3">
                          <Alert className="bg-blue-50 border-blue-200">
                            <Info className="h-4 w-4 text-blue-600" />
                            <AlertDescription className="text-blue-700 text-xs">
                              Don't have a Firebase account yet?
                              <a
                                href="https://firebase.google.com/"
                                target="_blank"
                                rel="noopener noreferrer"
                                className="ml-1 text-blue-600 hover:underline inline-flex items-center"
                              >
                                Register here
                                <ExternalLink className="h-3 w-3 ml-1" />
                              </a>
                            </AlertDescription>
                          </Alert>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <Button variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Test Configuration
                </Button>
                <Button onClick={handleSaveSettings}>
                  {isSaved ? (
                    <>
                      <Check className="h-4 w-4 mr-2" />
                      Saved
                    </>
                  ) : (
                    <>Save Settings</>
                  )}
                </Button>
              </CardFooter>
            </Card>

            {/* Send Test Notification Card */}
            <Card>
              <CardHeader>
                <CardTitle>Send Test Notification</CardTitle>
                <CardDescription>
                  Send a test notification to verify your configuration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="test-title" className="text-right">
                      Notification Title
                    </Label>
                    <Input
                      id="test-title"
                      placeholder="Test Notification"
                      className="col-span-3"
                    />
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="test-message" className="text-right">
                      Notification Message
                    </Label>
                    <Input
                      id="test-message"
                      placeholder="This is a test notification"
                      className="col-span-3"
                    />
                  </div>
                </div>
              </CardContent>
              <CardFooter className="justify-end">
                <Button variant="secondary">
                  <Send className="h-4 w-4 mr-2" />
                  Send Test Notification
                </Button>
              </CardFooter>
            </Card>

            {/* Best Practices */}
            <Card>
              <CardHeader>
                <CardTitle>Push Notification Best Practices</CardTitle>
                <CardDescription>
                  Tips for effective push notifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Keep it Brief</h3>
                    <p className="text-sm text-muted-foreground">Short, concise messages have higher engagement rates. Aim for 60-90 characters.</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Timing Matters</h3>
                    <p className="text-sm text-muted-foreground">Send notifications when users are most likely to engage, typically between 12-5 PM on weekdays.</p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h3 className="font-medium mb-2">Personalize Content</h3>
                    <p className="text-sm text-muted-foreground">Personalized notifications have up to 4x higher open rates than generic messages.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    </div>
  );
}
