import { FastifyRequest, FastifyReply } from 'fastify';
import { messageService } from '../services/message.service';
import { conversationService } from '../services/conversation.service';
import { AuthenticatedUser } from '../types/user';

/**
 * Message controller for handling message-related requests
 */
export class MessageController {
  /**
   * Get messages for a conversation
   */
  async getMessagesByConversation(
    request: FastifyRequest<{
      Params: {
        conversationId: string;
      };
      Querystring: {
        page?: number;
        limit?: number;
        sort?: string;
        before?: string;
        after?: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { conversationId } = request.params;
      const { page, limit, sort, before, after } = request.query;

      // Convert date strings to Date objects if provided
      const beforeDate = before ? new Date(before) : undefined;
      const afterDate = after ? new Date(after) : undefined;

      const result = await messageService.getMessagesByConversation(conversationId, userId, {
        page,
        limit,
        sort,
        before: beforeDate,
        after: afterDate,
      });

      return reply.code(200).send({
        success: true,
        data: result.data,
        pagination: result.pagination,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        message: error.message || 'Failed to get messages',
        error: error.code || 'INTERNAL_SERVER_ERROR',
      });
    }
  }

  /**
   * Send a text message
   */
  async sendTextMessage(
    request: FastifyRequest<{
      Body: {
        conversationId: string;
        content: string;
        clientId?: string;
        metadata?: {
          tags?: string[];
          isAnswer?: boolean;
          customFields?: Record<string, any>;
        };
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { conversationId, content, clientId, metadata } = request.body;

      // Get conversation to determine sender type
      const conversation = await conversationService.getConversationById(conversationId, userId);
      const senderType = conversation.creatorId === userId ? 'creator' : 'user';

      const message = await messageService.sendTextMessage({
        conversationId,
        senderId: userId,
        senderType,
        content,
        clientId,
        metadata,
      });

      return reply.code(201).send({
        success: true,
        data: message,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        message: error.message || 'Failed to send message',
        error: error.code || 'INTERNAL_SERVER_ERROR',
      });
    }
  }

  /**
   * Send a media message
   */
  async sendMediaMessage(
    request: FastifyRequest<{
      Body: {
        conversationId: string;
        contentType: 'voice' | 'image' | 'video' | 'file';
        mediaUrl: string;
        mediaMimeType: string;
        content?: string;
        clientId?: string;
        metadata?: {
          tags?: string[];
          isAnswer?: boolean;
          customFields?: Record<string, any>;
        };
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const {
        conversationId,
        contentType,
        mediaUrl,
        mediaMimeType,
        content,
        clientId,
        metadata,
      } = request.body;

      // Get conversation to determine sender type
      const conversation = await conversationService.getConversationById(conversationId, userId);
      const senderType = conversation.creatorId === userId ? 'creator' : 'user';

      const message = await messageService.sendMediaMessage({
        conversationId,
        senderId: userId,
        senderType,
        contentType,
        mediaUrl,
        mediaMimeType,
        content,
        clientId,
        metadata,
      });

      return reply.code(201).send({
        success: true,
        data: message,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        message: error.message || 'Failed to send message',
        error: error.code || 'INTERNAL_SERVER_ERROR',
      });
    }
  }

  /**
   * Mark a message as read
   */
  async markMessageAsRead(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { id } = request.params;

      await messageService.markMessageAsRead(id, userId);

      return reply.code(200).send({
        success: true,
        message: 'Message marked as read',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        message: error.message || 'Failed to mark message as read',
        error: error.code || 'INTERNAL_SERVER_ERROR',
      });
    }
  }

  /**
   * Delete a message
   */
  async deleteMessage(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { id } = request.params;

      await messageService.deleteMessage(id, userId);

      return reply.code(200).send({
        success: true,
        message: 'Message deleted successfully',
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        message: error.message || 'Failed to delete message',
        error: error.code || 'INTERNAL_SERVER_ERROR',
      });
    }
  }

  /**
   * Translate a message
   */
  async translateMessage(
    request: FastifyRequest<{
      Params: {
        id: string;
      };
      Body: {
        targetLanguage: string;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const userId = (request.user as AuthenticatedUser).id;
      const { id } = request.params;
      const { targetLanguage } = request.body;

      const message = await messageService.translateMessage(id, userId, targetLanguage);

      return reply.code(200).send({
        success: true,
        data: message,
      });
    } catch (error: any) {
      request.log.error(error);
      return reply.code(error.statusCode || 500).send({
        success: false,
        message: error.message || 'Failed to translate message',
        error: error.code || 'INTERNAL_SERVER_ERROR',
      });
    }
  }
}

export const messageController = new MessageController();
