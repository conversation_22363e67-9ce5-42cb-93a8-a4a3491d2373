import * as React from "react"
import { <PERSON> } from "react-router-dom"
import { cn } from "@/lib/utils"

interface FallbackAvatarProps {
  name: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  username?: string; // Optional username for navigation
  onClick?: () => void; // Optional click handler
}

/**
 * A custom avatar component that always shows the fallback with the first letter of the name
 * This is useful when you want to ensure the fallback is always shown, regardless of whether an image is provided
 * When username is provided, clicking the avatar will navigate to the user's channel page
 */
export function FallbackAvatar({ name, className, size = 'md', username, onClick }: FallbackAvatarProps) {
  // Get the first letter of the name
  const firstLetter = name && name.length > 0 ? name[0].toUpperCase() : '?';

  // Determine size class
  const sizeClass =
    size === 'sm' ? 'h-8 w-8 text-sm' :
    size === 'lg' ? 'h-12 w-12 text-lg' :
    'h-10 w-10 text-base';

  // Create the avatar content
  const avatarContent = (
    <div
      className={cn(
        "flex items-center justify-center rounded-full bg-gray-100 text-black font-medium border border-black cursor-pointer",
        sizeClass,
        className
      )}
      onClick={onClick}
    >
      {firstLetter}
    </div>
  );

  // If username is provided, wrap in Link component for navigation
  if (username) {
    return (
      <Link to={`/channel/${username}`}>
        {avatarContent}
      </Link>
    );
  }

  // Otherwise, return the avatar content directly
  return avatarContent;
}
