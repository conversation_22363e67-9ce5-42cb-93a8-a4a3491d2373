import { FastifyRequest, FastifyReply } from 'fastify';
import { UserModel } from '../models';
import { AuthenticatedUser } from '../types/user';

// Interface for conversation object
interface ConversationWithUser {
  id: string;
  userId: string;
  creatorId: string;
  lastMessageAt: Date;
}

/**
 * User search controller for handling user search requests
 */
export class UserSearchController {
  /**
   * Helper method to search users
   */
  // Completely rewrite the search function to be simpler
  async searchUsers(
    request: FastifyRequest<{
      Querystring: {
        query: string;
        page?: number;
        limit?: number;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const currentUserId = (request.user as AuthenticatedUser).id;
      const { query, page = 1, limit = 10 } = request.query;

      if (!query || query.length < 2) {
        return reply.code(400).send({
          success: false,
          message: 'Search query must be at least 2 characters',
        });
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Create search regex
      const searchRegex = new RegExp(query, 'i');

      console.log(`Searching for users with query: ${query}, currentUserId: ${currentUserId}`);

      try {
        // First, let's check if we can find any users at all to verify the collection is accessible
        const totalUsers = await UserModel.countDocuments({});
        console.log(`Total users in database: ${totalUsers}`);

        // Now try the actual search query with a simpler approach
        const users = await UserModel.find(
          {
            $or: [
              { username: searchRegex },
              { email: searchRegex },
              { displayName: searchRegex },
              { firstName: searchRegex },
              { lastName: searchRegex },
            ],
          },
          'id username displayName firstName lastName email avatar'
        )
          .sort({ displayName: 1 })
          .skip(skip)
          .limit(limit)
          .lean();

        // Add default presence field to users that don't have it
        const processedUsers = users.map(user => ({
          ...user,
          presence: {
            isOnline: false,
            lastActiveAt: new Date().toISOString()
          }
        }));

        console.log(`Found ${processedUsers.length} users matching query: ${query}`);

        // Get total count for pagination
        const total = await UserModel.countDocuments({
          $or: [
            { username: searchRegex },
            { email: searchRegex },
            { displayName: searchRegex },
            { firstName: searchRegex },
            { lastName: searchRegex },
          ],
        });

        return reply.code(200).send({
          success: true,
          data: processedUsers,
          pagination: {
            total,
            page,
            limit,
            pages: Math.ceil(total / limit),
          },
        });
      } catch (error) {
        console.error('Error in search query:', error);
        throw error;
      }
    } catch (error: any) {
      request.log.error(error);
      console.error('Error in searchUsers:', error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to search users: ' + (error.message || 'Unknown error'),
        error: error.message || 'Unknown error',
      });
    }
  }



  /**
   * Get recent chat users
   */
  async getRecentChatUsers(
    request: FastifyRequest<{
      Querystring: {
        limit?: number;
      };
    }>,
    reply: FastifyReply
  ) {
    try {
      const currentUserId = (request.user as AuthenticatedUser).id;
      const { limit = 10 } = request.query;

      // Get user's role
      console.log(`Getting recent chat users for user: ${currentUserId}`);

      try {
        // First, check if we can find the current user
        const currentUser = await UserModel.findOne({ id: currentUserId });
        if (!currentUser) {
          console.log(`User ${currentUserId} not found in database`);
          return reply.code(404).send({
            success: false,
            message: 'User not found',
          });
        }

        const isCreator = currentUser?.roles?.includes('creator');
        console.log(`User ${currentUserId} is creator: ${isCreator}`);

        // Check if Conversation model exists
        if (!request.server.mongoose.models.Conversation) {
          console.log('Conversation model not found');
          return reply.code(200).send({
            success: true,
            data: [], // Return empty array instead of error
          });
        }

        // Find conversations for the current user
        const conversations = await (isCreator
          ? // If user is a creator, find conversations where they are the creator
            request.server.mongoose.model('Conversation').find(
              {
                creatorId: currentUserId,
                deletedAt: null,
                status: { $ne: 'closed' },
              },
              'userId lastMessageAt'
            )
              .sort({ lastMessageAt: -1 })
              .limit(limit)
              .lean()
          : // If user is not a creator, find conversations where they are the user
            request.server.mongoose.model('Conversation').find(
              {
                userId: currentUserId,
                deletedAt: null,
                status: { $ne: 'closed' },
              },
              'creatorId lastMessageAt'
            )
              .sort({ lastMessageAt: -1 })
              .limit(limit)
              .lean());

        console.log(`Found ${conversations.length} conversations for user ${currentUserId}`);

        if (conversations.length === 0) {
          return reply.code(200).send({
            success: true,
            data: [], // Return empty array if no conversations
          });
        }

        // Get user IDs from conversations
        const userIds = conversations.map((conv: ConversationWithUser) =>
          isCreator ? conv.userId : conv.creatorId
        );

        // Get user details
        const users = await UserModel.find(
          {
            id: { $in: userIds },
            deletedAt: null,
          },
          'id username displayName firstName lastName email avatar'
        ).lean();

        console.log(`Found ${users.length} users for these conversations`);

        // Map users to conversations and add default presence field
        const recentUsers = conversations.map((conv: ConversationWithUser) => {
          const userId = isCreator ? conv.userId : conv.creatorId;
          const user = users.find((u) => u.id === userId);

          if (!user) {
            console.log(`User ${userId} not found for conversation ${conv.id}`);
            return null;
          }

          return {
            ...user,
            presence: {
              isOnline: false,
              lastActiveAt: new Date().toISOString()
            },
            conversationId: conv.id,
            lastMessageAt: conv.lastMessageAt,
          };
        }).filter(Boolean);

        console.log(`Returning ${recentUsers.length} recent users`);

        return reply.code(200).send({
          success: true,
          data: recentUsers,
        });
      } catch (error) {
        console.error('Error getting recent chat users:', error);
        throw error;
      }
    } catch (error: any) {
      request.log.error(error);
      return reply.code(500).send({
        success: false,
        message: 'Failed to get recent chat users',
        error: error.message || 'Unknown error',
      });
    }
  }
}

export const userSearchController = new UserSearchController();
