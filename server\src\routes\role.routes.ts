import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import roleController from '../controllers/role.controller';
import {
  createRoleSchema,
  getAllRolesSchema,
  getRoleByIdSchema,
  updateRoleSchema,
  deleteRoleSchema,
  assignRoleToUserSchema,
} from '../schemas/role.schema';
import { authenticate } from '../middleware/auth';
import { checkPermission } from '../middleware/rbac';
import { adminActionLogger } from '../middleware/auditLogger';

/**
 * Role routes
 */
export default async function roleRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // All role routes require authentication
  fastify.register(async (fastify: FastifyInstance) => {
    // Apply authentication middleware to all routes in this context
    fastify.addHook('preHandler', authenticate);

    // Routes for reading roles (requires basic permission)
    fastify.register(async (fastify: FastifyInstance) => {
      fastify.addHook('preHandler', checkPermission('role:read'));

      fastify.get('/', { schema: getAllRolesSchema }, roleController.getAllRoles as any);
      fastify.get('/:id', { schema: getRoleByIdSchema }, roleController.getRoleById as any);
      fastify.get('/user/:userId', roleController.getUserRoles as any);
    });

    // Routes for managing roles (requires admin permissions)
    fastify.register(async (fastify: FastifyInstance) => {
      fastify.post(
        '/',
        {
          schema: createRoleSchema,
          preHandler: [checkPermission('role:create'), adminActionLogger('create', 'role', 'Role creation')],
        },
        roleController.createRole as any
      );

      fastify.put(
        '/:id',
        {
          schema: updateRoleSchema,
          preHandler: [checkPermission('role:update'), adminActionLogger('update', 'role', 'Role update')],
        },
        roleController.updateRole as any
      );

      fastify.delete(
        '/:id',
        {
          schema: deleteRoleSchema,
          preHandler: [checkPermission('role:delete'), adminActionLogger('delete', 'role', 'Role deletion')],
        },
        roleController.deleteRole as any
      );

      fastify.post(
        '/assign/:userId',
        {
          schema: assignRoleToUserSchema,
          preHandler: [checkPermission('role:assign'), adminActionLogger('assign', 'role', 'Role assignment to user')],
        },
        roleController.assignRolesToUser as any
      );
    });
  });
}
