import { config } from 'dotenv';
import axios from 'axios';

// Load environment variables
config();

// API base URL
const API_URL = process.env.API_URL || 'http://localhost:3000/api/v1';

/**
 * Test authentication flow
 */
async function testAuth() {
  try {
    console.log('🔑 Testing authentication flow...');
    
    // Step 1: Register a new user
    console.log('\n1. Registering a new user...');
    const registerResponse = await axios.post(`${API_URL}/users/register`, {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!',
      firstName: 'Test',
      lastName: 'User',
      displayName: 'Test User',
    });
    
    console.log('✅ User registered successfully');
    console.log('User ID:', registerResponse.data.user.id);
    console.log('Verification Token:', registerResponse.data.verificationToken);
    
    // Step 2: Verify email
    console.log('\n2. Verifying email...');
    const verifyResponse = await axios.get(`${API_URL}/auth/verify-email/${registerResponse.data.verificationToken}`);
    
    console.log('✅ Email verified successfully');
    
    // Step 3: Login
    console.log('\n3. Logging in...');
    const loginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'Password123!',
    });
    
    console.log('✅ Login successful');
    console.log('Access Token:', loginResponse.data.accessToken);
    console.log('Refresh Token:', loginResponse.data.refreshToken);
    
    // Step 4: Get user profile
    console.log('\n4. Getting user profile...');
    const profileResponse = await axios.get(`${API_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${loginResponse.data.accessToken}`,
      },
    });
    
    console.log('✅ Profile retrieved successfully');
    console.log('User:', profileResponse.data.user);
    
    // Step 5: Refresh token
    console.log('\n5. Refreshing token...');
    const refreshResponse = await axios.post(`${API_URL}/auth/refresh-token`, {
      refreshToken: loginResponse.data.refreshToken,
    });
    
    console.log('✅ Token refreshed successfully');
    console.log('New Access Token:', refreshResponse.data.accessToken);
    
    // Step 6: Request password reset
    console.log('\n6. Requesting password reset...');
    const resetRequestResponse = await axios.post(`${API_URL}/auth/forgot-password`, {
      email: '<EMAIL>',
    });
    
    console.log('✅ Password reset requested successfully');
    console.log('Reset Token:', resetRequestResponse.data.resetToken);
    
    // Step 7: Reset password
    console.log('\n7. Resetting password...');
    const resetResponse = await axios.post(`${API_URL}/auth/reset-password`, {
      token: resetRequestResponse.data.resetToken,
      newPassword: 'NewPassword123!',
      confirmPassword: 'NewPassword123!',
    });
    
    console.log('✅ Password reset successfully');
    
    // Step 8: Login with new password
    console.log('\n8. Logging in with new password...');
    const newLoginResponse = await axios.post(`${API_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'NewPassword123!',
    });
    
    console.log('✅ Login with new password successful');
    console.log('Access Token:', newLoginResponse.data.accessToken);
    
    console.log('\n🎉 Authentication flow test completed successfully!');
  } catch (error) {
    console.error('❌ Error testing authentication flow:', error.response?.data || error.message);
  }
}

// Run the test
testAuth();
