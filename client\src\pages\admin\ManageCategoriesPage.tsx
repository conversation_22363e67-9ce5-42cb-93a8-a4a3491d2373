import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, Pencil, Trash2, X, Plus, Search, ChevronRight, Filter, AlertCircle } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Mock data for categories
const mockCategories = [
  { id: '1', name: 'Film & Animation' },
  { id: '3', name: 'Music' },
  { id: '4', name: 'Pets & Animals' },
  { id: '5', name: 'Sports' },
  { id: '6', name: 'Travel & Events' },
  { id: '7', name: 'Gaming' },
  { id: '8', name: 'People & Blogs' },
];

// Language options for adding categories
const languages = [
  'English', 'Arabic', 'Dutch', 'French', 'German', 'Russian',
  'Spanish', 'Turkish', 'Hindi', 'Chinese', 'Urdu', 'Indonesian',
  'Croatian', 'Hebrew', 'Bengali', 'Japanese', 'Portuguese', 'Italian',
  'Persian', 'Swedish', 'Vietnamese', 'Danish', 'Filipino'
];

export default function ManageCategoriesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [categories, setCategories] = useState(mockCategories);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [categoryInputs, setCategoryInputs] = useState<Record<string, string>>({});
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<{id: string, translations: Record<string, string>} | null>(null);

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle category selection
  const handleSelectCategory = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedCategories([...selectedCategories, id]);
    } else {
      setSelectedCategories(selectedCategories.filter(categoryId => categoryId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCategories(filteredCategories.map(category => category.id));
    } else {
      setSelectedCategories([]);
    }
  };

  // Handle edit category
  const handleEdit = (id: string) => {
    const category = categories.find(cat => cat.id === id);
    if (category) {
      // For this demo, we'll just use the same name for all languages
      // In a real app, you would fetch translations from the backend
      const translations: Record<string, string> = {};
      languages.forEach(lang => {
        translations[lang] = category.name;
      });

      // Set some example translations for demo purposes
      translations['Arabic'] = 'فيلم والرسوم المتحركة';
      translations['Dutch'] = 'Film & Animatie';
      translations['French'] = 'Film et animation';
      translations['German'] = 'Film & Animation';
      translations['Russian'] = 'Фильм и анимация';
      translations['Spanish'] = 'Cine y Animación';
      translations['Portuguese'] = 'Filme e animação';
      translations['Italian'] = 'Film e animazione';
      translations['Persian'] = 'فیلم و انیمیشن';
      translations['Swedish'] = 'Film & Animation';
      translations['Vietnamese'] = 'Phim & Hoạt hình';
      translations['Danish'] = 'Film og animation';
      translations['Filipino'] = 'Pelikula at Animasyon';

      setEditingCategory({ id, translations });
      setEditModalOpen(true);
    }
  };

  // Handle save edited category
  const handleSaveEdit = () => {
    if (editingCategory) {
      const updatedCategories = categories.map(category => {
        if (category.id === editingCategory.id) {
          // In a real app, you would save all translations
          // Here we just update the English name for simplicity
          return { ...category, name: editingCategory.translations['English'] || category.name };
        }
        return category;
      });

      setCategories(updatedCategories);
      setEditModalOpen(false);
      setEditingCategory(null);
    }
  };

  // Handle edit input change
  const handleEditInputChange = (language: string, value: string) => {
    if (editingCategory) {
      setEditingCategory({
        ...editingCategory,
        translations: {
          ...editingCategory.translations,
          [language]: value
        }
      });
    }
  };

  // Handle delete category
  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      setCategories(categories.filter(category => category.id !== id));
    }
  };

  // Handle delete selected categories
  const handleDeleteSelected = () => {
    if (selectedCategories.length === 0) return;

    if (window.confirm('Are you sure you want to delete the selected categories?')) {
      setCategories(categories.filter(category => !selectedCategories.includes(category.id)));
      setSelectedCategories([]);
    }
  };

  // Handle category input change
  const handleCategoryInputChange = (language: string, value: string) => {
    setCategoryInputs({
      ...categoryInputs,
      [language]: value
    });
  };

  // Handle add category
  const handleAddCategory = () => {
    // Check if at least one language has a value
    const hasValue = Object.values(categoryInputs).some(value => value.trim() !== '');

    if (!hasValue) {
      alert('Please enter at least one category name');
      return;
    }

    // Add new category (using English name or first non-empty value)
    const newCategoryName = categoryInputs['English'] || Object.values(categoryInputs).find(value => value.trim() !== '') || '';

    const newCategory = {
      id: (Math.max(...categories.map(c => parseInt(c.id)), 0) + 1).toString(),
      name: newCategoryName
    };

    setCategories([...categories, newCategory]);
    setCategoryInputs({});
  };

  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Show success alert
  const displaySuccessAlert = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);
  };

  // Handle add category with success message
  const handleAddCategoryWithFeedback = () => {
    handleAddCategory();
    displaySuccessAlert('Category added successfully!');
  };

  // Handle delete with success message
  const handleDeleteWithFeedback = (id: string) => {
    handleDelete(id);
    displaySuccessAlert('Category deleted successfully!');
  };

  // Handle delete selected with success message
  const handleDeleteSelectedWithFeedback = () => {
    handleDeleteSelected();
    displaySuccessAlert('Selected categories deleted successfully!');
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Categories</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Manage Categories</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="manage" className="w-full">
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="add">Add Category</TabsTrigger>
                <TabsTrigger value="manage">Manage Categories</TabsTrigger>
              </TabsList>

              <TabsContent value="add">
                <Card>
                  <CardHeader>
                    <CardTitle>Add New Category</CardTitle>
                    <CardDescription>Create a new category with translations for multiple languages</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {languages.map((language, index) => (
                        <div key={index} className="space-y-2">
                          <label className="text-sm font-medium">{language}</label>
                          <Input
                            value={categoryInputs[language] || ''}
                            onChange={(e) => handleCategoryInputChange(language, e.target.value)}
                            placeholder={`${language} name...`}
                          />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={handleAddCategoryWithFeedback}
                      className="gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Add Category
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="manage">
                <Card>
                  <CardHeader>
                    <CardTitle>Manage Categories</CardTitle>
                    <CardDescription>View, edit, and delete categories</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search categories..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>

                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-12">
                              <Checkbox
                                checked={selectedCategories.length === filteredCategories.length && filteredCategories.length > 0}
                                onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                              />
                            </TableHead>
                            <TableHead>ID</TableHead>
                            <TableHead>Category Name</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredCategories.length > 0 ? (
                            filteredCategories.map((category) => (
                              <TableRow key={category.id}>
                                <TableCell>
                                  <Checkbox
                                    checked={selectedCategories.includes(category.id)}
                                    onCheckedChange={(checked) => handleSelectCategory(category.id, checked as boolean)}
                                  />
                                </TableCell>
                                <TableCell className="font-medium">{category.id}</TableCell>
                                <TableCell>
                                  <Badge variant="outline" className="font-normal">
                                    {category.name}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="flex justify-end gap-2">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleEdit(category.id)}
                                      className="h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                                    >
                                      <Pencil className="h-4 w-4" />
                                      <span className="sr-only">Edit</span>
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleDeleteWithFeedback(category.id)}
                                      className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                      <span className="sr-only">Delete</span>
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                                No categories found
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>

                    {selectedCategories.length > 0 && (
                      <div className="flex items-center justify-between bg-muted/30 p-3 rounded-md">
                        <div className="text-sm">
                          <span className="font-medium">{selectedCategories.length}</span> {selectedCategories.length === 1 ? 'category' : 'categories'} selected
                        </div>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={handleDeleteSelectedWithFeedback}
                          className="gap-2"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete Selected
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>

      {/* Edit Category Modal */}
      <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>
              Update category translations for different languages
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4 max-h-[60vh] overflow-y-auto">
            {editingCategory && languages.map((language, index) => (
              <div key={index} className="space-y-2">
                <label htmlFor={`edit-${language}`} className="text-sm font-medium">
                  {language}
                </label>
                <Input
                  id={`edit-${language}`}
                  value={editingCategory.translations[language] || ''}
                  onChange={(e) => handleEditInputChange(language, e.target.value)}
                  placeholder={`${language} name...`}
                />
              </div>
            ))}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleSaveEdit();
                displaySuccessAlert('Category updated successfully!');
              }}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
