import React, { useEffect, useState, useRef } from 'react';
import { useTour } from './TourProvider';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { X, ChevronLeft, ChevronRight, Skip } from 'lucide-react';
import { useLanguage } from '@/context/LanguageContext';

interface TourOverlayProps {
  className?: string;
}

export function TourOverlay({ className }: TourOverlayProps) {
  const { currentTour, currentStepIndex, isActive, nextStep, previousStep, skipTour, stopTour } = useTour();
  const { t } = useLanguage();
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [overlayPosition, setOverlayPosition] = useState({ top: 0, left: 0 });
  const overlayRef = useRef<HTMLDivElement>(null);

  // Get current step
  const currentStep = currentTour?.steps[currentStepIndex];

  // Find target element and calculate position
  useEffect(() => {
    if (!isActive || !currentStep) {
      setTargetElement(null);
      return;
    }

    const findTargetElement = () => {
      const element = document.querySelector(currentStep.target) as HTMLElement;
      if (element) {
        setTargetElement(element);
        calculateOverlayPosition(element);
        
        // Scroll element into view
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'center'
        });
        
        // Highlight the target element
        highlightElement(element);
      } else {
        // If target not found, position overlay in center
        setTargetElement(null);
        setOverlayPosition({
          top: window.innerHeight / 2 - 150,
          left: window.innerWidth / 2 - 200
        });
      }
    };

    // Delay to ensure DOM is ready
    const timer = setTimeout(findTargetElement, 100);
    return () => clearTimeout(timer);
  }, [isActive, currentStep]);

  // Calculate overlay position based on target element and placement
  const calculateOverlayPosition = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    const placement = currentStep?.placement || 'bottom';
    const overlayWidth = 400;
    const overlayHeight = 200;
    const offset = 20;

    let top = 0;
    let left = 0;

    switch (placement) {
      case 'top':
        top = rect.top - overlayHeight - offset;
        left = rect.left + (rect.width / 2) - (overlayWidth / 2);
        break;
      case 'bottom':
        top = rect.bottom + offset;
        left = rect.left + (rect.width / 2) - (overlayWidth / 2);
        break;
      case 'left':
        top = rect.top + (rect.height / 2) - (overlayHeight / 2);
        left = rect.left - overlayWidth - offset;
        break;
      case 'right':
        top = rect.top + (rect.height / 2) - (overlayHeight / 2);
        left = rect.right + offset;
        break;
    }

    // Ensure overlay stays within viewport
    top = Math.max(10, Math.min(top, window.innerHeight - overlayHeight - 10));
    left = Math.max(10, Math.min(left, window.innerWidth - overlayWidth - 10));

    setOverlayPosition({ top, left });
  };

  // Highlight target element
  const highlightElement = (element: HTMLElement) => {
    // Remove previous highlights
    document.querySelectorAll('.tour-highlight').forEach(el => {
      el.classList.remove('tour-highlight');
    });

    // Add highlight to current element
    element.classList.add('tour-highlight');
  };

  // Remove highlights when tour ends
  useEffect(() => {
    if (!isActive) {
      document.querySelectorAll('.tour-highlight').forEach(el => {
        el.classList.remove('tour-highlight');
      });
    }
  }, [isActive]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (targetElement) {
        calculateOverlayPosition(targetElement);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [targetElement, currentStep]);

  // Don't render if tour is not active
  if (!isActive || !currentTour || !currentStep) {
    return null;
  }

  const progress = ((currentStepIndex + 1) / currentTour.steps.length) * 100;
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === currentTour.steps.length - 1;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 z-40 tour-backdrop" />
      
      {/* Tour overlay */}
      <div
        ref={overlayRef}
        className={`fixed z-50 tour-overlay ${className || ''}`}
        style={{
          top: overlayPosition.top,
          left: overlayPosition.left,
          maxWidth: '400px',
          minWidth: '300px'
        }}
      >
        <Card className="bg-white dark:bg-gray-800 shadow-2xl border-2 border-blue-500">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                {currentStep.title}
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={stopTour}
                className="h-6 w-6 p-0 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Progress bar */}
            {currentTour.showProgress && (
              <div className="mt-2">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                  <span>Step {currentStepIndex + 1} of {currentTour.steps.length}</span>
                  <span>{Math.round(progress)}%</span>
                </div>
                <Progress value={progress} className="h-1" />
              </div>
            )}
          </CardHeader>
          
          <CardContent className="pt-0">
            <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
              {currentStep.content}
            </p>
            
            {/* Navigation buttons */}
            <div className="flex items-center justify-between">
              <div className="flex gap-2">
                {/* Previous button */}
                {currentStep.showPrevious && !isFirstStep && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={previousStep}
                    className="flex items-center gap-1"
                  >
                    <ChevronLeft className="h-3 w-3" />
                    Previous
                  </Button>
                )}
              </div>
              
              <div className="flex gap-2">
                {/* Skip button */}
                {currentStep.showSkip && currentTour.allowSkip && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={skipTour}
                    className="flex items-center gap-1 text-gray-500 hover:text-gray-700"
                  >
                    <Skip className="h-3 w-3" />
                    Skip Tour
                  </Button>
                )}
                
                {/* Next/Finish button */}
                {currentStep.showNext !== false && (
                  <Button
                    size="sm"
                    onClick={nextStep}
                    className="flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    {isLastStep ? 'Finish' : 'Next'}
                    {!isLastStep && <ChevronRight className="h-3 w-3" />}
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Pointer arrow */}
        {targetElement && (
          <div
            className={`absolute w-0 h-0 border-8 ${
              currentStep.placement === 'top' ? 'border-t-blue-500 border-x-transparent border-b-transparent top-full left-1/2 transform -translate-x-1/2' :
              currentStep.placement === 'bottom' ? 'border-b-blue-500 border-x-transparent border-t-transparent bottom-full left-1/2 transform -translate-x-1/2' :
              currentStep.placement === 'left' ? 'border-l-blue-500 border-y-transparent border-r-transparent left-full top-1/2 transform -translate-y-1/2' :
              'border-r-blue-500 border-y-transparent border-l-transparent right-full top-1/2 transform -translate-y-1/2'
            }`}
          />
        )}
      </div>
      
      {/* Tour styles */}
      <style jsx global>{`
        .tour-highlight {
          position: relative;
          z-index: 45;
          box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5), 0 0 0 8px rgba(59, 130, 246, 0.2);
          border-radius: 4px;
          transition: box-shadow 0.3s ease;
        }
        
        .tour-backdrop {
          backdrop-filter: blur(1px);
        }
        
        .tour-overlay {
          animation: tourFadeIn 0.3s ease-out;
        }
        
        @keyframes tourFadeIn {
          from {
            opacity: 0;
            transform: scale(0.95);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </>
  );
}

export default TourOverlay;
