import { VideoModel, UserModel } from '../models';

/**
 * Seed videos data
 */
async function seedVideos() {
  try {
    // Clear existing videos
    await VideoModel.deleteMany({});
    
    // Get creator user
    const creator = await UserModel.findOne({ username: 'creator' });
    if (!creator) {
      throw new Error('Creator user not found. Please seed users first.');
    }
    
    // Define videos to seed
    const videos = [
      {
        title: 'Understanding Contract Law: The Basics',
        description: 'This video covers the fundamental principles of contract law, including offer, acceptance, consideration, and intent. Learn what makes a contract legally binding and how to avoid common pitfalls when entering into agreements.',
        url: 'https://example.com/videos/contract-law-basics.mp4',
        thumbnailUrl: 'https://example.com/thumbnails/contract-law-basics.jpg',
        duration: 1245, // 20:45 in seconds
        userId: creator.id,
        channelId: creator.id, // Using creator ID as channel ID for now
        visibility: 'public',
        tags: ['contract law', 'legal basics', 'agreements', 'legal education'],
        category: 'Business Law',
        contentRating: 'general',
        processingStatus: 'ready',
        commentsEnabled: true,
        ratingsEnabled: true,
        embeddingEnabled: true,
        stats: {
          views: 1250,
          likes: 87,
          dislikes: 4,
          comments: 23,
          shares: 15,
          playlistAdds: 45,
          averageWatchTime: 950,
          retentionRate: 76,
        },
        file: {
          originalName: 'contract-law-basics.mp4',
          size: 256000000, // 256MB
          mimeType: 'video/mp4',
          codec: 'h264',
          resolution: '1920x1080',
          bitrate: 5000,
          frameRate: 30,
        },
        variants: [
          {
            quality: '1080p',
            url: 'https://example.com/videos/contract-law-basics-1080p.mp4',
            resolution: '1920x1080',
            bitrate: 5000,
            size: 256000000,
          },
          {
            quality: '720p',
            url: 'https://example.com/videos/contract-law-basics-720p.mp4',
            resolution: '1280x720',
            bitrate: 2500,
            size: 128000000,
          },
          {
            quality: '480p',
            url: 'https://example.com/videos/contract-law-basics-480p.mp4',
            resolution: '854x480',
            bitrate: 1000,
            size: 64000000,
          },
        ],
        captions: [
          {
            language: 'en',
            label: 'English',
            url: 'https://example.com/captions/contract-law-basics-en.vtt',
            isAutoGenerated: false,
            isDefault: true,
          },
          {
            language: 'es',
            label: 'Spanish',
            url: 'https://example.com/captions/contract-law-basics-es.vtt',
            isAutoGenerated: true,
            isDefault: false,
          },
        ],
        monetization: {
          enabled: true,
          types: ['ads'],
          midrollEnabled: true,
          midrollTimestamps: [300, 600, 900],
        },
        chapters: [
          {
            title: 'Introduction',
            startTime: 0,
            endTime: 120,
            thumbnailUrl: 'https://example.com/thumbnails/contract-law-basics-ch1.jpg',
          },
          {
            title: 'Elements of a Contract',
            startTime: 121,
            endTime: 450,
            thumbnailUrl: 'https://example.com/thumbnails/contract-law-basics-ch2.jpg',
          },
          {
            title: 'Common Contract Issues',
            startTime: 451,
            endTime: 900,
            thumbnailUrl: 'https://example.com/thumbnails/contract-law-basics-ch3.jpg',
          },
          {
            title: 'Case Studies',
            startTime: 901,
            endTime: 1245,
            thumbnailUrl: 'https://example.com/thumbnails/contract-law-basics-ch4.jpg',
          },
        ],
        copyright: {
          owner: 'LawEngaxe Legal Education',
          license: 'Standard YouTube License',
          allowReuse: false,
          allowCommercialUse: false,
          allowModification: false,
          attributionRequired: true,
        },
        createdBy: creator.id,
        updatedBy: creator.id,
      },
      {
        title: 'Intellectual Property Rights Explained',
        description: 'A comprehensive guide to intellectual property rights including patents, trademarks, copyrights, and trade secrets. Learn how to protect your creative works and innovations in the digital age.',
        url: 'https://example.com/videos/intellectual-property-rights.mp4',
        thumbnailUrl: 'https://example.com/thumbnails/intellectual-property-rights.jpg',
        duration: 1830, // 30:30 in seconds
        userId: creator.id,
        channelId: creator.id, // Using creator ID as channel ID for now
        visibility: 'public',
        tags: ['intellectual property', 'copyright', 'trademark', 'patent', 'legal education'],
        category: 'Intellectual Property',
        contentRating: 'general',
        processingStatus: 'ready',
        commentsEnabled: true,
        ratingsEnabled: true,
        embeddingEnabled: true,
        stats: {
          views: 2100,
          likes: 145,
          dislikes: 8,
          comments: 37,
          shares: 28,
          playlistAdds: 62,
          averageWatchTime: 1500,
          retentionRate: 82,
        },
        file: {
          originalName: 'intellectual-property-rights.mp4',
          size: 320000000, // 320MB
          mimeType: 'video/mp4',
          codec: 'h264',
          resolution: '1920x1080',
          bitrate: 5000,
          frameRate: 30,
        },
        variants: [
          {
            quality: '1080p',
            url: 'https://example.com/videos/intellectual-property-rights-1080p.mp4',
            resolution: '1920x1080',
            bitrate: 5000,
            size: 320000000,
          },
          {
            quality: '720p',
            url: 'https://example.com/videos/intellectual-property-rights-720p.mp4',
            resolution: '1280x720',
            bitrate: 2500,
            size: 160000000,
          },
          {
            quality: '480p',
            url: 'https://example.com/videos/intellectual-property-rights-480p.mp4',
            resolution: '854x480',
            bitrate: 1000,
            size: 80000000,
          },
        ],
        captions: [
          {
            language: 'en',
            label: 'English',
            url: 'https://example.com/captions/intellectual-property-rights-en.vtt',
            isAutoGenerated: false,
            isDefault: true,
          },
          {
            language: 'fr',
            label: 'French',
            url: 'https://example.com/captions/intellectual-property-rights-fr.vtt',
            isAutoGenerated: true,
            isDefault: false,
          },
        ],
        monetization: {
          enabled: true,
          types: ['ads'],
          midrollEnabled: true,
          midrollTimestamps: [300, 600, 900, 1200, 1500],
        },
        chapters: [
          {
            title: 'Introduction to IP',
            startTime: 0,
            endTime: 180,
            thumbnailUrl: 'https://example.com/thumbnails/ip-rights-ch1.jpg',
          },
          {
            title: 'Copyright Protection',
            startTime: 181,
            endTime: 600,
            thumbnailUrl: 'https://example.com/thumbnails/ip-rights-ch2.jpg',
          },
          {
            title: 'Trademark Basics',
            startTime: 601,
            endTime: 1020,
            thumbnailUrl: 'https://example.com/thumbnails/ip-rights-ch3.jpg',
          },
          {
            title: 'Patent Process',
            startTime: 1021,
            endTime: 1440,
            thumbnailUrl: 'https://example.com/thumbnails/ip-rights-ch4.jpg',
          },
          {
            title: 'Trade Secrets & Conclusion',
            startTime: 1441,
            endTime: 1830,
            thumbnailUrl: 'https://example.com/thumbnails/ip-rights-ch5.jpg',
          },
        ],
        copyright: {
          owner: 'LawEngaxe Legal Education',
          license: 'Standard YouTube License',
          allowReuse: false,
          allowCommercialUse: false,
          allowModification: false,
          attributionRequired: true,
        },
        createdBy: creator.id,
        updatedBy: creator.id,
      },
      {
        title: 'Tenant Rights: What Every Renter Should Know',
        description: 'This video explains the legal rights and responsibilities of tenants. Learn about lease agreements, security deposits, repairs, eviction processes, and how to handle disputes with landlords.',
        url: 'https://example.com/videos/tenant-rights.mp4',
        thumbnailUrl: 'https://example.com/thumbnails/tenant-rights.jpg',
        duration: 1560, // 26:00 in seconds
        userId: creator.id,
        channelId: creator.id, // Using creator ID as channel ID for now
        visibility: 'public',
        tags: ['tenant rights', 'rental law', 'housing law', 'eviction', 'legal education'],
        category: 'Housing Law',
        contentRating: 'general',
        processingStatus: 'ready',
        commentsEnabled: true,
        ratingsEnabled: true,
        embeddingEnabled: true,
        stats: {
          views: 3500,
          likes: 210,
          dislikes: 12,
          comments: 85,
          shares: 120,
          playlistAdds: 95,
          averageWatchTime: 1200,
          retentionRate: 77,
        },
        file: {
          originalName: 'tenant-rights.mp4',
          size: 280000000, // 280MB
          mimeType: 'video/mp4',
          codec: 'h264',
          resolution: '1920x1080',
          bitrate: 5000,
          frameRate: 30,
        },
        variants: [
          {
            quality: '1080p',
            url: 'https://example.com/videos/tenant-rights-1080p.mp4',
            resolution: '1920x1080',
            bitrate: 5000,
            size: 280000000,
          },
          {
            quality: '720p',
            url: 'https://example.com/videos/tenant-rights-720p.mp4',
            resolution: '1280x720',
            bitrate: 2500,
            size: 140000000,
          },
          {
            quality: '480p',
            url: 'https://example.com/videos/tenant-rights-480p.mp4',
            resolution: '854x480',
            bitrate: 1000,
            size: 70000000,
          },
        ],
        captions: [
          {
            language: 'en',
            label: 'English',
            url: 'https://example.com/captions/tenant-rights-en.vtt',
            isAutoGenerated: false,
            isDefault: true,
          },
          {
            language: 'es',
            label: 'Spanish',
            url: 'https://example.com/captions/tenant-rights-es.vtt',
            isAutoGenerated: false,
            isDefault: false,
          },
        ],
        monetization: {
          enabled: true,
          types: ['ads'],
          midrollEnabled: true,
          midrollTimestamps: [300, 600, 900, 1200],
        },
        chapters: [
          {
            title: 'Introduction',
            startTime: 0,
            endTime: 120,
            thumbnailUrl: 'https://example.com/thumbnails/tenant-rights-ch1.jpg',
          },
          {
            title: 'Lease Agreements',
            startTime: 121,
            endTime: 480,
            thumbnailUrl: 'https://example.com/thumbnails/tenant-rights-ch2.jpg',
          },
          {
            title: 'Repairs & Maintenance',
            startTime: 481,
            endTime: 840,
            thumbnailUrl: 'https://example.com/thumbnails/tenant-rights-ch3.jpg',
          },
          {
            title: 'Eviction Process',
            startTime: 841,
            endTime: 1200,
            thumbnailUrl: 'https://example.com/thumbnails/tenant-rights-ch4.jpg',
          },
          {
            title: 'Dispute Resolution',
            startTime: 1201,
            endTime: 1560,
            thumbnailUrl: 'https://example.com/thumbnails/tenant-rights-ch5.jpg',
          },
        ],
        copyright: {
          owner: 'LawEngaxe Legal Education',
          license: 'Standard YouTube License',
          allowReuse: false,
          allowCommercialUse: false,
          allowModification: false,
          attributionRequired: true,
        },
        createdBy: creator.id,
        updatedBy: creator.id,
      },
    ];
    
    // Insert videos
    await VideoModel.insertMany(videos);
    
    console.log(`✅ ${videos.length} videos seeded successfully`);
  } catch (error) {
    console.error('❌ Error seeding videos:', error);
    throw error;
  }
}

export default seedVideos;
