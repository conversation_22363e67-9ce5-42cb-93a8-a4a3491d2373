import mongoose, { <PERSON>hem<PERSON>, Document } from 'mongoose';
import { IBaseEntity, BaseSchema } from './base.model';

/**
 * Role interface extending the base entity
 */
export interface IRole extends IBaseEntity {
  /** Human-readable name of the role */
  name: string;

  /** Detailed description explaining the purpose and scope of this role */
  description: string;

  /**
   * Unique role code used for programmatic access
   * Example: 'admin', 'moderator', 'subscriber'
   */
  code: string;

  /**
   * Array of permission codes granted to users with this role
   * References the 'code' field in the Permission model
   */
  permissions: string[];

  /**
   * Whether this role is a system role
   * System roles cannot be modified or deleted by regular administrators
   */
  isSystem: boolean;

  /**
   * Whether this role is currently active
   * Inactive roles are not assigned to new users
   */
  isActive: boolean;

  /**
   * Priority/rank of this role (lower number = higher priority)
   * Used for resolving permission conflicts between multiple roles
   */
  priority: number;

  /**
   * Parent role ID for hierarchical role structures
   * Child roles inherit all permissions from parent roles
   */
  parentId?: string;

  /**
   * Maximum number of users that can have this role
   * Useful for limiting premium or special roles
   */
  maxUsers?: number;

  /**
   * Whether this role can be assigned to users
   * Some roles might be for organizational purposes only
   */
  isAssignable: boolean;

  /**
   * Auto-assignment conditions
   * JSON string defining conditions when this role should be automatically assigned
   */
  autoAssignConditions?: string;
}

/**
 * Role schema definition
 */
const RoleSchema = new Schema<IRole>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
    },
    code: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      match: /^[a-z0-9_.-]+$/,
    },
    permissions: {
      type: [String],
      default: [],
    },
    isSystem: {
      type: Boolean,
      default: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    priority: {
      type: Number,
      default: 100,
    },
    parentId: {
      type: String,
      ref: 'Role',
    },
    maxUsers: {
      type: Number,
    },
    isAssignable: {
      type: Boolean,
      default: true,
    },
    autoAssignConditions: {
      type: String,
    },
  }
);

// Merge with base schema
RoleSchema.add(BaseSchema);

// Add compound index for code with soft delete support
RoleSchema.index({ code: 1, deletedAt: 1 }, { unique: true, sparse: true });

// Create and export the Role model
const RoleModel = mongoose.model<IRole>('Role', RoleSchema);
export default RoleModel;
