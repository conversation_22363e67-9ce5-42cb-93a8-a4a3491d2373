import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, ChevronRight, Bold, Italic, AlignLeft, AlignCenter, AlignRight, AlignJustify, List, ListOrdered, Undo, Redo, Link2, Image, Type, Palette, Smile, IndentDecrease, IndentIncrease, Save, AlertCircle, MessageSquare, Bell, BellOff, Calendar, Clock, Trash2, Pencil } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function AnnouncementsPage() {
  const [editorContent, setEditorContent] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const [activeTab, setActiveTab] = useState('create');
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [textFormat, setTextFormat] = useState<string[]>([]);
  const [textAlign, setTextAlign] = useState<string>('left');
  const [announcementTitle, setAnnouncementTitle] = useState('');
  const [announcementStatus, setAnnouncementStatus] = useState('active');

  // Mock announcements data
  const mockActiveAnnouncements = [
    { id: '1', title: 'Platform Maintenance', content: 'Scheduled maintenance on July 15th', date: '2023-07-10' },
    { id: '2', title: 'New Feature Release', content: 'Exciting new features coming soon!', date: '2023-07-05' },
  ];

  const mockInactiveAnnouncements = [
    { id: '3', title: 'Holiday Schedule', content: 'Office closed during holidays', date: '2023-06-20' },
    { id: '4', title: 'System Update', content: 'System updated to version 2.0', date: '2023-06-15' },
  ];

  const handleEditorChange = (content: string) => {
    setEditorContent(content);
    // Count words (simple implementation)
    const text = content.replace(/<[^>]*>/g, '');
    const words = text.trim().split(/\s+/);
    setWordCount(words.length === 1 && words[0] === '' ? 0 : words.length);
  };

  const handleCreateAnnouncement = () => {
    // In a real app, you would send the data to an API
    console.log({
      title: announcementTitle,
      content: editorContent,
      status: announcementStatus
    });
    
    // Show success message
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);
    
    // Reset form
    setAnnouncementTitle('');
    setEditorContent('');
    setAnnouncementStatus('active');
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Announcements</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Tools
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Announcements</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  Announcement has been created successfully.
                </AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="create" className="w-full" onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-3 mb-6">
                <TabsTrigger value="create">Create Announcement</TabsTrigger>
                <TabsTrigger value="active">Active Announcements</TabsTrigger>
                <TabsTrigger value="inactive">Inactive Announcements</TabsTrigger>
              </TabsList>

              <TabsContent value="create">
                <Card>
                  <CardHeader>
                    <CardTitle>Create New Announcement</CardTitle>
                    <CardDescription>Create and publish announcements for your platform users</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="title">Announcement Title</Label>
                      <Input 
                        id="title" 
                        placeholder="Enter announcement title" 
                        value={announcementTitle}
                        onChange={(e) => setAnnouncementTitle(e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="status">Status</Label>
                      <Select value={announcementStatus} onValueChange={setAnnouncementStatus}>
                        <SelectTrigger id="status">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                          <SelectItem value="scheduled">Scheduled</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Content</Label>
                      <Card className="border rounded-md">
                        {/* Editor Toolbar */}
                        <div className="border-b p-2 flex flex-wrap gap-2">
                          <Select defaultValue="paragraph">
                            <SelectTrigger className="w-[150px]">
                              <SelectValue placeholder="Format" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="paragraph">Paragraph</SelectItem>
                              <SelectItem value="heading1">Heading 1</SelectItem>
                              <SelectItem value="heading2">Heading 2</SelectItem>
                              <SelectItem value="heading3">Heading 3</SelectItem>
                              <SelectItem value="code">Code</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="border-b p-2">
                          <ToggleGroup type="multiple" value={textFormat} onValueChange={setTextFormat}>
                            <ToggleGroupItem value="bold" aria-label="Toggle bold">
                              <Bold className="h-4 w-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="italic" aria-label="Toggle italic">
                              <Italic className="h-4 w-4" />
                            </ToggleGroupItem>
                            <Separator orientation="vertical" className="mx-1 h-6" />
                            <ToggleGroupItem value="link" aria-label="Toggle link">
                              <Link2 className="h-4 w-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="image" aria-label="Toggle image">
                              <Image className="h-4 w-4" />
                            </ToggleGroupItem>
                          </ToggleGroup>
                        </div>

                        <div className="border-b p-2">
                          <ToggleGroup type="single" value={textAlign} onValueChange={(value) => value && setTextAlign(value)}>
                            <ToggleGroupItem value="left" aria-label="Align left">
                              <AlignLeft className="h-4 w-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="center" aria-label="Align center">
                              <AlignCenter className="h-4 w-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="right" aria-label="Align right">
                              <AlignRight className="h-4 w-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="justify" aria-label="Justify">
                              <AlignJustify className="h-4 w-4" />
                            </ToggleGroupItem>
                          </ToggleGroup>
                        </div>

                        <div className="border-b p-2">
                          <ToggleGroup type="multiple">
                            <ToggleGroupItem value="bulletList" aria-label="Bullet list">
                              <List className="h-4 w-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="numberedList" aria-label="Numbered list">
                              <ListOrdered className="h-4 w-4" />
                            </ToggleGroupItem>
                            <Separator orientation="vertical" className="mx-1 h-6" />
                            <ToggleGroupItem value="decreaseIndent" aria-label="Decrease indent">
                              <IndentDecrease className="h-4 w-4" />
                            </ToggleGroupItem>
                            <ToggleGroupItem value="increaseIndent" aria-label="Increase indent">
                              <IndentIncrease className="h-4 w-4" />
                            </ToggleGroupItem>
                          </ToggleGroup>
                        </div>

                        {/* Editor Content Area */}
                        <div className="p-4 min-h-[200px]">
                          <Textarea
                            className="w-full h-[200px] border-0 focus:outline-none resize-none"
                            value={editorContent}
                            onChange={(e) => handleEditorChange(e.target.value)}
                            placeholder="Write your announcement here..."
                          />
                        </div>

                        {/* Word Count */}
                        <div className="p-2 text-xs text-muted-foreground border-t flex justify-between">
                          <span>{wordCount} words</span>
                          <span>Rich Text Editor</span>
                        </div>
                      </Card>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button onClick={handleCreateAnnouncement} className="gap-2">
                      <Save className="h-4 w-4" />
                      Create Announcement
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="active">
                <Card>
                  <CardHeader>
                    <CardTitle>Active Announcements</CardTitle>
                    <CardDescription>Currently active announcements visible to users</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {mockActiveAnnouncements.length > 0 ? (
                      <div className="space-y-4">
                        {mockActiveAnnouncements.map((announcement) => (
                          <Card key={announcement.id} className="overflow-hidden">
                            <div className="p-4 border-b bg-muted/30 flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <Bell className="h-4 w-4 text-primary" />
                                <h3 className="font-medium">{announcement.title}</h3>
                                <Badge variant="outline" className="ml-2">Active</Badge>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <CardContent className="p-4">
                              <p className="text-sm text-muted-foreground mb-2">{announcement.content}</p>
                              <div className="flex items-center gap-4 text-xs text-muted-foreground mt-4">
                                <div className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  <span>{announcement.date}</span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                        <p>No active announcements found</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="inactive">
                <Card>
                  <CardHeader>
                    <CardTitle>Inactive Announcements</CardTitle>
                    <CardDescription>Announcements that are not currently visible to users</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {mockInactiveAnnouncements.length > 0 ? (
                      <div className="space-y-4">
                        {mockInactiveAnnouncements.map((announcement) => (
                          <Card key={announcement.id} className="overflow-hidden">
                            <div className="p-4 border-b bg-muted/30 flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <BellOff className="h-4 w-4 text-muted-foreground" />
                                <h3 className="font-medium">{announcement.title}</h3>
                                <Badge variant="outline" className="ml-2 bg-muted">Inactive</Badge>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button variant="ghost" size="icon" className="h-8 w-8">
                                  <Pencil className="h-4 w-4" />
                                </Button>
                                <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <CardContent className="p-4">
                              <p className="text-sm text-muted-foreground mb-2">{announcement.content}</p>
                              <div className="flex items-center gap-4 text-xs text-muted-foreground mt-4">
                                <div className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  <span>{announcement.date}</span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <MessageSquare className="h-12 w-12 mx-auto mb-2 opacity-20" />
                        <p>No inactive announcements found</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  );
}
