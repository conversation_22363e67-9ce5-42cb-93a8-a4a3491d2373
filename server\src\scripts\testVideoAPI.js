// <PERSON>ript to test the video API directly
// Run with: node testVideoAPI.js

const axios = require('axios');

// The token to use for authentication
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************.Kv_B7h1qYGroD7rbxf7Ua058hehyA5qIO7dsa9YkYIY';

// The video data to send
const videoData = {
  title: 'API Test Video',
  description: 'This is a test video created via direct API call',
  channelId: '956c2164-d429-4101-a6fb-c10c8cf6a62a',
  category: 'Education',
  tags: ['test', 'api'],
  visibility: 'public',
  contentRating: 'general',
  commentsEnabled: true,
  ratingsEnabled: true,
  embeddingEnabled: true,
  source: {
    type: 'embed',
    originalUrl: 'https://engaxe.com/videos/apitest123',
    platform: 'engaxe',
    externalId: 'apitest123'
  },
  thumbnailUrl: 'https://example.com/thumbnail.jpg',
  duration: 300,
  file: {
    originalName: 'test.mp4',
    size: 10485760,
    mimeType: 'video/mp4'
  }
};

// Test the upload endpoint
async function testUpload() {
  try {
    console.log('Testing /videos/upload endpoint...');
    const response = await axios.post('http://localhost:3002/api/v1/videos/upload', videoData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Upload response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Upload error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Test the import endpoint
async function testImport() {
  try {
    console.log('Testing /videos/import endpoint...');
    const response = await axios.post('http://localhost:3002/api/v1/videos/import', videoData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('Import response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Import error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Test the get videos endpoint
async function testGetVideos() {
  try {
    console.log('Testing /videos endpoint...');
    const response = await axios.get('http://localhost:3002/api/v1/videos', {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        limit: 10,
        sort: 'newest'
      }
    });

    console.log('Get videos response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Get videos error:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return null;
  }
}

// Run all tests
async function runTests() {
  console.log('Starting API tests...');

  // Test upload
  const uploadResult = await testUpload();

  // Test import
  const importResult = await testImport();

  // Test get videos
  const getVideosResult = await testGetVideos();

  console.log('Tests completed.');
}

// Run the tests
runTests();
