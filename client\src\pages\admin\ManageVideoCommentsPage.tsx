import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Search, Filter, Edit, Trash2, Eye, MoreVertical, MessageSquare, CheckCircle, XCircle, Home, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Sample comment data
const comments = [
  {
    id: 7,
    text: 'vary nic vdo mam',
    videoId: 'LeU5VBb2URXimJP',
    article: '',
    postedOn: 'January-08-2025'
  },
  {
    id: 2,
    text: 'VIDEO HAS BEEN QUEUED',
    videoId: 'AOZVIUVyiALkR4H',
    article: '',
    postedOn: 'July-18-2024'
  }
];

export default function ManageVideoCommentsPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [selectedComments, setSelectedComments] = useState<number[]>([]);

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  const handleSearch = () => {
    console.log('Searching for:', searchQuery);
    // Implement search functionality
  };

  const handleDeleteSelected = () => {
    console.log('Deleting selected comments:', selectedComments);
    // Implement delete functionality
  };

  const toggleCommentSelection = (id: number) => {
    if (selectedComments.includes(id)) {
      setSelectedComments(selectedComments.filter(commentId => commentId !== id));
    } else {
      setSelectedComments([...selectedComments, id]);
    }
  };

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <div className="p-6 flex-1 bg-gray-50">
          {/* Breadcrumb */}
          <div className="flex items-center text-sm mb-6">
            <Link to="/admin" className="text-blue-600 hover:underline flex items-center">
              <Home size={14} className="mr-1" />
              Admin Panel
            </Link>
            <ChevronRight size={14} className="mx-2 text-gray-500" />
            <span className="text-gray-600">Manage Comments</span>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6">
            <h1 className="text-2xl font-bold text-gray-800 mb-6">Manage Comments</h1>

            <div className="mb-6">
              <div className="flex justify-between items-center">
                <div className="w-full max-w-md">
                  <p className="text-sm text-gray-600 mb-2">Search for Keyword</p>
                  <div className="flex">
                    <input
                      type="text"
                      placeholder="Enter keyword..."
                      className="flex-1 p-2 border border-gray-300 rounded-l"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <Button
                      className="bg-blue-500 hover:bg-blue-600 text-white rounded-r rounded-l-none"
                      onClick={handleSearch}
                    >
                      Search
                    </Button>
                  </div>
                </div>

                <div className="relative">
                  <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                    <SelectTrigger className="w-32 bg-white">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="Today">Today</SelectItem>
                      <SelectItem value="Yesterday">Yesterday</SelectItem>
                      <SelectItem value="This Week">This Week</SelectItem>
                      <SelectItem value="This Month">This Month</SelectItem>
                      <SelectItem value="Last Month">Last Month</SelectItem>
                      <SelectItem value="This Year">This Year</SelectItem>
                      <SelectItem value="Custom Range">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="min-w-full bg-white border border-gray-200">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="w-12 px-4 py-3 border">
                      <input type="checkbox" className="h-4 w-4" />
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      ID <span className="text-gray-400">↑</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      TEXT <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      VIDEO <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      ARTICLES <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      POSTED ON <span className="text-gray-400">↓</span>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border">
                      ACTION
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {comments.map((comment) => (
                    <tr key={comment.id}>
                      <td className="px-4 py-3 whitespace-nowrap border">
                        <input
                          type="checkbox"
                          className="h-4 w-4"
                          checked={selectedComments.includes(comment.id)}
                          onChange={() => toggleCommentSelection(comment.id)}
                        />
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {comment.id}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {comment.text}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {comment.videoId}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {comment.article || '-'}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 border">
                        {comment.postedOn}
                      </td>
                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium border">
                        <button className="flex items-center px-2 py-1 bg-red-100 text-red-700 rounded text-xs">
                          <Trash2 size={12} className="mr-1" />
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            <div className="mt-6 flex flex-col md:flex-row justify-between items-start md:items-center">
              <div className="mb-4 md:mb-0">
                <p className="text-sm text-gray-600 mb-2">Showing 1 out of 1</p>
                <Button
                  className="bg-blue-500 hover:bg-blue-600 text-white"
                  onClick={handleDeleteSelected}
                  disabled={selectedComments.length === 0}
                >
                  Delete Selected
                </Button>
              </div>

              <div className="flex items-center space-x-1">
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &lt;&lt;
                </button>
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &lt;
                </button>
                <button className="w-8 h-8 flex items-center justify-center bg-blue-500 text-white rounded">
                  1
                </button>
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &gt;
                </button>
                <button className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded">
                  &gt;&gt;
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
