import { FastifySchema } from 'fastify';
import { Type } from '@sinclair/typebox';

/**
 * Toggle like schema
 */
export const ToggleLikeSchema: FastifySchema = {
  tags: ['Likes'],
  description: 'Toggle like for a video',
  params: Type.Object({
    videoId: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      isLiked: Type.Boolean(),
      likeCount: Type.Number(),
    }),
    500: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Has user liked schema
 */
export const HasUserLikedSchema: FastifySchema = {
  tags: ['Likes'],
  description: 'Check if a user has liked a video',
  params: Type.Object({
    videoId: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      isLiked: Type.Boolean(),
    }),
    500: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};

/**
 * Get like count schema
 */
export const GetLikeCountSchema: FastifySchema = {
  tags: ['Likes'],
  description: 'Get like count for a video',
  params: Type.Object({
    videoId: Type.String(),
  }),
  response: {
    200: Type.Object({
      success: Type.Boolean(),
      likeCount: Type.Number(),
    }),
    500: Type.Object({
      success: Type.Boolean(),
      message: Type.String(),
    }),
  },
};
