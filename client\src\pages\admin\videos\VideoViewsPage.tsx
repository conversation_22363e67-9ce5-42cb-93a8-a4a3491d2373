import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useTheme } from '@/context/ThemeContext';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Eye, ArrowUpDown, Download, Filter, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';

// Sample video views data
const videoViewsData = [
  {
    id: 1,
    videoId: 'h8SN9aB7J91Deb6',
    title: 'UNO: You Play! Interactive Video Game',
    views: 1245,
    uniqueViewers: 982,
    avgWatchTime: '4:32',
    completionRate: '68%',
    date: '2023-12-15'
  },
  {
    id: 2,
    videoId: 'vPwWsQ4fBCT7hF3',
    title: 'PhD | All of Would in Detail..',
    views: 876,
    uniqueViewers: 743,
    avgWatchTime: '3:15',
    completionRate: '54%',
    date: '2023-12-14'
  },
  {
    id: 3,
    videoId: 'xStzlCwln6yMMnLM',
    title: 'PLAY UNO WITH YOUR KEYBOARD..',
    views: 654,
    uniqueViewers: 521,
    avgWatchTime: '5:47',
    completionRate: '72%',
    date: '2023-12-13'
  },
  {
    id: 4,
    videoId: 'kLm9pQrT8jD2nS5',
    title: 'Learn JavaScript in 30 Minutes',
    views: 543,
    uniqueViewers: 432,
    avgWatchTime: '6:21',
    completionRate: '81%',
    date: '2023-12-12'
  },
  {
    id: 5,
    videoId: 'qRt7vBnH3jK4mP9',
    title: 'How to Cook Perfect Pasta Every Time',
    views: 504,
    uniqueViewers: 398,
    avgWatchTime: '2:54',
    completionRate: '47%',
    date: '2023-12-11'
  }
];

// Summary stats
const summaryStats = {
  totalViews: 3822,
  avgViewsPerVideo: 764,
  topPerformingVideo: 'UNO: You Play! Interactive Video Game',
  viewsGrowth: 8
};

export default function VideoViewsPage() {
  const { isAdmin } = useAuth();
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [searchTerm, setSearchTerm] = useState('');
  const [timeRange, setTimeRange] = useState('all');
  const [sortColumn, setSortColumn] = useState('views');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Redirect non-admin users to home page
  React.useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  // Filter and sort data
  const filteredData = videoViewsData.filter(video => 
    video.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    video.videoId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedData = [...filteredData].sort((a, b) => {
    const aValue = a[sortColumn as keyof typeof a];
    const bValue = b[sortColumn as keyof typeof b];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      if (sortDirection === 'asc') {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    } else {
      if (sortDirection === 'asc') {
        return Number(aValue) - Number(bValue);
      } else {
        return Number(bValue) - Number(aValue);
      }
    }
  });

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('desc');
    }
  };

  // If not admin, don't render the page content
  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1 flex flex-col">
        <AdminHeader />

        <main className="flex-1 p-6 overflow-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">Total Video Views</h1>
              <p className="text-muted-foreground">Analyze and track video performance across the platform</p>
            </div>
            <Button className="flex items-center gap-2">
              <Download size={16} />
              Export Data
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Total Views</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{summaryStats.totalViews.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mt-1">Across all videos</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Avg. Views Per Video</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{summaryStats.avgViewsPerVideo.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground mt-1">Platform average</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Top Performing</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold truncate">{summaryStats.topPerformingVideo}</div>
                <p className="text-xs text-muted-foreground mt-1">Most viewed video</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Views Growth</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-500">+{summaryStats.viewsGrowth}%</div>
                <p className="text-xs text-muted-foreground mt-1">Compared to last period</p>
              </CardContent>
            </Card>
          </div>

          <div className="bg-card rounded-lg shadow-sm border mb-6">
            <div className="p-6">
              <div className="flex flex-col md:flex-row gap-4 mb-6 justify-between">
                <div className="flex-1 max-w-md">
                  <div className="relative">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search videos..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-[180px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue placeholder="Time Range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="yesterday">Yesterday</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="year">This Year</SelectItem>
                      <SelectItem value="all">All Time</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px]">Video ID</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('views')}
                      >
                        <div className="flex items-center">
                          Views
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('uniqueViewers')}
                      >
                        <div className="flex items-center">
                          Unique Viewers
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('avgWatchTime')}
                      >
                        <div className="flex items-center">
                          Avg. Watch Time
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('completionRate')}
                      >
                        <div className="flex items-center">
                          Completion Rate
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer"
                        onClick={() => handleSort('date')}
                      >
                        <div className="flex items-center">
                          Date
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedData.map((video) => (
                      <TableRow key={video.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{video.videoId.substring(0, 8)}...</TableCell>
                        <TableCell>
                          <div className="font-medium truncate max-w-[250px]">{video.title}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            <Eye className="mr-2 h-4 w-4 text-blue-500" />
                            {video.views.toLocaleString()}
                          </div>
                        </TableCell>
                        <TableCell>{video.uniqueViewers.toLocaleString()}</TableCell>
                        <TableCell>{video.avgWatchTime}</TableCell>
                        <TableCell>{video.completionRate}</TableCell>
                        <TableCell>{video.date}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
