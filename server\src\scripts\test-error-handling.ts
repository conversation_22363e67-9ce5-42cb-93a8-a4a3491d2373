import { config } from 'dotenv';
import { 
  AppError, 
  createBadRequestError, 
  createNotFoundError, 
  createUnauthorizedError,
  createValidationError,
  createInternalServerError,
  errorLogger
} from '../utils/errors';
import { logger } from '../utils/logger';

// Load environment variables
config();

/**
 * Test error handling system
 */
async function testErrorHandling() {
  try {
    console.log('🔍 Testing error handling system...');
    
    // Test AppError
    console.log('\n1. Testing AppError class...');
    const appError = new AppError('Test error message', 400, 'TEST_ERROR', true, { foo: 'bar' });
    console.log('AppError instance:', {
      message: appError.message,
      statusCode: appError.statusCode,
      errorCode: appError.errorCode,
      isOperational: appError.isOperational,
      data: appError.data,
    });
    
    // Test error factory functions
    console.log('\n2. Testing error factory functions...');
    const badRequestError = createBadRequestError('Bad request error', 'CUSTOM_BAD_REQUEST');
    const notFoundError = createNotFoundError('Resource not found', 'CUSTOM_NOT_FOUND');
    const unauthorizedError = createUnauthorizedError('Unauthorized access', 'CUSTOM_UNAUTHORIZED');
    const validationError = createValidationError('Validation failed', 'CUSTOM_VALIDATION_ERROR', [
      { field: 'email', message: 'Invalid email format' },
      { field: 'password', message: 'Password too short' },
    ]);
    const internalError = createInternalServerError('Something went wrong', 'CUSTOM_INTERNAL_ERROR');
    
    console.log('Bad Request Error:', {
      message: badRequestError.message,
      statusCode: badRequestError.statusCode,
      errorCode: badRequestError.errorCode,
    });
    
    console.log('Not Found Error:', {
      message: notFoundError.message,
      statusCode: notFoundError.statusCode,
      errorCode: notFoundError.errorCode,
    });
    
    console.log('Unauthorized Error:', {
      message: unauthorizedError.message,
      statusCode: unauthorizedError.statusCode,
      errorCode: unauthorizedError.errorCode,
    });
    
    console.log('Validation Error:', {
      message: validationError.message,
      statusCode: validationError.statusCode,
      errorCode: validationError.errorCode,
      data: validationError.data,
    });
    
    console.log('Internal Server Error:', {
      message: internalError.message,
      statusCode: internalError.statusCode,
      errorCode: internalError.errorCode,
    });
    
    // Test error logger
    console.log('\n3. Testing error logger...');
    errorLogger.logError(appError);
    errorLogger.logError(new Error('Standard JavaScript error'));
    
    // Test logger
    console.log('\n4. Testing general logger...');
    logger.info('This is an info message', { user: 'test-user', action: 'login' });
    logger.warn('This is a warning message', { user: 'test-user', action: 'delete' });
    logger.error('This is an error message', { user: 'test-user', error: 'database connection failed' });
    logger.debug('This is a debug message', { user: 'test-user', query: 'SELECT * FROM users' });
    
    console.log('\n✅ Error handling system test completed successfully!');
    console.log('Check the logs directory for log files.');
  } catch (error) {
    console.error('❌ Error testing error handling system:', error);
  }
}

// Run the test
testErrorHandling();
