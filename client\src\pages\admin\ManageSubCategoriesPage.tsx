import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, Pencil, Trash2, X, Plus, Search, ChevronRight, Filter, AlertCircle, Layers } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Mock data for categories
const mockCategories = [
  { id: '1', name: 'Film & Animation' },
  { id: '2', name: 'Music' },
  { id: '3', name: 'Pets & Animals' },
  { id: '4', name: 'Sports' },
  { id: '5', name: 'Travel & Events' },
  { id: '6', name: 'Gaming' },
  { id: '7', name: 'People & Blogs' },
  { id: '8', name: 'Comedy' },
  { id: '9', name: 'Entertainment' },
  { id: '10', name: 'News & Politics' },
  { id: '11', name: 'How-to & Style' },
  { id: '12', name: 'Non-profits & Activism' },
  { id: '13', name: 'Education' },
];

// Mock data for subcategories
const mockSubCategories = [
  { id: '1', categoryId: '1', name: 'Action Movies' },
  { id: '2', categoryId: '1', name: 'Animation' },
  { id: '3', categoryId: '2', name: 'Rock' },
  { id: '4', categoryId: '2', name: 'Pop' },
  { id: '5', categoryId: '3', name: 'Dogs' },
  { id: '6', categoryId: '3', name: 'Cats' },
  { id: '7', categoryId: '4', name: 'Football' },
  { id: '8', categoryId: '4', name: 'Basketball' },
  { id: '9', categoryId: '5', name: 'Travel Vlogs' },
  { id: '10', categoryId: '6', name: 'Minecraft' },
  { id: '11', categoryId: '7', name: 'Daily Vlogs' },
  { id: '12', categoryId: '8', name: 'Stand-up Comedy' },
  { id: '13', categoryId: '9', name: 'Talk Shows' },
  { id: '14', categoryId: '10', name: 'Political Analysis' },
  { id: '15', categoryId: '11', name: 'Makeup Tutorials' },
  { id: '16', categoryId: '12', name: 'Charity Events' },
  { id: '17', categoryId: '13', name: 'Online Courses' },
];

// Language options for adding subcategories
const languages = [
  'English', 'Arabic', 'Dutch', 'French', 'German', 'Russian',
  'Spanish', 'Turkish', 'Hindi', 'Chinese', 'Urdu', 'Indonesian',
  'Croatian', 'Hebrew', 'Bengali', 'Japanese', 'Portuguese', 'Italian',
  'Persian', 'Swedish', 'Vietnamese', 'Danish', 'Filipino'
];

export default function ManageSubCategoriesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [categories] = useState(mockCategories);
  const [subCategories, setSubCategories] = useState(mockSubCategories);
  const [selectedSubCategories, setSelectedSubCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('1'); // Default to first category
  const [subCategoryInputs, setSubCategoryInputs] = useState<Record<string, string>>({});
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingSubCategory, setEditingSubCategory] = useState<{id: string, categoryId: string, translations: Record<string, string>} | null>(null);

  // Filter subcategories based on search term and selected category
  const filteredSubCategories = subCategories.filter(subCategory =>
    subCategory.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (selectedCategory === '' || subCategory.categoryId === selectedCategory)
  );

  // Handle subcategory selection
  const handleSelectSubCategory = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedSubCategories([...selectedSubCategories, id]);
    } else {
      setSelectedSubCategories(selectedSubCategories.filter(subCategoryId => subCategoryId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedSubCategories(filteredSubCategories.map(subCategory => subCategory.id));
    } else {
      setSelectedSubCategories([]);
    }
  };

  // Handle edit subcategory
  const handleEdit = (id: string) => {
    const subCategory = subCategories.find(sub => sub.id === id);
    if (subCategory) {
      // For this demo, we'll just use the same name for all languages
      // In a real app, you would fetch translations from the backend
      const translations: Record<string, string> = {};
      languages.forEach(lang => {
        translations[lang] = subCategory.name;
      });

      // Set some example translations for demo purposes
      translations['Arabic'] = 'أفلام الحركة';
      translations['Dutch'] = 'Actiefilms';
      translations['French'] = 'Films d\'action';
      translations['German'] = 'Actionfilme';
      translations['Russian'] = 'Боевики';
      translations['Spanish'] = 'Películas de acción';
      translations['Portuguese'] = 'Filmes de ação';
      translations['Italian'] = 'Film d\'azione';
      translations['Persian'] = 'فیلم‌های اکشن';
      translations['Swedish'] = 'Actionfilmer';
      translations['Vietnamese'] = 'Phim hành động';
      translations['Danish'] = 'Actionfilm';
      translations['Filipino'] = 'Mga pelikulang aksyon';

      setEditingSubCategory({ id, categoryId: subCategory.categoryId, translations });
      setEditModalOpen(true);
    }
  };

  // Handle save edited subcategory
  const handleSaveEdit = () => {
    if (editingSubCategory) {
      const updatedSubCategories = subCategories.map(subCategory => {
        if (subCategory.id === editingSubCategory.id) {
          // In a real app, you would save all translations
          // Here we just update the English name for simplicity
          return {
            ...subCategory,
            name: editingSubCategory.translations['English'] || subCategory.name,
            categoryId: editingSubCategory.categoryId
          };
        }
        return subCategory;
      });

      setSubCategories(updatedSubCategories);
      setEditModalOpen(false);
      setEditingSubCategory(null);
    }
  };

  // Handle edit input change
  const handleEditInputChange = (language: string, value: string) => {
    if (editingSubCategory) {
      setEditingSubCategory({
        ...editingSubCategory,
        translations: {
          ...editingSubCategory.translations,
          [language]: value
        }
      });
    }
  };

  // Handle edit category change
  const handleEditCategoryChange = (categoryId: string) => {
    if (editingSubCategory) {
      setEditingSubCategory({
        ...editingSubCategory,
        categoryId
      });
    }
  };

  // Handle delete subcategory
  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this subcategory?')) {
      setSubCategories(subCategories.filter(subCategory => subCategory.id !== id));
    }
  };

  // Handle delete selected subcategories
  const handleDeleteSelected = () => {
    if (selectedSubCategories.length === 0) return;

    if (window.confirm('Are you sure you want to delete the selected subcategories?')) {
      setSubCategories(subCategories.filter(subCategory => !selectedSubCategories.includes(subCategory.id)));
      setSelectedSubCategories([]);
    }
  };

  // Handle subcategory input change
  const handleSubCategoryInputChange = (language: string, value: string) => {
    setSubCategoryInputs({
      ...subCategoryInputs,
      [language]: value
    });
  };

  // Handle add subcategory
  const handleAddSubCategory = () => {
    // Check if a category is selected
    if (!selectedCategory) {
      alert('Please select a category');
      return;
    }

    // Check if at least one language has a value
    const hasValue = Object.values(subCategoryInputs).some(value => value.trim() !== '');

    if (!hasValue) {
      alert('Please enter at least one subcategory name');
      return;
    }

    // Add new subcategory (using English name or first non-empty value)
    const newSubCategoryName = subCategoryInputs['English'] || Object.values(subCategoryInputs).find(value => value.trim() !== '') || '';

    const newSubCategory = {
      id: (Math.max(...subCategories.map(c => parseInt(c.id)), 0) + 1).toString(),
      categoryId: selectedCategory,
      name: newSubCategoryName
    };

    setSubCategories([...subCategories, newSubCategory]);
    setSubCategoryInputs({});
  };

  // Get category name by id
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    return category ? category.name : '';
  };

  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // Show success alert
  const displaySuccessAlert = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);
  };

  // Handle add subcategory with success message
  const handleAddSubCategoryWithFeedback = () => {
    handleAddSubCategory();
    displaySuccessAlert('Subcategory added successfully!');
  };

  // Handle delete with success message
  const handleDeleteWithFeedback = (id: string) => {
    handleDelete(id);
    displaySuccessAlert('Subcategory deleted successfully!');
  };

  // Handle delete selected with success message
  const handleDeleteSelectedWithFeedback = () => {
    handleDeleteSelected();
    displaySuccessAlert('Selected subcategories deleted successfully!');
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Sub Categories</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Manage Sub Categories</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="manage" className="w-full">
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="add">Add Sub Category</TabsTrigger>
                <TabsTrigger value="manage">Manage Sub Categories</TabsTrigger>
              </TabsList>

              <TabsContent value="add">
                <Card>
                  <CardHeader>
                    <CardTitle>Add New Sub Category</CardTitle>
                    <CardDescription>Create a new subcategory with translations for multiple languages</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <div className="col-span-1 space-y-2">
                        <label className="text-sm font-medium">Parent Category</label>
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map(category => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      {languages.map((language, index) => (
                        <div key={index} className="space-y-2">
                          <label className="text-sm font-medium">{language}</label>
                          <Input
                            value={subCategoryInputs[language] || ''}
                            onChange={(e) => handleSubCategoryInputChange(language, e.target.value)}
                            placeholder={`${language} name...`}
                          />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={handleAddSubCategoryWithFeedback}
                      className="gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Add Sub Category
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="manage">
                <Card>
                  <CardHeader>
                    <CardTitle>Manage Sub Categories</CardTitle>
                    <CardDescription>View, edit, and delete subcategories</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="flex flex-col md:flex-row gap-4 items-end">
                      <div className="w-full md:w-64 space-y-2">
                        <label className="text-sm font-medium">Filter by Category</label>
                        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a category" />
                          </SelectTrigger>
                          <SelectContent>
                            {categories.map(category => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative flex-1">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search subcategories..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>

                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-12">
                              <Checkbox
                                checked={selectedSubCategories.length === filteredSubCategories.length && filteredSubCategories.length > 0}
                                onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                              />
                            </TableHead>
                            <TableHead>ID</TableHead>
                            <TableHead>Sub Category Name</TableHead>
                            <TableHead>Parent Category</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredSubCategories.length > 0 ? (
                            filteredSubCategories.map((subCategory) => (
                              <TableRow key={subCategory.id}>
                                <TableCell>
                                  <Checkbox
                                    checked={selectedSubCategories.includes(subCategory.id)}
                                    onCheckedChange={(checked) => handleSelectSubCategory(subCategory.id, checked as boolean)}
                                  />
                                </TableCell>
                                <TableCell className="font-medium">{subCategory.id}</TableCell>
                                <TableCell>
                                  <Badge variant="outline" className="font-normal">
                                    {subCategory.name}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="secondary" className="font-normal">
                                    {getCategoryName(subCategory.categoryId)}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="flex justify-end gap-2">
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleEdit(subCategory.id)}
                                      className="h-8 w-8 text-blue-500 hover:text-blue-700 hover:bg-blue-50"
                                    >
                                      <Pencil className="h-4 w-4" />
                                      <span className="sr-only">Edit</span>
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      onClick={() => handleDeleteWithFeedback(subCategory.id)}
                                      className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <Trash2 className="h-4 w-4" />
                                      <span className="sr-only">Delete</span>
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                                <div className="flex flex-col items-center justify-center py-4">
                                  <Layers className="h-10 w-10 text-muted-foreground mb-2" />
                                  <p>No subcategories found</p>
                                  <p className="text-sm text-muted-foreground">Try selecting a different category or adjusting your search</p>
                                </div>
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </div>

                    {selectedSubCategories.length > 0 && (
                      <div className="flex items-center justify-between bg-muted/30 p-3 rounded-md">
                        <div className="text-sm">
                          <span className="font-medium">{selectedSubCategories.length}</span> {selectedSubCategories.length === 1 ? 'subcategory' : 'subcategories'} selected
                        </div>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={handleDeleteSelectedWithFeedback}
                          className="gap-2"
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete Selected
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>

      {/* Edit Sub Category Modal */}
      <Dialog open={editModalOpen} onOpenChange={setEditModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Sub Category</DialogTitle>
            <DialogDescription>
              Update subcategory translations and parent category
            </DialogDescription>
          </DialogHeader>

          {editingSubCategory && (
            <div className="py-4">
              <div className="mb-6 space-y-2">
                <label className="text-sm font-medium">Parent Category</label>
                <Select value={editingSubCategory.categoryId} onValueChange={handleEditCategoryChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-h-[60vh] overflow-y-auto">
                {languages.map((language, index) => (
                  <div key={index} className="space-y-2">
                    <label htmlFor={`edit-${language}`} className="text-sm font-medium">
                      {language}
                    </label>
                    <Input
                      id={`edit-${language}`}
                      value={editingSubCategory.translations[language] || ''}
                      onChange={(e) => handleEditInputChange(language, e.target.value)}
                      placeholder={`${language} name...`}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEditModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleSaveEdit();
                displaySuccessAlert('Subcategory updated successfully!');
              }}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
