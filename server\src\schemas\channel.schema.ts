import { Type } from '@sinclair/typebox';

/**
 * Base channel schema with common properties
 */
const ChannelBaseSchema = {
  name: Type.String({
    minLength: 3,
    maxLength: 30,
    pattern: '^[a-zA-Z0-9_.-]+$',
    description: 'Unique channel name (3-30 characters, alphanumeric with _.-)',
  }),
  displayName: Type.Optional(
    Type.String({
      minLength: 1,
      maxLength: 50,
      description: 'Display name for the channel (1-50 characters)',
    })
  ),
  description: Type.String({
    minLength: 1,
    maxLength: 5000,
    description: 'Channel description (1-5000 characters)',
  }),
  category: Type.Optional(
    Type.String({
      description: 'Channel category',
    })
  ),
  visibility: Type.Optional(
    Type.String({
      enum: ['public', 'private', 'unlisted'],
      default: 'public',
      description: 'Channel visibility',
    })
  ),
  tags: Type.Optional(
    Type.Array(
      Type.String({
        description: 'Channel tag',
      }),
      {
        maxItems: 20,
        description: 'Tags for the channel (max 20)',
      }
    )
  ),
  socialLinks: Type.Optional(
    Type.Object(
      {
        website: Type.Optional(Type.String({ format: 'uri' })),
        facebook: Type.Optional(Type.String()),
        twitter: Type.Optional(Type.String()),
        instagram: Type.Optional(Type.String()),
        linkedin: Type.Optional(Type.String()),
        youtube: Type.Optional(Type.String()),
      },
      {
        description: 'Social media links',
      }
    )
  ),
  settings: Type.Optional(
    Type.Object(
      {
        defaultCommentsEnabled: Type.Optional(Type.Boolean()),
        moderateComments: Type.Optional(Type.Boolean()),
        showSubscriberCount: Type.Optional(Type.Boolean()),
      },
      {
        description: 'Channel settings',
      }
    )
  ),
};

/**
 * Schema for creating a new channel
 */
export const CreateChannelSchema = {
  body: Type.Object(
    {
      ...ChannelBaseSchema,
      avatar: Type.Optional(Type.String()),
      banner: Type.Optional(Type.String()),
    },
    {
      description: 'Create a new channel',
    }
  ),
  response: {
    201: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        channel: Type.Object(
          {
            id: Type.String(),
            name: Type.String(),
            displayName: Type.String(),
            description: Type.String(),
            avatar: Type.Optional(Type.String()),
            banner: Type.Optional(Type.String()),
            ownerId: Type.String(),
            category: Type.Optional(Type.String()),
            tags: Type.Array(Type.String()),
            isVerified: Type.Boolean(),
            isFeatured: Type.Boolean(),
            visibility: Type.String(),
            status: Type.String(),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Created channel',
          }
        ),
      },
      {
        description: 'Channel created successfully',
      }
    ),
    400: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Bad request',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    409: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Conflict',
      }
    ),
  },
};

/**
 * Schema for updating an existing channel
 */
export const UpdateChannelSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Channel ID',
      }),
    },
    {
      description: 'Channel parameters',
    }
  ),
  body: Type.Object(
    {
      ...ChannelBaseSchema,
      avatar: Type.Optional(Type.String()),
      banner: Type.Optional(Type.String()),
    },
    {
      description: 'Update channel',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        channel: Type.Object(
          {
            id: Type.String(),
            name: Type.String(),
            displayName: Type.String(),
            description: Type.String(),
            avatar: Type.Optional(Type.String()),
            banner: Type.Optional(Type.String()),
            ownerId: Type.String(),
            category: Type.Optional(Type.String()),
            tags: Type.Array(Type.String()),
            isVerified: Type.Boolean(),
            isFeatured: Type.Boolean(),
            visibility: Type.String(),
            status: Type.String(),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Updated channel',
          }
        ),
      },
      {
        description: 'Channel updated successfully',
      }
    ),
    400: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Bad request',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    403: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Forbidden',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Channel not found',
      }
    ),
    409: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Conflict',
      }
    ),
  },
};

/**
 * Schema for getting a channel by ID
 */
export const GetChannelSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Channel ID',
      }),
    },
    {
      description: 'Channel parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        channel: Type.Object(
          {
            id: Type.String(),
            name: Type.String(),
            displayName: Type.String(),
            description: Type.String(),
            avatar: Type.Optional(Type.String()),
            banner: Type.Optional(Type.String()),
            ownerId: Type.String(),
            category: Type.Optional(Type.String()),
            tags: Type.Array(Type.String()),
            isVerified: Type.Boolean(),
            isFeatured: Type.Boolean(),
            visibility: Type.String(),
            status: Type.String(),
            stats: Type.Object(
              {
                subscribers: Type.Number(),
                totalViews: Type.Number(),
                videoCount: Type.Number(),
              },
              {
                description: 'Channel statistics',
              }
            ),
            socialLinks: Type.Optional(
              Type.Object(
                {
                  website: Type.Optional(Type.String()),
                  facebook: Type.Optional(Type.String()),
                  twitter: Type.Optional(Type.String()),
                  instagram: Type.Optional(Type.String()),
                  linkedin: Type.Optional(Type.String()),
                  youtube: Type.Optional(Type.String()),
                },
                {
                  description: 'Social media links',
                }
              )
            ),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Channel details',
          }
        ),
      },
      {
        description: 'Channel retrieved successfully',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Channel not found',
      }
    ),
  },
};

/**
 * Schema for getting a list of channels
 */
export const GetChannelsSchema = {
  querystring: Type.Object(
    {
      page: Type.Optional(
        Type.Union([
          Type.Number({
            minimum: 1,
            default: 1,
            description: 'Page number',
          }),
          Type.String()
        ])
      ),
      limit: Type.Optional(
        Type.Union([
          Type.Number({
            minimum: 1,
            maximum: 100,
            default: 20,
            description: 'Number of items per page',
          }),
          Type.String()
        ])
      ),
      sort: Type.Optional(
        Type.String({
          enum: ['newest', 'popular', 'trending'],
          default: 'newest',
          description: 'Sort order',
        })
      ),
      category: Type.Optional(
        Type.String({
          description: 'Filter by category',
        })
      ),
      featured: Type.Optional(
        Type.Union([
          Type.Boolean({
            description: 'Filter by featured status',
          }),
          Type.String()
        ])
      ),
      search: Type.Optional(
        Type.String({
          description: 'Search term',
        })
      ),
    },
    {
      description: 'Query parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        channels: Type.Array(
          Type.Object(
            {
              id: Type.String(),
              name: Type.String(),
              displayName: Type.String(),
              description: Type.String(),
              avatar: Type.Optional(Type.String()),
              banner: Type.Optional(Type.String()),
              ownerId: Type.String(),
              category: Type.Optional(Type.String()),
              tags: Type.Array(Type.String()),
              isVerified: Type.Boolean(),
              isFeatured: Type.Boolean(),
              visibility: Type.String(),
              stats: Type.Object(
                {
                  subscribers: Type.Number(),
                  totalViews: Type.Number(),
                  videoCount: Type.Number(),
                },
                {
                  description: 'Channel statistics',
                }
              ),
              createdAt: Type.String({ format: 'date-time' }),
            },
            {
              description: 'Channel summary',
            }
          )
        ),
        pagination: Type.Object(
          {
            total: Type.Number(),
            page: Type.Number(),
            limit: Type.Number(),
            pages: Type.Number(),
          },
          {
            description: 'Pagination information',
          }
        ),
      },
      {
        description: 'Channels retrieved successfully',
      }
    ),
  },
};

/**
 * Schema for getting a channel by name
 */
export const GetChannelByNameSchema = {
  params: Type.Object(
    {
      name: Type.String({
        description: 'Channel name',
      }),
    },
    {
      description: 'Channel parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        channel: Type.Object(
          {
            id: Type.String(),
            name: Type.String(),
            displayName: Type.String(),
            description: Type.String(),
            avatar: Type.Optional(Type.String()),
            banner: Type.Optional(Type.String()),
            ownerId: Type.String(),
            category: Type.Optional(Type.String()),
            tags: Type.Array(Type.String()),
            isVerified: Type.Boolean(),
            isFeatured: Type.Boolean(),
            status: Type.String(),
            stats: Type.Object(
              {
                subscribers: Type.Number(),
                totalViews: Type.Number(),
                videoCount: Type.Number(),
              },
              {
                description: 'Channel statistics',
              }
            ),
            socialLinks: Type.Optional(
              Type.Object(
                {
                  website: Type.Optional(Type.String()),
                  facebook: Type.Optional(Type.String()),
                  twitter: Type.Optional(Type.String()),
                  instagram: Type.Optional(Type.String()),
                  linkedin: Type.Optional(Type.String()),
                  youtube: Type.Optional(Type.String()),
                },
                {
                  description: 'Social media links',
                }
              )
            ),
            createdAt: Type.String({ format: 'date-time' }),
            updatedAt: Type.String({ format: 'date-time' }),
          },
          {
            description: 'Channel details',
          }
        ),
      },
      {
        description: 'Channel retrieved successfully',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Channel not found',
      }
    ),
  },
};

/**
 * Schema for getting a user's channels
 */
export const GetUserChannelsSchema = {
  params: Type.Object(
    {
      userId: Type.String({
        description: 'User ID',
      }),
    },
    {
      description: 'User parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        channels: Type.Array(
          Type.Object(
            {
              id: Type.String(),
              name: Type.String(),
              displayName: Type.String(),
              description: Type.String(),
              avatar: Type.Optional(Type.String()),
              category: Type.Optional(Type.String()),
              isVerified: Type.Boolean(),
              isFeatured: Type.Boolean(),
              stats: Type.Object(
                {
                  subscribers: Type.Number(),
                  totalViews: Type.Number(),
                  videoCount: Type.Number(),
                },
                {
                  description: 'Channel statistics',
                }
              ),
              createdAt: Type.String({ format: 'date-time' }),
            },
            {
              description: 'Channel summary',
            }
          )
        ),
      },
      {
        description: 'User channels retrieved successfully',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'User not found',
      }
    ),
  },
};

/**
 * Schema for deleting a channel
 */
export const DeleteChannelSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Channel ID',
      }),
    },
    {
      description: 'Channel parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
      },
      {
        description: 'Channel deleted successfully',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    403: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Forbidden',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Channel not found',
      }
    ),
  },
};

/**
 * Schema for subscribing to a channel
 */
export const SubscribeChannelSchema = {
  params: Type.Object(
    {
      id: Type.String({
        description: 'Channel ID',
      }),
    },
    {
      description: 'Channel parameters',
    }
  ),
  response: {
    200: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        isSubscribed: Type.Boolean(),
      },
      {
        description: 'Subscription updated successfully',
      }
    ),
    401: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Unauthorized',
      }
    ),
    404: Type.Object(
      {
        success: Type.Boolean(),
        message: Type.String(),
        error: Type.Object(
          {
            code: Type.String(),
            message: Type.String(),
          },
          {
            description: 'Error details',
          }
        ),
      },
      {
        description: 'Channel not found',
      }
    ),
  },
};
