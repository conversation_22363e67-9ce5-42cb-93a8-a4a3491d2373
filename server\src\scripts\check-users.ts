import { UserModel } from '../models';
import { connectDB } from '../config/database';
import mongoose from 'mongoose';

async function checkUsers() {
  try {
    // Connect to the database
    await connectDB();

    // Find all users
    const users = await UserModel.find({});

    console.log(`Found ${users.length} users:`);

    // Display user details
    users.forEach(user => {
      console.log(`
User ID: ${user.id}
Username: ${user.username}
Email: ${user.email}
Status: ${user.status}
Email Verified: ${user.emailVerified}
Roles: ${user.roles}
Created At: ${user.createdAt}
      `);
    });

    // Close the database connection
    await mongoose.connection.close();

  } catch (error) {
    console.error('Error checking users:', error);
    process.exit(1);
  }
}

// Run the function
checkUsers().catch(console.error);
