import axios from 'axios';
import { translateText as directTranslateText, SUPPORTED_LANGUAGES, getScriptCode } from './bhashiniDirectApi';

/**
 * Bhashini API Service
 *
 * This service provides integration with the Bhashini API for translation and language processing
 * Bhashini is an AI-based translation service developed by the Government of India
 * that specializes in Indian languages.
 *
 * Documentation: https://bhashini.gov.in/ulca/apis
 */

// Bhashini API endpoints - Updated with new URL as suggested
const BHASHINI_API_BASE_URL = 'https://bhashini.gov.in';
const BHASHINI_COMPUTE_API_URL = 'https://bhashini.gov.in/api/v1/inference/translation';

// Fallback endpoints if the new ones don't work
const BHASHINI_API_BASE_URL_FALLBACK = 'https://meity-auth.ulcacontrib.org';
const BHASHINI_COMPUTE_API_URL_FALLBACK = 'https://meity-auth.ulcacontrib.org/ulca/apis/v0/model/getModelsPipeline';

// Bhashini API configuration interface
export interface BhashiniConfig {
  apiKey: string;
  userId?: string;
  ulcaApiKey?: string;
}

// Export supported languages
export { SUPPORTED_LANGUAGES };

// Bhashini language code mapping
const ISO_TO_BHASHINI_LANGUAGE_MAP: Record<string, string> = {
  'en': 'en',
  'hi': 'hi',
  'mr': 'mr',
  'gu': 'gu',
  'ta': 'ta',
  'te': 'te',
  'bn': 'bn',
  'kn': 'kn',
  'ml': 'ml',
  'pa': 'pa',
  'or': 'or',
  'as': 'as',
  'ur': 'ur',
  'sa': 'sa',
  'ne': 'ne',
  'si': 'si',
  'doi': 'doi',
  'ks': 'ks',
  'sd': 'sd',
  'brx': 'brx',
  'mni': 'mni',
  'sat': 'sat',
  'lus': 'lus',
  'njz': 'njz',
  'pnr': 'pnr',
  'kha': 'kha',
  'grt': 'grt',
  'kok': 'kok',
  'mai': 'mai',
  'bho': 'bho',
  'raj': 'raj',
  'mup': 'mup',
  'bodo': 'brx', // Alias
  'dogri': 'doi', // Alias
  'konkani': 'kok', // Alias
  'maithili': 'mai', // Alias
  'manipuri': 'mni', // Alias
  'nepali': 'ne', // Alias
  'odia': 'or', // Alias
  'punjabi': 'pa', // Alias
  'sanskrit': 'sa', // Alias
  'santali': 'sat', // Alias
  'sindhi': 'sd', // Alias
  'kashmiri': 'ks', // Alias
  'assamese': 'as', // Alias
  'bengali': 'bn', // Alias
  'gujarati': 'gu', // Alias
  'kannada': 'kn', // Alias
  'malayalam': 'ml', // Alias
  'marathi': 'mr', // Alias
  'tamil': 'ta', // Alias
  'telugu': 'te', // Alias
  'hindi': 'hi', // Alias
  'english': 'en', // Alias
  'urdu': 'ur' // Alias
};

/**
 * Convert ISO language code to Bhashini language code
 * @param isoCode ISO language code
 * @returns Bhashini language code
 */
export function toBhashiniLanguageCode(isoCode: string): string {
  return ISO_TO_BHASHINI_LANGUAGE_MAP[isoCode.toLowerCase()] || isoCode.toLowerCase();
}

/**
 * Get authentication token from Bhashini API
 * @param config Bhashini API configuration
 * @returns Authentication token
 */
export async function getAuthToken(config: BhashiniConfig): Promise<string> {
  try {
    console.log(`Getting Bhashini auth token for userId: ${config.userId || config.apiKey}`);

    // For the updated Bhashini API, we don't need to get a separate auth token
    // Instead, we'll use the userId directly in the API calls
    // This is a placeholder function to maintain compatibility with existing code
    console.log('Using direct authentication with Bhashini API');
    return 'direct-auth';
  } catch (error) {
    console.error('Error getting Bhashini auth token:', error);
    throw new Error('Failed to authenticate with Bhashini API');
  }
}

/**
 * Execute model computation using Bhashini API
 * @param modelId Model ID to use for computation
 * @param taskType Type of task (translation, language-detection, etc.)
 * @param inputData Input data for the model
 * @param config Bhashini API configuration
 * @returns Model computation result
 */
export async function executeModelComputation(
  modelId: string,
  taskType: string,
  inputData: Array<{ source: string }>,
  pipelineParams: any = {},
  config: BhashiniConfig
): Promise<any> {
  try {
    // Use the provided credentials or default to the known working ones
    const bhashiniUserId = config.userId || 'cee60134c6bb4d179efd3fda48ff32fe';
    const bhashiniUlcaApiKey = config.ulcaApiKey || '13a647c84b-2747-4f0c-afcd-2ac8235f5318';

    // For translation tasks, use the direct API implementation
    if (taskType === 'translation' && inputData.length > 0) {
      console.log('Using direct Bhashini API implementation for translation');

      const sourceLanguage = pipelineParams.language?.sourceLanguage || 'en';
      const targetLanguage = pipelineParams.language?.targetLanguage || 'hi';

      // Call the direct translation API
      const translatedText = await directTranslateText(
        inputData[0].source,
        sourceLanguage,
        targetLanguage,
        {
          userId: bhashiniUserId,
          apiKey: bhashiniUlcaApiKey
        }
      );

      // Return the result in the expected format
      return {
        output: [
          {
            target: translatedText,
            source_language: sourceLanguage,
            target_language: targetLanguage
          }
        ]
      };
    }

    // For other tasks or as a fallback, use the original implementation
    console.log('Using standard Bhashini API implementation');

    // Prepare request payload for the new Bhashini API endpoint
    const payload = {
      userId: bhashiniUserId,
      ulcaApiKey: bhashiniUlcaApiKey,
      sourceLanguage: pipelineParams.language?.sourceLanguage || 'en',
      targetLanguage: pipelineParams.language?.targetLanguage || 'hi',
      domain: "general",
      text: inputData[0].source
    };

    console.log('Executing Bhashini translation with payload:', payload);

    // Make API request with headers for the new Bhashini API endpoint
    const response = await axios.post(
      BHASHINI_COMPUTE_API_URL,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }
    );

    // Return the response data in a format compatible with the rest of the code
    if (response.data) {
      console.log('Bhashini translation successful:', response.data);

      // Format the response to match the expected structure
      return {
        output: [
          {
            target: response.data.translatedText || response.data.text || inputData[0].source,
            source_language: pipelineParams.language?.sourceLanguage || 'en',
            target_language: pipelineParams.language?.targetLanguage || 'hi'
          }
        ]
      };
    }

    throw new Error('Invalid response from Bhashini API');
  } catch (error) {
    console.error('Error executing Bhashini model computation:', error);
    if (axios.isAxiosError(error)) {
      console.error('API Error details:', error.response?.data);
      console.error('API Error status:', error.response?.status);
      console.error('API Error headers:', error.response?.headers);
    }

    // Add more detailed error message
    const errorMessage = axios.isAxiosError(error)
      ? `Failed to execute Bhashini model computation: ${error.response?.status} - ${error.response?.data?.message || error.message}`
      : `Failed to execute Bhashini model computation: ${error instanceof Error ? error.message : 'Unknown error'}`;

    throw new Error(errorMessage);
  }
}

export default {
  toBhashiniLanguageCode,
  getAuthToken,
  executeModelComputation
};
