/**
 * Application configuration
 * Uses environment variables with fallback defaults
 */

// API base URL
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api/v1';

// WebSocket URL
export const WS_URL = import.meta.env.VITE_WS_URL || 'ws://localhost:3001/ws/chat';

// Application settings
export const APP_NAME = import.meta.env.VITE_APP_NAME || 'LawEngaxe';
export const APP_VERSION = import.meta.env.VITE_APP_VERSION || '1.0.0';
export const NODE_ENV = import.meta.env.VITE_NODE_ENV || 'development';

// Default pagination settings
export const DEFAULT_PAGE_SIZE = parseInt(import.meta.env.VITE_DEFAULT_PAGE_SIZE) || 10;

// File upload limits
export const MAX_FILE_SIZE = parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || 10 * 1024 * 1024; // 10MB
export const ALLOWED_FILE_TYPES = import.meta.env.VITE_ALLOWED_FILE_TYPES
  ? import.meta.env.VITE_ALLOWED_FILE_TYPES.split(',')
  : [
      'image/jpeg',
      'image/png',
      'image/gif',
      'audio/mpeg',
      'audio/wav',
      'video/mp4',
      'application/pdf',
    ];

// Avatar placeholder URL
export const DEFAULT_AVATAR_URL = import.meta.env.VITE_DEFAULT_AVATAR_URL || 'https://ui-avatars.com/api/?background=random';

// Debounce delay for search inputs (in milliseconds)
export const SEARCH_DEBOUNCE_DELAY = parseInt(import.meta.env.VITE_SEARCH_DEBOUNCE_DELAY) || 300;

// WebSocket ping interval (in milliseconds)
export const WS_PING_INTERVAL = parseInt(import.meta.env.VITE_WS_PING_INTERVAL) || 30000; // 30 seconds

// Typing indicator timeout (in milliseconds)
export const TYPING_INDICATOR_TIMEOUT = parseInt(import.meta.env.VITE_TYPING_INDICATOR_TIMEOUT) || 3000; // 3 seconds

// Bhashini API Configuration
export const BHASHINI_CONFIG = {
  API_BASE_URL: import.meta.env.VITE_BHASHINI_API_BASE_URL || 'https://bhashini.gov.in',
  COMPUTE_API_URL: import.meta.env.VITE_BHASHINI_COMPUTE_API_URL || 'https://bhashini.gov.in/api/v1/inference/translation',
  INFERENCE_URL: import.meta.env.VITE_BHASHINI_INFERENCE_URL || 'https://dhruva-api.bhashini.gov.in/services/inference/pipeline',
  USER_ID: import.meta.env.VITE_BHASHINI_USER_ID || 'cee60134c6bb4d179efd3fda48ff32fe',
  API_KEY: import.meta.env.VITE_BHASHINI_API_KEY || '13a647c84b-2747-4f0c-afcd-2ac8235f5318',
  AUTHORIZATION: import.meta.env.VITE_BHASHINI_AUTHORIZATION || 'W9QK_zzb7Lnsc_xYAl30Gk64Z8rJU4r_2NBiguZjiMIdkUI_8p-E38M-zc_VVhun',
};

// Development/Debug Settings
export const DEBUG_MODE = import.meta.env.VITE_DEBUG_MODE === 'true';
export const ENABLE_LOGGING = import.meta.env.VITE_ENABLE_LOGGING !== 'false';
