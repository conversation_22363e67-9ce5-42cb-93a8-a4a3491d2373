import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import authController from '../controllers/auth.controller';
import { authenticate } from '../middleware/auth';
import { Type } from '@sinclair/typebox';
import { authActionLogger } from '../middleware/auditLogger';

/**
 * Authentication routes
 */
export default async function authRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
): Promise<void> {
  // Login route
  fastify.post(
    '/login',
    {
      preHandler: authActionLogger('login', 'User login attempt'),
      schema: {
        body: Type.Object({
          email: Type.String({ pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$' }),
          password: Type.String(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
            accessToken: Type.String(),
            refreshToken: Type.String(),
            user: Type.Object({
              id: Type.String(),
              username: Type.String(),
              email: Type.String(),
              firstName: Type.String(),
              lastName: Type.String(),
              displayName: Type.String(),
              roles: Type.Array(Type.String()),
            }),
          }),
          401: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    authController.login as any
  );



  // Refresh token route
  fastify.post(
    '/refresh-token',
    {
      preHandler: authActionLogger('refresh-token', 'Token refresh'),
      schema: {
        body: Type.Object({
          refreshToken: Type.String(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            accessToken: Type.String(),
          }),
          401: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    authController.refreshToken as any
  );

  // Get current user route (protected)
  fastify.get(
    '/me',
    {
      preHandler: authenticate,
      schema: {
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            user: Type.Object({
              id: Type.String(),
              username: Type.String(),
              email: Type.String(),
              firstName: Type.String(),
              lastName: Type.String(),
              displayName: Type.String(),
              avatar: Type.Optional(Type.String()),
              roles: Type.Array(Type.String()),
              permissions: Type.Array(Type.String()),
              createdAt: Type.String({ format: 'date-time' }),
              updatedAt: Type.String({ format: 'date-time' }),
            }),
          }),
          401: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    authController.me
  );

  // Verify email route
  fastify.get(
    '/verify-email/:token',
    {
      schema: {
        params: Type.Object({
          token: Type.String(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
          400: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    authController.verifyEmail
  );

  // Request password reset route
  fastify.post(
    '/forgot-password',
    {
      preHandler: authActionLogger('forgot-password', 'Password reset request'),
      schema: {
        body: Type.Object({
          email: Type.String({ pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$' }),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
            resetToken: Type.Optional(Type.String()),
          }),
          400: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    authController.requestPasswordReset as any
  );

  // Reset password route
  fastify.post(
    '/reset-password',
    {
      preHandler: authActionLogger('reset-password', 'Password reset'),
      schema: {
        body: Type.Object({
          token: Type.String(),
          newPassword: Type.String({ minLength: 8 }),
          confirmPassword: Type.String(),
        }),
        response: {
          200: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
          400: Type.Object({
            success: Type.Boolean(),
            message: Type.String(),
          }),
        },
      },
    },
    authController.resetPassword as any
  );
}
