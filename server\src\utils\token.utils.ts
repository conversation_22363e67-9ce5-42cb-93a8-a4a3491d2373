import { FastifyInstance } from 'fastify';

/**
 * Generate email verification token
 */
export function generateEmailVerificationToken(
  fastify: FastifyInstance,
  userId: string,
  email: string
): string {
  return fastify.jwt.sign(
    {
      id: userId,
      email,
      type: 'email_verification',
    },
    { expiresIn: '24h' }
  );
}

/**
 * Generate password reset token
 */
export function generatePasswordResetToken(
  fastify: FastifyInstance,
  userId: string,
  email: string
): string {
  return fastify.jwt.sign(
    {
      id: userId,
      email,
      type: 'password_reset',
    },
    { expiresIn: '1h' }
  );
}

/**
 * Generate API key
 */
export function generateApiKey(
  fastify: FastifyInstance,
  userId: string,
  scopes: string[]
): string {
  return fastify.jwt.sign(
    {
      id: userId,
      scopes,
      type: 'api_key',
    },
    { expiresIn: '365d' }
  );
}
