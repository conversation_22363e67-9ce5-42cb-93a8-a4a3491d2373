/**
 * <PERSON>ript to update all videos in the database to ensure they have proper language data
 * Run this script with: node src/scripts/update-video-languages.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/lawengaxe';

// Helper function to get a flag emoji for a language code
function getLanguageFlag(code) {
  if (!code) return '🌐';
  
  switch (code.toLowerCase()) {
    case 'en': return '🇺🇸';
    case 'hi': return '🇮🇳';
    case 'es': return '🇪🇸';
    case 'fr': return '🇫🇷';
    case 'de': return '🇩🇪';
    case 'ja': return '🇯🇵';
    case 'zh': return '🇨🇳';
    case 'ru': return '🇷🇺';
    case 'ar': return '🇸🇦';
    case 'pt': return '🇵🇹';
    case 'it': return '🇮🇹';
    case 'nl': return '🇳🇱';
    case 'ko': return '🇰🇷';
    case 'tr': return '🇹🇷';
    // Add more Indian languages
    case 'bn': return '🇮🇳'; // Bengali
    case 'ta': return '🇮🇳'; // Tamil
    case 'te': return '🇮🇳'; // Telugu
    case 'mr': return '🇮🇳'; // Marathi
    case 'gu': return '🇮🇳'; // Gujarati
    case 'kn': return '🇮🇳'; // Kannada
    case 'ml': return '🇮🇳'; // Malayalam
    case 'pa': return '🇮🇳'; // Punjabi
    case 'or': return '🇮🇳'; // Odia
    case 'as': return '🇮🇳'; // Assamese
    case 'ur': return '🇵🇰'; // Urdu
    default: return '🌐';
  }
}

// Check if a string is a valid Engaxe ID (6-7 alphanumeric characters)
function isValidEngaxeId(id) {
  return /^[a-zA-Z0-9]{6,7}$/.test(id);
}

// Define the Video schema
const VideoSchema = new mongoose.Schema({
  id: String,
  title: String,
  description: String,
  url: String,
  thumbnailUrl: String,
  duration: Number,
  userId: String,
  channelId: String,
  visibility: String,
  languages: [{
    code: String,
    name: String,
    flag: String,
    isDefault: Boolean,
    url: String
  }],
  source: mongoose.Schema.Types.Mixed
});

// Create the Video model
const Video = mongoose.model('Video', VideoSchema);

// Function to fix all videos
async function updateAllVideoLanguages() {
  try {
    console.log('Starting to update all video languages...');

    // Get all videos
    const videos = await Video.find({});
    console.log(`Found ${videos.length} videos in the database`);

    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Process each video
    for (const video of videos) {
      try {
        console.log(`Processing video: ${video.title} (ID: ${video.id})`);
        
        // Check if the video already has languages
        if (video.languages && video.languages.length > 0) {
          console.log(`Video already has ${video.languages.length} languages`);
          
          // Validate existing languages
          let needsUpdate = false;
          const updatedLanguages = video.languages.map(lang => {
            // Create a copy to avoid mutating the original
            const processedLang = { ...lang };
            
            // Ensure language has a code
            if (!processedLang.code) {
              processedLang.code = 'en';
              needsUpdate = true;
              console.log(`Added missing code 'en' to language`);
            }
            
            // Ensure language has a name
            if (!processedLang.name) {
              processedLang.name = processedLang.code === 'en' ? 'English' : `Language (${processedLang.code})`;
              needsUpdate = true;
              console.log(`Added missing name '${processedLang.name}' to language`);
            }
            
            // Ensure language has a flag
            if (!processedLang.flag) {
              processedLang.flag = getLanguageFlag(processedLang.code);
              needsUpdate = true;
              console.log(`Added missing flag '${processedLang.flag}' to language ${processedLang.name}`);
            }
            
            // Ensure language has a URL
            if (!processedLang.url) {
              processedLang.url = video.url;
              needsUpdate = true;
              console.log(`Added missing URL to language ${processedLang.name}`);
            }
            
            // Ensure URL is a valid Engaxe ID
            if (!isValidEngaxeId(processedLang.url)) {
              console.log(`Language ${processedLang.name} has invalid URL: ${processedLang.url}`);
              if (isValidEngaxeId(video.url)) {
                processedLang.url = video.url;
                needsUpdate = true;
                console.log(`Updated language URL to video URL: ${video.url}`);
              }
            }
            
            // Ensure isDefault is a boolean
            if (typeof processedLang.isDefault !== 'boolean') {
              processedLang.isDefault = processedLang.code === 'en';
              needsUpdate = true;
              console.log(`Set isDefault to ${processedLang.isDefault} for language ${processedLang.name}`);
            }
            
            return processedLang;
          });
          
          // Check if we have at least one default language
          const hasDefaultLanguage = updatedLanguages.some(lang => lang.isDefault);
          if (!hasDefaultLanguage && updatedLanguages.length > 0) {
            updatedLanguages[0].isDefault = true;
            needsUpdate = true;
            console.log(`Set first language ${updatedLanguages[0].name} as default`);
          }
          
          if (needsUpdate) {
            // Update the video with the fixed languages
            video.languages = updatedLanguages;
            await video.save();
            updatedCount++;
            console.log(`Updated languages for video: ${video.title}`);
          } else {
            skippedCount++;
            console.log(`No updates needed for video: ${video.title}`);
          }
        } else {
          // Video has no languages, add a default English language
          console.log(`Video has no languages, adding default English language`);
          
          // Create a default English language with the video URL
          video.languages = [{
            code: 'en',
            name: 'English',
            flag: '🇺🇸',
            isDefault: true,
            url: video.url
          }];
          
          await video.save();
          updatedCount++;
          console.log(`Added default English language to video: ${video.title}`);
        }
      } catch (error) {
        console.error(`Error processing video ${video.id}:`, error);
        errorCount++;
      }
    }

    console.log('Update complete!');
    console.log(`Total videos: ${videos.length}`);
    console.log(`Updated: ${updatedCount}`);
    console.log(`Skipped: ${skippedCount}`);
    console.log(`Errors: ${errorCount}`);

    return {
      total: videos.length,
      updated: updatedCount,
      skipped: skippedCount,
      errors: errorCount
    };
  } catch (error) {
    console.error('Error updating video languages:', error);
    throw error;
  } finally {
    // Close the MongoDB connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  }
}

// Connect to MongoDB and run the update
mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('Connected to MongoDB');
    return updateAllVideoLanguages();
  })
  .then(result => {
    console.log('Update completed successfully!');
    console.log(result);
    process.exit(0);
  })
  .catch(error => {
    console.error('Error:', error);
    process.exit(1);
  });
