import { useState, useEffect, useRef } from 'react';
import { Phone, PhoneOff, Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/context/AuthContext';
import { useChatbot } from '@/context/ChatbotContext';
import { useToast } from '@/hooks/use-toast';
import * as speechSynthesis from '@/utils/speechSynthesis';

interface VoiceCallChatProps {
  isOpen?: boolean;
  onClose?: () => void;
}

export default function VoiceCallChat({ isOpen = false, onClose }: VoiceCallChatProps) {
  const { currentUser } = useAuth();
  const { isVoiceCallingEnabled } = useChatbot();
  const { toast } = useToast();

  // Call states
  const [isCallActive, setIsCallActive] = useState(false);
  const [isRinging, setIsRinging] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isListening, setIsListening] = useState(false);

  // Speech synthesis
  const synth = useRef<SpeechSynthesis | null>(null);
  const recognition = useRef<SpeechRecognition | null>(null);

  // Chat history
  const [chatHistory, setChatHistory] = useState<Array<{
    sender: 'user' | 'bot';
    text: string;
    timestamp: Date;
  }>>([]);

  // Initialize speech synthesis and recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize speech synthesis
      synth.current = window.speechSynthesis;

      // Test speech synthesis
      console.log('Speech synthesis available:', typeof window.speechSynthesis !== 'undefined');

      // Check if SpeechRecognition is available
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      console.log('Speech recognition available:', typeof SpeechRecognition !== 'undefined');

      if (SpeechRecognition) {
        try {
          recognition.current = new SpeechRecognition();
          recognition.current.continuous = false;
          recognition.current.interimResults = false;
          recognition.current.lang = 'en-US';

          // Set up recognition event handlers
          recognition.current.onresult = (event) => {
            console.log('Speech recognition result received:', event);
            handleSpeechResult(event);
          };

          recognition.current.onerror = (event) => {
            console.error('Speech recognition error:', event.error, event.message);
            handleSpeechError(event);
          };

          recognition.current.onend = () => {
            console.log('Speech recognition ended');
            setIsListening(false);
          };

          recognition.current.onstart = () => {
            console.log('Speech recognition started');
            setIsListening(true);
          };

          console.log('Speech recognition initialized successfully');
        } catch (error) {
          console.error('Error initializing speech recognition:', error);
          toast({
            title: "Speech Recognition Error",
            description: "Could not initialize speech recognition. Please try a different browser.",
            variant: "destructive"
          });
        }
      } else {
        console.error('SpeechRecognition not available in this browser');
        toast({
          title: "Speech Recognition Unavailable",
          description: "Your browser doesn't support speech recognition. Try Chrome or Edge.",
          variant: "destructive"
        });
      }
    }

    // Add initial greeting message
    setChatHistory([
      {
        sender: 'bot',
        text: 'Welcome to the LegalAid voice-enabled chatbot! Click the call button to start a voice conversation.',
        timestamp: new Date()
      }
    ]);

    // Cleanup
    return () => {
      // Stop any ongoing speech
      speechSynthesis.stop();

      // Stop speech recognition
      if (recognition.current) {
        try {
          recognition.current.abort();
        } catch (error) {
          console.error('Error aborting speech recognition:', error);
        }
      }
    };
  }, [toast]);

  // Start the call
  const startCall = async () => {
    if (!isVoiceCallingEnabled) {
      toast({
        title: "Voice Calling Disabled",
        description: "Please enable voice calling in your settings",
        variant: "destructive"
      });
      return;
    }

    try {
      // Check if browser supports required APIs
      if (typeof window.SpeechRecognition === 'undefined' &&
          typeof window.webkitSpeechRecognition === 'undefined') {
        toast({
          title: "Speech Recognition Unavailable",
          description: "Your browser doesn't support speech recognition. Try Chrome or Edge.",
          variant: "destructive"
        });

        // Add a message to the chat
        addBotMessage("I can't start a voice call because your browser doesn't support speech recognition. Please try using Chrome or Edge instead.");
        return;
      }

      if (typeof window.speechSynthesis === 'undefined') {
        toast({
          title: "Speech Synthesis Unavailable",
          description: "Your browser doesn't support text-to-speech. Try Chrome or Edge.",
          variant: "destructive"
        });

        // Add a message to the chat
        addBotMessage("I can't start a voice call because your browser doesn't support text-to-speech. Please try using Chrome or Edge instead.");
        return;
      }

      // Request microphone permission
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        // Stop the stream immediately, we just needed to check permission
        stream.getTracks().forEach(track => track.stop());
      } catch (error) {
        console.error('Microphone permission denied:', error);
        toast({
          title: "Microphone Access Denied",
          description: "Please allow microphone access to use voice calling.",
          variant: "destructive"
        });

        // Add a message to the chat
        addBotMessage("I can't start a voice call because microphone access was denied. Please allow microphone access and try again.");
        return;
      }

      // Show ringing state
      setIsRinging(true);

      // Add connecting message
      addBotMessage("Connecting to voice assistant...");

      // Simulate connection delay
      setTimeout(() => {
        setIsRinging(false);
        setIsCallActive(true);

        // Get user's name for personalized greeting
        const userName = currentUser?.displayName || currentUser?.username || 'there';
        console.log('Current user for greeting:', currentUser);
        console.log('Using username for greeting:', userName);

        // Greet the user by name
        const greeting = `Hello ${userName}! How can I help you today?`;
        addBotMessage(greeting);

        // Speak the greeting
        speakText(greeting);

        // Add instructions
        setTimeout(() => {
          addBotMessage("You can ask me about the app, its features, or how to use it. Click the 'Click to Speak' button, say your question, then click again when you're done.");
        }, 1000);
      }, 1500);
    } catch (error) {
      console.error('Error starting call:', error);
      setIsRinging(false);

      toast({
        title: "Call Error",
        description: "Could not start the voice call. Please try again.",
        variant: "destructive"
      });

      // Add a message to the chat
      addBotMessage("I encountered an error starting the voice call. Please try again.");
    }
  };

  // End the call
  const endCall = () => {
    console.log('Ending call and cleaning up resources');

    // Cancel any ongoing speech
    speechSynthesis.stop();
    console.log('Speech synthesis canceled');

    // Stop speech recognition
    if (recognition.current) {
      try {
        // Remove event handlers first to prevent any callbacks
        recognition.current.onresult = null;
        recognition.current.onerror = null;
        recognition.current.onend = null;
        recognition.current.onstart = null;

        // Then abort
        recognition.current.abort();
        console.log('Speech recognition aborted');
      } catch (error) {
        console.error('Error aborting speech recognition:', error);
      }
    }

    // Update states
    setIsCallActive(false);
    setIsListening(false);
    setIsSpeaking(false);
    setIsRinging(false);

    // Add call ended message
    addBotMessage("Call ended. Click the call button to start a new conversation.");

    toast({
      title: "Call Ended",
      description: "Voice call has been ended.",
    });
  };

  // Toggle speech recognition (click to start/stop)
  const toggleSpeechRecognition = (event: React.MouseEvent | React.TouchEvent) => {
    // Prevent default behavior for touch events
    if ('touches' in event) {
      event.preventDefault();
    }

    // If AI is currently speaking, stop the speech
    if (isSpeaking) {
      speechSynthesis.stop();
      setIsSpeaking(false);
      return;
    }

    // If already listening, stop
    if (isListening) {
      stopSpeechRecognition();
      return;
    }

    // Otherwise start listening
    if (!isSpeaking) {
      // Start listening when button is clicked
      if (recognition.current) {
        try {
          // Create a fresh instance
          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
          recognition.current = new SpeechRecognition();
          recognition.current.continuous = false;
          recognition.current.interimResults = false;
          recognition.current.lang = 'en-US';

          recognition.current.onresult = (event) => {
            console.log('Speech recognition result received:', event);
            handleSpeechResult(event);
          };

          recognition.current.onerror = (event) => {
            console.error('Speech recognition error:', event.error, event.message);
            setIsListening(false);

            if (event.error !== 'no-speech' && event.error !== 'aborted') {
              toast({
                title: "Speech Recognition Error",
                description: `Error: ${event.error}`,
                variant: "destructive"
              });
            }
          };

          recognition.current.onend = () => {
            console.log('Speech recognition ended');
            setIsListening(false);
          };

          recognition.current.start();
          setIsListening(true);

          // No toast notification - just visual feedback in the button
          addBotMessage("I'm listening. Speak now and click the button again when you're done.");
        } catch (error) {
          console.error('Error starting speech recognition:', error);
          toast({
            title: "Speech Recognition Error",
            description: "Could not start listening.",
            variant: "destructive"
          });
        }
      }
    }
  };

  // Stop speech recognition
  const stopSpeechRecognition = () => {
    // Stop listening
    if (recognition.current) {
      try {
        recognition.current.stop();
      } catch (error) {
        console.error('Error stopping speech recognition:', error);
      }
    }
    setIsListening(false);
  };

  // Toggle mute (not used in push-to-talk mode, but kept for reference)
  const toggleMute = () => {
    setIsMuted(!isMuted);

    if (!isMuted) {
      // If we're muting, stop listening
      if (recognition.current && isListening) {
        recognition.current.abort();
        setIsListening(false);
      }
      toast({
        title: "Microphone Muted",
        description: "The chatbot can't hear you now"
      });
    } else {
      // If we're unmuting, start listening
      startListening();
      toast({
        title: "Microphone Unmuted",
        description: "The chatbot can hear you now"
      });
    }
  };

  // Start listening for speech
  const startListening = () => {
    // Don't start listening if muted or not in an active call
    if (isMuted || !isCallActive) {
      console.log('Not starting listening - muted or call not active');
      return;
    }

    // Don't start listening if already speaking
    if (isSpeaking) {
      console.log('Not starting listening - currently speaking');
      return;
    }

    if (recognition.current) {
      try {
        // Check if already listening
        if (isListening) {
          console.log('Already listening, not starting again');
          return;
        }

        console.log('Starting speech recognition...');

        // Recreate the recognition object to avoid any potential issues
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        if (SpeechRecognition) {
          // Clean up existing recognition if needed
          if (recognition.current) {
            try {
              recognition.current.onresult = null;
              recognition.current.onerror = null;
              recognition.current.onend = null;
              recognition.current.onstart = null;
              recognition.current.abort();
            } catch (e) {
              console.log('Error cleaning up existing recognition:', e);
            }
          }

          // Create a fresh instance
          recognition.current = new SpeechRecognition();
          recognition.current.continuous = false;
          recognition.current.interimResults = false;
          recognition.current.lang = 'en-US';

          // Set up event handlers
          recognition.current.onresult = (event) => {
            console.log('Speech recognition result received:', event);
            handleSpeechResult(event);
          };

          recognition.current.onerror = (event) => {
            console.error('Speech recognition error:', event.error, event.message);
            handleSpeechError(event);
          };

          recognition.current.onend = () => {
            console.log('Speech recognition ended');
            setIsListening(false);
          };

          recognition.current.onstart = () => {
            console.log('Speech recognition started');
            setIsListening(true);
          };

          // Start recognition
          recognition.current.start();

          // Add visual feedback (but not too frequently to avoid toast spam)
          if (!isListening) {
            toast({
              title: "Listening",
              description: "Speak now...",
            });
          }
        }
      } catch (error) {
        console.error('Error starting speech recognition:', error);

        // Only show toast if this is a real error, not just a state issue
        if (error.name !== 'InvalidStateError') {
          toast({
            title: "Speech Recognition Error",
            description: "Could not start listening. Trying again...",
            variant: "destructive"
          });
        }

        // Try again after a short delay
        setTimeout(() => {
          // Only try again if we're still in an active call
          if (isCallActive && !isSpeaking && !isListening) {
            console.log('Attempting to restart speech recognition after error');
            try {
              // Create a fresh instance
              const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
              recognition.current = new SpeechRecognition();
              recognition.current.continuous = false;
              recognition.current.interimResults = false;
              recognition.current.lang = 'en-US';

              recognition.current.onresult = (event) => {
                console.log('Speech recognition result received:', event);
                handleSpeechResult(event);
              };

              recognition.current.onerror = (event) => {
                console.error('Speech recognition error:', event.error, event.message);
                handleSpeechError(event);
              };

              recognition.current.onend = () => {
                console.log('Speech recognition ended');
                setIsListening(false);
              };

              recognition.current.onstart = () => {
                console.log('Speech recognition started');
                setIsListening(true);
              };

              recognition.current.start();
            } catch (e) {
              console.error('Failed to restart speech recognition:', e);
            }
          }
        }, 1000);
      }
    } else {
      toast({
        title: "Speech Recognition Unavailable",
        description: "Your browser doesn't support speech recognition. Try Chrome or Edge.",
        variant: "destructive"
      });

      // Add a fallback message
      addBotMessage("I can't hear you because speech recognition isn't supported in this browser. Please try Chrome or Edge instead.");
    }
  };

  // Handle speech recognition result
  const handleSpeechResult = (event: SpeechRecognitionEvent) => {
    try {
      console.log('Processing speech result:', event);

      if (event.results && event.results.length > 0 && event.results[0].length > 0) {
        const transcript = event.results[0][0].transcript;
        console.log('Transcript:', transcript);

        if (transcript && transcript.trim() !== '') {
          // Add user message to chat
          addUserMessage(transcript);

          // Process the user's message and respond
          processUserMessage(transcript);
        } else {
          console.log('Empty transcript received');
          // Start listening again
          setTimeout(() => {
            startListening();
          }, 1000);
        }
      } else {
        console.error('Invalid speech recognition results structure:', event.results);
        // Start listening again
        setTimeout(() => {
          startListening();
        }, 1000);
      }
    } catch (error) {
      console.error('Error handling speech result:', error);
      toast({
        title: "Speech Processing Error",
        description: "Could not process your speech. Please try again.",
        variant: "destructive"
      });

      // Start listening again
      setTimeout(() => {
        startListening();
      }, 1000);
    }
  };

  // Handle speech recognition error
  const handleSpeechError = (event: SpeechRecognitionErrorEvent) => {
    console.error('Speech recognition error:', event.error, event.message);

    if (event.error === 'no-speech') {
      // If no speech was detected, start listening again
      addBotMessage("I didn't hear anything. Please try speaking again.");
      setTimeout(() => {
        startListening();
      }, 1000);
    } else if (event.error === 'aborted') {
      console.log('Speech recognition was aborted - this is often normal when stopping one recognition to start another');
      // Don't show an error message for aborted as it's often part of normal operation
      // Only restart listening if we're still in an active call
      if (isCallActive && !isSpeaking) {
        setTimeout(() => {
          startListening();
        }, 1000);
      }
    } else if (event.error === 'network') {
      toast({
        title: "Network Error",
        description: "There was a network error with speech recognition.",
        variant: "destructive"
      });
      addBotMessage("I'm having trouble connecting to the speech recognition service. Please check your internet connection.");

      // Try again after a delay
      if (isCallActive) {
        setTimeout(() => {
          startListening();
        }, 3000);
      }
    } else if (event.error === 'not-allowed') {
      toast({
        title: "Microphone Access Denied",
        description: "Please allow microphone access to use voice calling.",
        variant: "destructive"
      });
      addBotMessage("I can't hear you because microphone access was denied. Please allow microphone access in your browser settings.");
    } else if (event.error === 'service-not-allowed') {
      toast({
        title: "Speech Service Error",
        description: "The speech recognition service is not allowed.",
        variant: "destructive"
      });
      addBotMessage("There's an issue with the speech recognition service. Please try again later.");
    } else {
      toast({
        title: "Speech Recognition Error",
        description: `Error: ${event.error}`,
        variant: "destructive"
      });
      addBotMessage("I'm having trouble understanding you. Please try again or type your message instead.");

      // Try again after a delay for other errors
      if (isCallActive) {
        setTimeout(() => {
          startListening();
        }, 2000);
      }
    }
  };

  // Process user message and generate response
  const processUserMessage = (message: string) => {
    console.log('Processing user message:', message);
    const lowerMessage = message.toLowerCase();
    let response = '';

    // Get user's name for personalized responses
    const userName = currentUser?.displayName || currentUser?.username || 'there';
    console.log('Current user:', currentUser);
    console.log('Using username:', userName);

    // Log the message for debugging
    console.log(`Processing: "${lowerMessage}"`);

    // Check for platform capability phrases that might not be caught by specific patterns
    if ((lowerMessage.includes('platform') &&
        (lowerMessage.includes('can') || lowerMessage.includes('do') || lowerMessage.includes('feature'))) ||
        (lowerMessage.includes('legalaid') &&
        (lowerMessage.includes('can') || lowerMessage.includes('do') || lowerMessage.includes('feature'))) ||
        (lowerMessage.includes('legal') && lowerMessage.includes('aid') &&
        (lowerMessage.includes('can') || lowerMessage.includes('do') || lowerMessage.includes('feature')))) {
      console.log("Detected platform capability question via general pattern");
      response = "LegalAid offers several features: You can watch legal education videos, chat with video creators, translate content to different languages, and access a variety of legal resources. The platform supports multiple languages and provides AI-assisted interactions. You can also use voice commands like you're doing now! The platform includes a video player with language selection, real-time chat with translation, and a creator dashboard for uploading content.";

      // Add bot response to chat
      addBotMessage(response);

      // Speak the response
      speakText(response);

      return; // Exit early
    }

    try {
      // Check for greeting
      if (lowerMessage.includes('hi') ||
          lowerMessage.includes('hello') ||
          lowerMessage.includes('hey') ||
          lowerMessage.includes('hola') ||
          lowerMessage.includes('namaste')) {
        response = `Hello ${userName}! How can I help you today?`;
      }
      // Check for user's name
      else if (lowerMessage.includes('my name') ||
               lowerMessage.includes('who am i')) {
        response = `Your name is ${userName}. How can I assist you today?`;
      }
      // Check for platform or app information request
      else if ((lowerMessage.includes('about') && (lowerMessage.includes('app') || lowerMessage.includes('platform'))) ||
               (lowerMessage.includes('what') && (lowerMessage.includes('app') || lowerMessage.includes('platform'))) ||
               (lowerMessage.includes('tell') && (lowerMessage.includes('app') || lowerMessage.includes('platform'))) ||
               (lowerMessage.includes('platform') && lowerMessage.includes('information')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('is') && lowerMessage.includes('this')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('is') && lowerMessage.includes('legalaid')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('is') && lowerMessage.includes('legal') && lowerMessage.includes('aid')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('legalaid')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('legal') && lowerMessage.includes('aid'))) {
        response = "This is LegalAid, a platform for legal education videos with multilingual support. You can watch videos, chat with creators, and access legal content in multiple languages. The platform aims to make legal education accessible to everyone regardless of language barriers. It features video playback in different languages, real-time chat with translation, and this voice assistant to help you navigate the platform.";
      }
      // Check for features request
      else if ((lowerMessage.includes('what') && lowerMessage.includes('do')) ||
               (lowerMessage.includes('feature')) ||
               (lowerMessage.includes('can') && lowerMessage.includes('do')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('offer')) ||
               (lowerMessage.includes('platform') && lowerMessage.includes('feature')) ||
               (lowerMessage.includes('platform') && lowerMessage.includes('capability')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('platform') && lowerMessage.includes('can')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('platform') && lowerMessage.includes('do')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('this') && lowerMessage.includes('platform') && lowerMessage.includes('do')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('this') && lowerMessage.includes('platform') && lowerMessage.includes('can')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('legalaid') && lowerMessage.includes('do')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('legalaid') && lowerMessage.includes('can')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('legal') && lowerMessage.includes('aid') && lowerMessage.includes('do')) ||
               (lowerMessage.includes('what') && lowerMessage.includes('legal') && lowerMessage.includes('aid') && lowerMessage.includes('can'))) {
        response = "LegalAid offers several features: You can watch legal education videos, chat with video creators, translate content to different languages, and access a variety of legal resources. The platform supports multiple languages and provides AI-assisted interactions. You can also use voice commands like you're doing now! The platform includes a video player with language selection, real-time chat with translation, and a creator dashboard for uploading content.";
      }
      // Check for language support question
      else if (lowerMessage.includes('language') ||
               lowerMessage.includes('languages') ||
               lowerMessage.includes('translate') ||
               lowerMessage.includes('translation')) {
        response = "LegalAid supports multiple languages including English, Hindi, and several other Indian languages. Videos can be viewed in different languages, and chat messages can be translated. The platform uses advanced translation technology to ensure accurate translations of legal terminology.";
      }
      // Check for how to use question
      else if ((lowerMessage.includes('how') && lowerMessage.includes('use')) ||
               (lowerMessage.includes('guide')) ||
               (lowerMessage.includes('tutorial')) ||
               (lowerMessage.includes('help'))) {
        response = "To use LegalAid, browse videos on the home page, click on a video to watch it, use the language selector to change video language, and use the chat feature to communicate with video creators or the AI assistant. You can also use voice commands for a hands-free experience. If you need help with specific features, just ask!";
      }
      // Check for who made this app
      else if ((lowerMessage.includes('who') && lowerMessage.includes('made')) ||
               (lowerMessage.includes('creator')) ||
               (lowerMessage.includes('developer'))) {
        response = "LegalAid was developed by a team of developers focused on making legal education accessible across language barriers. The platform combines video content, AI assistance, and translation technology to provide a comprehensive learning experience.";
      }
      // Basic math questions - check for math expressions first
      // But make sure it's actually a math question by checking for numbers or math operators
      else if ((lowerMessage.includes('what is') && (lowerMessage.match(/\d+/) || lowerMessage.includes('plus') || lowerMessage.includes('minus') || lowerMessage.includes('times') || lowerMessage.includes('divided'))) ||
               (lowerMessage.includes('what\'s') && (lowerMessage.match(/\d+/) || lowerMessage.includes('plus') || lowerMessage.includes('minus') || lowerMessage.includes('times') || lowerMessage.includes('divided'))) ||
               lowerMessage.includes('calculate') ||
               lowerMessage.includes('compute') ||
               lowerMessage.includes('solve') ||
               lowerMessage.match(/\d+\s*[\+\-\*\/]\s*\d+/) ||  // Match patterns like "5 + 5"
               (lowerMessage.includes('add') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('plus') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('sum') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('subtract') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('minus') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('multiply') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('times') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('divide') && lowerMessage.match(/\d+/)) ||
               (lowerMessage.includes('divided by') && lowerMessage.match(/\d+/))) {

        console.log("Detected math question:", message);

        // Try to extract a math expression
        let mathExpression = null;

        // Check for patterns like "5 + 5" or "5+5"
        const directPattern = /(\d+)\s*([\+\-\*\/])\s*(\d+)/;
        const directMatch = lowerMessage.match(directPattern);

        if (directMatch) {
          const num1 = parseInt(directMatch[1]);
          const operator = directMatch[2];
          const num2 = parseInt(directMatch[3]);

          console.log(`Detected direct math expression: ${num1} ${operator} ${num2}`);

          switch(operator) {
            case '+':
              response = `${num1} plus ${num2} equals ${num1 + num2}.`;
              break;
            case '-':
              response = `${num1} minus ${num2} equals ${num1 - num2}.`;
              break;
            case '*':
              response = `${num1} multiplied by ${num2} equals ${num1 * num2}.`;
              break;
            case '/':
              if (num2 === 0) {
                response = "I can't divide by zero. That's undefined.";
              } else {
                response = `${num1} divided by ${num2} equals ${num1 / num2}.`;
              }
              break;
          }
        }
        // Check for "what is X plus/minus/times/divided by Y" patterns
        else if (lowerMessage.includes('what is') || lowerMessage.includes('what\'s')) {
          // Extract numbers from the message
          const numbers = message.match(/\d+/g);
          if (numbers && numbers.length >= 2) {
            const num1 = parseInt(numbers[0]);
            const num2 = parseInt(numbers[1]);

            console.log(`Detected 'what is' math with numbers: ${num1} and ${num2}`);

            if (lowerMessage.includes('plus') || lowerMessage.includes('+')) {
              response = `${num1} plus ${num2} equals ${num1 + num2}.`;
            } else if (lowerMessage.includes('minus') || lowerMessage.includes('-')) {
              response = `${num1} minus ${num2} equals ${num1 - num2}.`;
            } else if (lowerMessage.includes('times') || lowerMessage.includes('multiply') || lowerMessage.includes('*')) {
              response = `${num1} multiplied by ${num2} equals ${num1 * num2}.`;
            } else if (lowerMessage.includes('divide') || lowerMessage.includes('/')) {
              if (num2 === 0) {
                response = "I can't divide by zero. That's undefined.";
              } else {
                response = `${num1} divided by ${num2} equals ${num1 / num2}.`;
              }
            } else {
              // If we have two numbers but no clear operation, assume addition
              response = `${num1} plus ${num2} equals ${num1 + num2}.`;
            }
          }
        }
        // Check for other math keywords
        else {
          // Extract numbers from the message
          const numbers = message.match(/\d+/g);
          if (numbers && numbers.length >= 2) {
            const num1 = parseInt(numbers[0]);
            const num2 = parseInt(numbers[1]);

            console.log(`Detected keyword math with numbers: ${num1} and ${num2}`);

            if (lowerMessage.includes('add') || lowerMessage.includes('plus') || lowerMessage.includes('sum')) {
              response = `${num1} plus ${num2} equals ${num1 + num2}.`;
            } else if (lowerMessage.includes('subtract') || lowerMessage.includes('minus')) {
              response = `${num1} minus ${num2} equals ${num1 - num2}.`;
            } else if (lowerMessage.includes('multiply') || lowerMessage.includes('times')) {
              response = `${num1} multiplied by ${num2} equals ${num1 * num2}.`;
            } else if (lowerMessage.includes('divide') || lowerMessage.includes('divided by')) {
              if (num2 === 0) {
                response = "I can't divide by zero. That's undefined.";
              } else {
                response = `${num1} divided by ${num2} equals ${num1 / num2}.`;
              }
            } else {
              response = "I'm not sure what calculation you want me to perform. Please specify add, subtract, multiply, or divide.";
            }
          } else {
            response = "I need two numbers to perform a calculation. Please specify the numbers clearly.";
          }
        }

        // If we couldn't parse the math expression
        if (!response) {
          response = "I'm sorry, I couldn't understand that math question. Please try rephrasing it, for example: 'What is 5 plus 7?' or '10 divided by 2'";
        }
      }
      // Science questions
      else if (lowerMessage.includes('science') ||
               lowerMessage.includes('physics') ||
               lowerMessage.includes('chemistry') ||
               lowerMessage.includes('biology')) {
        if (lowerMessage.includes('gravity')) {
          response = "Gravity is the force that attracts objects toward each other. On Earth, gravity pulls objects downward at an acceleration of approximately 9.8 meters per second squared.";
        } else if (lowerMessage.includes('element') || lowerMessage.includes('periodic table')) {
          response = "The periodic table organizes chemical elements by their atomic number, electron configuration, and chemical properties. There are 118 confirmed elements, with hydrogen being the first and oganesson being the 118th.";
        } else if (lowerMessage.includes('dna') || lowerMessage.includes('gene')) {
          response = "DNA (deoxyribonucleic acid) is the genetic material that carries the instructions for development, functioning, growth, and reproduction in all living organisms. It consists of two strands coiled around each other in a double helix structure.";
        } else if (lowerMessage.includes('solar system')) {
          response = "Our solar system consists of the Sun, eight planets (Mercury, Venus, Earth, Mars, Jupiter, Saturn, Uranus, and Neptune), dwarf planets like Pluto, moons, asteroids, comets, and other celestial bodies.";
        } else {
          response = "I can answer basic science questions about physics, chemistry, biology, and more. Please ask a more specific science question.";
        }
      }
      // History questions
      else if (lowerMessage.includes('history') ||
               lowerMessage.includes('historical') ||
               lowerMessage.includes('ancient')) {
        if (lowerMessage.includes('world war')) {
          response = "World War I lasted from 1914 to 1918, and World War II lasted from 1939 to 1945. Both were global conflicts that involved many of the world's nations and resulted in significant geopolitical changes.";
        } else if (lowerMessage.includes('india') && lowerMessage.includes('independence')) {
          response = "India gained independence from British rule on August 15, 1947, after a long struggle for freedom led by figures like Mahatma Gandhi, Jawaharlal Nehru, and many others.";
        } else if (lowerMessage.includes('constitution')) {
          response = "The Constitution of India came into effect on January 26, 1950. It is the longest written constitution of any sovereign country in the world, containing 448 articles in 25 parts, 12 schedules, and 104 amendments.";
        } else {
          response = "I can answer basic history questions. Please ask a more specific history question.";
        }
      }
      // Legal questions
      else if (lowerMessage.includes('law') ||
               lowerMessage.includes('legal') ||
               lowerMessage.includes('right') ||
               lowerMessage.includes('constitution')) {
        if (lowerMessage.includes('fundamental right')) {
          response = "The Constitution of India guarantees six fundamental rights: Right to Equality, Right to Freedom, Right against Exploitation, Right to Freedom of Religion, Cultural and Educational Rights, and Right to Constitutional Remedies.";
        } else if (lowerMessage.includes('article 21')) {
          response = "Article 21 of the Indian Constitution protects the right to life and personal liberty. It states that 'No person shall be deprived of his life or personal liberty except according to procedure established by law.'";
        } else if (lowerMessage.includes('ipc') || lowerMessage.includes('indian penal code')) {
          response = "The Indian Penal Code (IPC) is the official criminal code of India, covering all substantive aspects of criminal law. It was drafted in 1860 and came into force in 1862 during the British Raj.";
        } else {
          response = "I can answer basic legal questions. Please ask a more specific legal question.";
        }
      }
      // Thank you
      else if (lowerMessage.includes('thank') ||
               lowerMessage.includes('thanks') ||
               lowerMessage.includes('appreciate')) {
        response = `You're welcome, ${userName}! I'm happy to help. Is there anything else you'd like to know?`;
      }
      // Goodbye
      else if (lowerMessage.includes('bye') ||
               lowerMessage.includes('goodbye') ||
               lowerMessage.includes('see you')) {
        response = `Goodbye, ${userName}! Feel free to start another voice call anytime you need assistance.`;
      }
      // Check for user asking the bot's name
      else if ((lowerMessage.includes('your') && lowerMessage.includes('name')) ||
               (lowerMessage.includes('who') && lowerMessage.includes('are')) ||
               lowerMessage.includes('chatbot')) {
        response = `I'm the LegalAid Voice Assistant, ${userName}. I'm designed to help you navigate the platform and answer your questions about legal education content, as well as general knowledge questions.`;
      }
      // Check for any other platform-related questions
      else if (lowerMessage.includes('platform') ||
               lowerMessage.includes('legalaid') ||
               lowerMessage.includes('legal') && lowerMessage.includes('aid') ||
               lowerMessage.includes('website') ||
               lowerMessage.includes('site') ||
               lowerMessage.includes('application')) {
        response = `The LegalAid platform is designed to make legal education accessible across language barriers. It features multilingual video content, real-time chat with translation capabilities, and AI-assisted interactions like this voice assistant. You can browse videos, select your preferred language, chat with content creators, and access a variety of legal resources. Is there something specific about the platform you'd like to know, ${userName}?`;
      }
      // Default response
      else {
        response = `I'm sorry, ${userName}, I didn't quite understand that question. You can ask me about the platform, its features, language support, or how to use it. I can also answer basic questions about math, science, history, and law.`;
      }

      console.log('Generated response:', response);

      // Add bot response to chat
      addBotMessage(response);

      // Speak the response
      speakText(response);
    } catch (error) {
      console.error('Error processing message:', error);

      // Fallback response
      const fallbackResponse = `I'm sorry, ${userName}, I encountered an error processing your request. Could you please try asking something else?`;
      addBotMessage(fallbackResponse);
      speakText(fallbackResponse);
    }
  };

  // Speak text using speech synthesis
  const speakText = (text: string) => {
    try {
      console.log('Speaking text:', text);

      // Set speaking state
      setIsSpeaking(true);

      // Use our speech synthesis utility
      speechSynthesis.speak(text, {
        rate: 1.0,
        pitch: 1.0,
        onStart: () => {
          console.log('Speech started');
          setIsSpeaking(true);
        },
        onEnd: () => {
          console.log('Speech ended');
          setIsSpeaking(false);
          // Start listening again after speaking
          setTimeout(() => {
            startListening();
          }, 500);
        },
        onError: (error) => {
          console.error('Speech synthesis error:', error);
          setIsSpeaking(false);

          // Show error message
          toast({
            title: "Speech Synthesis Error",
            description: "Could not speak the response. Please check your audio settings.",
            variant: "destructive"
          });

          // Start listening again even if speaking fails
          setTimeout(() => {
            startListening();
          }, 1000);
        }
      });
    } catch (error) {
      console.error('Error with speech synthesis:', error);
      setIsSpeaking(false);

      // Show error message
      toast({
        title: "Speech Synthesis Error",
        description: "Could not speak the response. Please check your audio settings.",
        variant: "destructive"
      });

      // Start listening again even if speaking fails
      setTimeout(() => {
        startListening();
      }, 1000);
    }
  };

  // We no longer need the finishSpeaking function as we're using the speechSynthesis utility

  // Add a user message to the chat history
  const addUserMessage = (text: string) => {
    setChatHistory(prev => [
      ...prev,
      {
        sender: 'user',
        text: text,
        timestamp: new Date()
      }
    ]);
  };

  // Add a bot message to the chat history
  const addBotMessage = (text: string) => {
    setChatHistory(prev => [
      ...prev,
      {
        sender: 'bot',
        text: text,
        timestamp: new Date()
      }
    ]);
  };

  // If the component is not open, show a minimized version
  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          className="h-14 w-14 rounded-full bg-primary shadow-lg hover:bg-primary/90 text-white"
          onClick={onClose}
        >
          <Phone className="h-6 w-6" />
        </Button>
      </div>
    );
  }

  return (
    <Card className="fixed bottom-6 right-6 w-80 md:w-96 shadow-lg z-50 flex flex-col max-h-[500px]">
      <CardHeader className="p-3 flex flex-row items-center justify-between space-y-0 bg-primary text-white">
        <div className="flex items-center space-x-2">
          <Avatar className="h-8 w-8">
            <AvatarImage src="/bot-avatar.png" alt="AI Assistant" />
            <AvatarFallback>AI</AvatarFallback>
          </Avatar>
          <div>
            <h4 className="font-semibold">LegalAid Voice Assistant</h4>
            <p className="text-xs">
              {isCallActive
                ? (isSpeaking ? "Speaking..." : (isListening ? "Listening..." : "Ready"))
                : (isRinging ? "Connecting..." : "Disconnected")}
            </p>
          </div>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose} className="text-white hover:bg-primary-foreground/10">
          <PhoneOff className="h-4 w-4" />
        </Button>
      </CardHeader>

      <CardContent className="p-3 overflow-y-auto flex-1">
        <div className="space-y-4">
          {chatHistory.map((message, index) => (
            <div
              key={index}
              className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] rounded-lg px-3 py-2 ${
                  message.sender === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                <p className="text-sm">{message.text}</p>
                <p className="text-xs opacity-50 mt-1">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>

      <CardFooter className="p-3 border-t">
        <div className="flex w-full gap-2 justify-between">
          {isCallActive ? (
            <>
              {/* Push-to-talk button */}
              <Button
                variant={isListening ? "default" : "outline"}
                className={`flex-1 ${isListening ? "bg-green-500 hover:bg-green-600" : ""}`}
                onClick={toggleSpeechRecognition}
                disabled={false}
              >
                {isSpeaking ? (
                  <>
                    <Volume2 className="h-4 w-4 mr-2 animate-pulse" />
                    Click to Stop AI
                  </>
                ) : isListening ? (
                  <>
                    <Mic className="h-4 w-4 mr-2 animate-pulse" />
                    Click to Stop
                  </>
                ) : (
                  <>
                    <Mic className="h-4 w-4 mr-2" />
                    Click to Speak
                  </>
                )}
              </Button>

              <Button
                variant="destructive"
                onClick={endCall}
                className="w-24"
              >
                End Call
              </Button>
            </>
          ) : (
            <Button
              variant="default"
              onClick={startCall}
              className="flex-1"
              disabled={isRinging}
            >
              {isRinging ? "Connecting..." : "Start Call"}
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
