import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Home, ChevronRight, Bell, Send, Link2, Users, AlertCircle, Info, MessageSquare, Calendar, Clock, Trash2, History, Eye, EyeOff } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Mock data for sent notifications
const mockSentNotifications = [
  { id: '1', text: 'System maintenance scheduled for tomorrow', type: 'info', url: 'https://example.com/maintenance', sentTo: 'All Users', date: '2023-07-15', time: '14:30' },
  { id: '2', text: 'New feature released: Dark Mode', type: 'success', url: 'https://example.com/features', sentTo: 'Premium Users', date: '2023-07-10', time: '09:15' },
  { id: '3', text: 'Please update your profile information', type: 'warning', url: 'https://example.com/profile', sentTo: 'Incomplete Profiles', date: '2023-07-05', time: '11:45' },
];

export default function MassNotificationsPage() {
  const [url, setUrl] = useState('');
  const [notificationText, setNotificationText] = useState('');
  const [selectedUsers, setSelectedUsers] = useState('');
  const [notificationType, setNotificationType] = useState('info');
  const [showPreview, setShowPreview] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [activeTab, setActiveTab] = useState('compose');
  const [sentNotifications, setSentNotifications] = useState(mockSentNotifications);

  const handleSendNotifications = () => {
    if (!notificationText.trim()) {
      alert('Please enter notification text');
      return;
    }

    // In a real app, this would send the notifications to the selected users or all users
    const recipients = selectedUsers.trim() ? selectedUsers : 'all users';

    // Add to sent notifications history
    const now = new Date();
    const newNotification = {
      id: (Math.max(...sentNotifications.map(n => parseInt(n.id)), 0) + 1).toString(),
      text: notificationText,
      type: notificationType,
      url: url,
      sentTo: selectedUsers.trim() ? 'Selected Users' : 'All Users',
      date: now.toISOString().split('T')[0],
      time: now.toTimeString().split(' ')[0].substring(0, 5)
    };

    setSentNotifications([newNotification, ...sentNotifications]);

    // Show success message
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);

    // Reset form
    setUrl('');
    setNotificationText('');
    setSelectedUsers('');
    setNotificationType('info');

    // Switch to history tab
    setActiveTab('history');
  };

  const getNotificationIcon = () => {
    switch (notificationType) {
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      case 'success':
        return <AlertCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Bell className="h-5 w-5 text-blue-500" />;
    }
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Mass Notifications</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Tools
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Mass Notifications</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  Notifications have been sent successfully to {selectedUsers.trim() ? selectedUsers : 'all users'}.
                </AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="compose" className="w-full" value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="compose">Compose Notification</TabsTrigger>
                <TabsTrigger value="history">Notification History</TabsTrigger>
              </TabsList>

              <TabsContent value="compose">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="md:col-span-2">
                    <Card>
                      <CardHeader>
                        <CardTitle>Send Site Notifications</CardTitle>
                        <CardDescription>Send notifications to all users or specific users</CardDescription>
                      </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="notificationType">Notification Type</Label>
                      <Select value={notificationType} onValueChange={setNotificationType}>
                        <SelectTrigger id="notificationType">
                          <SelectValue placeholder="Select notification type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="info">Information</SelectItem>
                          <SelectItem value="success">Success</SelectItem>
                          <SelectItem value="warning">Warning</SelectItem>
                          <SelectItem value="error">Error</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="url">URL Link (when user clicks on the notification)</Label>
                      <div className="flex items-center space-x-2">
                        <Link2 className="h-4 w-4 text-muted-foreground" />
                        <Input
                          id="url"
                          value={url}
                          onChange={(e) => setUrl(e.target.value)}
                          placeholder="https://"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="notificationText">Notification Text</Label>
                      <Textarea
                        id="notificationText"
                        value={notificationText}
                        onChange={(e) => setNotificationText(e.target.value)}
                        placeholder="Enter notification text here"
                        className="min-h-[120px]"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="selectedUsers">Target Users (leave empty to send to all users)</Label>
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <Input
                          id="selectedUsers"
                          value={selectedUsers}
                          onChange={(e) => setSelectedUsers(e.target.value)}
                          placeholder="Enter user IDs separated by commas"
                        />
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {selectedUsers.trim() ? 'Notification will be sent to specific users' : 'Notification will be sent to all users'}
                      </p>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="showPreview"
                        checked={showPreview}
                        onCheckedChange={setShowPreview}
                      />
                      <Label htmlFor="showPreview">Show preview</Label>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button onClick={handleSendNotifications} className="gap-2">
                      <Send className="h-4 w-4" />
                      Send Notifications
                    </Button>
                  </CardFooter>
                </Card>
              </div>

              <div>
                <Card>
                  <CardHeader>
                    <CardTitle>Notification Preview</CardTitle>
                    <CardDescription>See how your notification will appear to users</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {showPreview ? (
                      <div className="border rounded-lg p-4 bg-muted/20">
                        <div className="flex items-start gap-3">
                          {getNotificationIcon()}
                          <div className="flex-1">
                            <div className="font-medium">
                              {notificationText || 'Notification text will appear here'}
                            </div>
                            {url && (
                              <div className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                                <Link2 className="h-3 w-3" />
                                <span className="truncate">{url}</span>
                              </div>
                            )}
                            <div className="text-xs text-muted-foreground mt-2">
                              Just now
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center justify-center py-8 text-center text-muted-foreground">
                        <MessageSquare className="h-12 w-12 mb-2 opacity-20" />
                        <p>Enable preview to see how your notification will look</p>
                      </div>
                    )}
                  </CardContent>
                  <Separator />
                  <CardContent className="pt-4">
                    <h3 className="text-sm font-medium mb-2">Recipients</h3>
                    {selectedUsers.trim() ? (
                      <div className="flex flex-wrap gap-2">
                        {selectedUsers.split(',').map((user, index) => (
                          <Badge key={index} variant="outline" className="px-2 py-1">
                            User ID: {user.trim()}
                          </Badge>
                        ))}
                      </div>
                    ) : (
                      <Badge variant="outline" className="px-2 py-1">All Users</Badge>
                    )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Notification History</CardTitle>
                    <CardDescription>View previously sent notifications</CardDescription>
                  </CardHeader>
                  <CardContent>
                    {sentNotifications.length > 0 ? (
                      <div className="space-y-4">
                        {sentNotifications.map((notification) => (
                          <Card key={notification.id} className="overflow-hidden">
                            <div className="p-4 border-b bg-muted/30 flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                {notification.type === 'info' && <Info className="h-4 w-4 text-blue-500" />}
                                {notification.type === 'success' && <AlertCircle className="h-4 w-4 text-green-500" />}
                                {notification.type === 'warning' && <AlertCircle className="h-4 w-4 text-yellow-500" />}
                                {notification.type === 'error' && <AlertCircle className="h-4 w-4 text-red-500" />}
                                <h3 className="font-medium truncate max-w-[300px]">{notification.text}</h3>
                              </div>
                              <Badge variant="outline" className={notification.type === 'info' ? 'bg-blue-50 text-blue-700' :
                                notification.type === 'success' ? 'bg-green-50 text-green-700' :
                                notification.type === 'warning' ? 'bg-yellow-50 text-yellow-700' :
                                'bg-red-50 text-red-700'}>
                                {notification.type.charAt(0).toUpperCase() + notification.type.slice(1)}
                              </Badge>
                            </div>
                            <CardContent className="p-4">
                              <div className="flex flex-col gap-2">
                                {notification.url && (
                                  <div className="flex items-center gap-2 text-sm">
                                    <Link2 className="h-4 w-4 text-muted-foreground" />
                                    <a href={notification.url} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline truncate">
                                      {notification.url}
                                    </a>
                                  </div>
                                )}
                                <div className="flex items-center gap-4 text-xs text-muted-foreground mt-2">
                                  <div className="flex items-center">
                                    <Users className="h-3 w-3 mr-1" />
                                    <span>Sent to: {notification.sentTo}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <Calendar className="h-3 w-3 mr-1" />
                                    <span>{notification.date}</span>
                                  </div>
                                  <div className="flex items-center">
                                    <Clock className="h-3 w-3 mr-1" />
                                    <span>{notification.time}</span>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        <Bell className="h-12 w-12 mx-auto mb-2 opacity-20" />
                        <p>No notifications have been sent yet</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </div>
  );
}
