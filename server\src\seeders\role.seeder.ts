import { RoleModel, PermissionModel } from '../models';

/**
 * Seed roles data
 */
async function seedRoles() {
  try {
    // Clear existing roles
    await RoleModel.deleteMany({});
    
    // Get all permission codes for reference
    const permissions = await PermissionModel.find({}, 'code');
    const permissionCodes = permissions.map(permission => permission.code);
    
    // Define roles to seed
    const roles = [
      {
        name: 'Administrator',
        description: 'Full system access with all permissions',
        code: 'admin',
        permissions: permissionCodes, // All permissions
        isSystem: true,
        isActive: true,
        priority: 10,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Moderator',
        description: 'Content moderation capabilities',
        code: 'moderator',
        permissions: [
          'user:read',
          'video:read',
          'video:update',
          'video:moderate',
          'message:read',
          'message:delete',
        ],
        isSystem: true,
        isActive: true,
        priority: 20,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Content Creator',
        description: 'Can create and manage their own content',
        code: 'creator',
        permissions: [
          'video:create',
          'video:read',
          'video:update',
          'video:delete',
          'message:create',
          'message:read',
          'message:delete',
        ],
        isSystem: true,
        isActive: true,
        priority: 30,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'User',
        description: 'Standard user with basic permissions',
        code: 'user',
        permissions: [
          'video:read',
          'message:create',
          'message:read',
          'message:delete',
        ],
        isSystem: true,
        isActive: true,
        priority: 100,
        isAssignable: true,
        createdBy: 'system',
        updatedBy: 'system',
      },
    ];
    
    // Insert roles
    await RoleModel.insertMany(roles);
    
    console.log(`✅ ${roles.length} roles seeded successfully`);
  } catch (error) {
    console.error('❌ Error seeding roles:', error);
    throw error;
  }
}

export default seedRoles;
