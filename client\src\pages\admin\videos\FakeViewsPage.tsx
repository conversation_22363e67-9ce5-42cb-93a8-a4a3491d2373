import React, { useState, useEffect } from 'react';
import { useN<PERSON><PERSON>, usePara<PERSON>, Link } from 'react-router-dom';
import { Home, ChevronRight, ArrowLeft, Eye, BarChart2, TrendingUp, Clock, Save, RefreshCw } from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { AlertCircle, Info } from 'lucide-react';

interface VideoData {
  id: number;
  videoId: string;
  title: string;
  thumbnail?: string;
  views: number;
  category: string;
  source: string;
  privacy: string;
  addedBy: string;
  approved: string;
  duration?: string;
  createdAt?: string;
}

export default function FakeViewsPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [activeTab, setActiveTab] = useState('basic');

  // Mock data for demonstration
  const [video, setVideo] = useState<VideoData>({
    id: parseInt(id || '0'),
    videoId: 'h8SN9aB7J91Deb6',
    title: 'UNO: You Play! Interactive Video Game',
    thumbnail: '/placeholder.svg',
    views: 12500,
    category: 'Gaming',
    source: 'YouTube',
    privacy: 'Public',
    addedBy: 'avatar_Sharliya',
    approved: '1',
    duration: '15:30',
    createdAt: '2023-10-15T14:30:00Z'
  });

  // Form state for fake views
  const [viewsToAdd, setViewsToAdd] = useState<number>(1000);
  const [viewsSpeed, setViewsSpeed] = useState<'slow' | 'medium' | 'fast'>('medium');
  const [viewsPattern, setViewsPattern] = useState<'linear' | 'random' | 'burst'>('linear');
  const [enableRetention, setEnableRetention] = useState<boolean>(true);
  const [retentionRate, setRetentionRate] = useState<number>(65);
  const [geoTargeting, setGeoTargeting] = useState<string>('worldwide');
  const [scheduleViews, setScheduleViews] = useState<boolean>(false);
  const [scheduleDate, setScheduleDate] = useState<string>('');
  const [scheduleTime, setScheduleTime] = useState<string>('');

  // Speed options
  const speedOptions = {
    slow: { label: 'Slow (More Natural)', description: '100-200 views per hour' },
    medium: { label: 'Medium', description: '500-1000 views per hour' },
    fast: { label: 'Fast', description: '2000+ views per hour' }
  };

  // Pattern options
  const patternOptions = {
    linear: { label: 'Linear', description: 'Steady increase over time' },
    random: { label: 'Random', description: 'Fluctuating increases (more natural)' },
    burst: { label: 'Burst', description: 'Quick spike followed by gradual increase' }
  };

  // Geo-targeting options
  const geoOptions = [
    { value: 'worldwide', label: 'Worldwide' },
    { value: 'us', label: 'United States' },
    { value: 'europe', label: 'Europe' },
    { value: 'asia', label: 'Asia' },
    { value: 'custom', label: 'Custom' }
  ];

  // Load video data
  useEffect(() => {
    // In a real app, you would fetch the video data from an API
    // For now, we're using the mock data initialized above
  }, [id]);

  // Calculate estimated completion time
  const getEstimatedTime = (): string => {
    let viewsPerHour = 0;

    switch (viewsSpeed) {
      case 'slow':
        viewsPerHour = 150;
        break;
      case 'medium':
        viewsPerHour = 750;
        break;
      case 'fast':
        viewsPerHour = 2500;
        break;
    }

    const hours = viewsToAdd / viewsPerHour;

    if (hours < 1) {
      return `${Math.ceil(hours * 60)} minutes`;
    } else if (hours < 24) {
      return `${Math.ceil(hours)} hours`;
    } else {
      return `${Math.ceil(hours / 24)} days`;
    }
  };

  // Calculate price (mock function)
  const calculatePrice = (): number => {
    const baseRate = 0.002; // $0.002 per view
    const retentionMultiplier = enableRetention ? (retentionRate / 50) : 1;
    const speedMultiplier = viewsSpeed === 'slow' ? 1.2 : viewsSpeed === 'medium' ? 1 : 0.9;
    const geoMultiplier = geoTargeting === 'worldwide' ? 1 : 1.3;

    return parseFloat((viewsToAdd * baseRate * retentionMultiplier * speedMultiplier * geoMultiplier).toFixed(2));
  };

  // Handle start process
  const handleStartProcess = () => {
    setIsProcessing(true);
    setProgress(0);

    // Simulate progress
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsProcessing(false);

          // Update video views
          setVideo(prev => ({
            ...prev,
            views: prev.views + viewsToAdd
          }));

          return 100;
        }
        return prev + 5;
      });
    }, 500);
  };

  // Handle save configuration
  const handleSaveConfig = () => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      alert('Configuration saved successfully!');
    }, 1000);
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <div>
                <Button
                  variant="ghost"
                  className="mb-2"
                  onClick={() => navigate('/admin/videos/manage')}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Videos
                </Button>
                <h1 className="text-2xl font-bold">Add Fake Views</h1>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={() => navigate('/admin/videos/manage')}>
                  Cancel
                </Button>
                <Button onClick={handleSaveConfig} disabled={isLoading}>
                  {isLoading ? (
                    <>Saving...</>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Configuration
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/videos" className="text-muted-foreground hover:text-foreground">
                Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="/admin/videos/manage" className="text-muted-foreground hover:text-foreground">
                Manage Videos
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Add Fake Views</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Main content - 2/3 width on desktop */}
              <div className="md:col-span-2 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Fake Views Configuration</CardTitle>
                    <CardDescription>Configure how many views to add and how they should be distributed</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs value={activeTab} onValueChange={setActiveTab}>
                      <TabsList className="grid grid-cols-3 mb-6">
                        <TabsTrigger value="basic">Basic Settings</TabsTrigger>
                        <TabsTrigger value="advanced">Advanced Settings</TabsTrigger>
                        <TabsTrigger value="schedule">Schedule</TabsTrigger>
                      </TabsList>

                      <TabsContent value="basic" className="space-y-6">
                        {/* Number of Views */}
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <Label htmlFor="viewsToAdd" className="text-base">Number of Views to Add</Label>
                            <Input
                              id="viewsToAdd"
                              type="number"
                              value={viewsToAdd}
                              onChange={(e) => setViewsToAdd(parseInt(e.target.value) || 0)}
                              className="w-32 text-right"
                              min={100}
                              max={1000000}
                            />
                          </div>
                          <Slider
                            value={[viewsToAdd]}
                            onValueChange={(value) => setViewsToAdd(value[0])}
                            min={100}
                            max={100000}
                            step={100}
                            className="py-4"
                          />
                          <div className="flex justify-between text-xs text-muted-foreground">
                            <span>100</span>
                            <span>25,000</span>
                            <span>50,000</span>
                            <span>75,000</span>
                            <span>100,000</span>
                          </div>
                        </div>

                        <Separator />

                        {/* Views Speed */}
                        <div className="space-y-4">
                          <Label className="text-base">Views Speed</Label>
                          <RadioGroup value={viewsSpeed} onValueChange={(value: 'slow' | 'medium' | 'fast') => setViewsSpeed(value)}>
                            {Object.entries(speedOptions).map(([key, { label, description }]) => (
                              <div key={key} className="flex items-center space-x-2">
                                <RadioGroupItem value={key} id={`speed-${key}`} />
                                <Label htmlFor={`speed-${key}`} className="flex flex-col">
                                  <span>{label}</span>
                                  <span className="text-xs text-muted-foreground">{description}</span>
                                </Label>
                              </div>
                            ))}
                          </RadioGroup>
                        </div>

                        <Separator />

                        {/* Views Pattern */}
                        <div className="space-y-4">
                          <Label className="text-base">Views Pattern</Label>
                          <RadioGroup value={viewsPattern} onValueChange={(value: 'linear' | 'random' | 'burst') => setViewsPattern(value)}>
                            {Object.entries(patternOptions).map(([key, { label, description }]) => (
                              <div key={key} className="flex items-center space-x-2">
                                <RadioGroupItem value={key} id={`pattern-${key}`} />
                                <Label htmlFor={`pattern-${key}`} className="flex flex-col">
                                  <span>{label}</span>
                                  <span className="text-xs text-muted-foreground">{description}</span>
                                </Label>
                              </div>
                            ))}
                          </RadioGroup>
                        </div>
                      </TabsContent>

                      <TabsContent value="advanced" className="space-y-6">
                        {/* Retention Rate */}
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="enableRetention" className="text-base">Enable Retention</Label>
                            <Switch
                              id="enableRetention"
                              checked={enableRetention}
                              onCheckedChange={setEnableRetention}
                            />
                          </div>

                          {enableRetention && (
                            <div className="space-y-4 mt-4">
                              <div className="flex justify-between items-center">
                                <Label htmlFor="retentionRate">Retention Rate (%)</Label>
                                <span className="font-medium">{retentionRate}%</span>
                              </div>
                              <Slider
                                id="retentionRate"
                                value={[retentionRate]}
                                onValueChange={(value) => setRetentionRate(value[0])}
                                min={30}
                                max={95}
                                step={5}
                                className="py-4"
                              />
                              <div className="flex justify-between text-xs text-muted-foreground">
                                <span>30%</span>
                                <span>50%</span>
                                <span>70%</span>
                                <span>95%</span>
                              </div>
                              <p className="text-sm text-muted-foreground">
                                Higher retention rates appear more natural but cost more.
                                Retention measures how long viewers watch the video.
                              </p>
                            </div>
                          )}
                        </div>

                        <Separator />

                        {/* Geo-targeting */}
                        <div className="space-y-4">
                          <Label htmlFor="geoTargeting" className="text-base">Geo-targeting</Label>
                          <Select value={geoTargeting} onValueChange={setGeoTargeting}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select region" />
                            </SelectTrigger>
                            <SelectContent>
                              {geoOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <p className="text-sm text-muted-foreground">
                            Targeting specific regions may increase costs but provides more relevant engagement.
                          </p>
                        </div>

                        <Separator />

                        {/* Traffic Sources */}
                        <div className="space-y-4">
                          <Label className="text-base">Traffic Sources</Label>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="flex items-center space-x-2">
                              <Checkbox id="source-direct" checked />
                              <Label htmlFor="source-direct">Direct (40%)</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="source-search" checked />
                              <Label htmlFor="source-search">Search (25%)</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="source-suggested" checked />
                              <Label htmlFor="source-suggested">Suggested Videos (20%)</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox id="source-external" checked />
                              <Label htmlFor="source-external">External (15%)</Label>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Default distribution is optimized for natural appearance.
                          </p>
                        </div>
                      </TabsContent>

                      <TabsContent value="schedule" className="space-y-6">
                        {/* Schedule Views */}
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <Label htmlFor="scheduleViews" className="text-base">Schedule Views</Label>
                            <Switch
                              id="scheduleViews"
                              checked={scheduleViews}
                              onCheckedChange={setScheduleViews}
                            />
                          </div>

                          {scheduleViews && (
                            <div className="space-y-4 mt-4 grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="scheduleDate">Start Date</Label>
                                <Input
                                  id="scheduleDate"
                                  type="date"
                                  value={scheduleDate}
                                  onChange={(e) => setScheduleDate(e.target.value)}
                                  min={new Date().toISOString().split('T')[0]}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="scheduleTime">Start Time</Label>
                                <Input
                                  id="scheduleTime"
                                  type="time"
                                  value={scheduleTime}
                                  onChange={(e) => setScheduleTime(e.target.value)}
                                />
                              </div>
                            </div>
                          )}

                          {!scheduleViews && (
                            <Alert>
                              <Info className="h-4 w-4" />
                              <AlertTitle>Immediate Processing</AlertTitle>
                              <AlertDescription>
                                Views will start being added immediately after you click "Start Process".
                              </AlertDescription>
                            </Alert>
                          )}
                        </div>

                        <Separator />

                        {/* Distribution Over Time */}
                        <div className="space-y-4">
                          <Label className="text-base">Distribution Over Time</Label>
                          <div className="h-40 bg-muted rounded-md flex items-center justify-center">
                            <div className="text-center text-muted-foreground">
                              <BarChart2 className="h-10 w-10 mx-auto mb-2" />
                              <p>Views distribution chart</p>
                              <p className="text-xs">(Based on selected pattern)</p>
                            </div>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            This chart shows how views will be distributed over time based on your selected pattern.
                          </p>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t pt-6">
                    <div>
                      <p className="text-sm text-muted-foreground">Estimated completion time: <span className="font-medium">{getEstimatedTime()}</span></p>
                    </div>
                    <Button
                      onClick={handleStartProcess}
                      disabled={isProcessing || viewsToAdd <= 0 || (scheduleViews && (!scheduleDate || !scheduleTime))}
                    >
                      {isProcessing ? (
                        <>
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <TrendingUp className="mr-2 h-4 w-4" />
                          Start Process
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>

                {isProcessing && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Processing Views</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Progress</span>
                          <span>{progress}%</span>
                        </div>
                        <Progress value={progress} className="h-2" />
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Views added: {Math.floor((viewsToAdd * progress) / 100)}</span>
                          <span>Remaining: {viewsToAdd - Math.floor((viewsToAdd * progress) / 100)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Sidebar - 1/3 width on desktop */}
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Video Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="aspect-video w-full overflow-hidden rounded-md bg-muted mb-4">
                      {video.thumbnail ? (
                        <img
                          src={video.thumbnail}
                          alt="Video thumbnail"
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <p className="text-muted-foreground">No thumbnail</p>
                        </div>
                      )}
                    </div>
                    <h3 className="font-medium mb-2">{video.title}</h3>
                    <div className="flex justify-between items-center mb-4">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm">{video.views.toLocaleString()} views</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm">{video.duration || 'Unknown'}</span>
                      </div>
                    </div>
                    <Separator className="my-4" />
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Category:</span>
                        <span className="text-sm font-medium">{video.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Source:</span>
                        <span className="text-sm font-medium">{video.source}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Added on:</span>
                        <span className="text-sm font-medium">
                          {video.createdAt
                            ? new Date(video.createdAt).toLocaleDateString()
                            : 'Unknown'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Order Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-sm">Views:</span>
                        <span className="text-sm font-medium">{viewsToAdd.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Speed:</span>
                        <span className="text-sm font-medium">{speedOptions[viewsSpeed].label}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm">Pattern:</span>
                        <span className="text-sm font-medium">{patternOptions[viewsPattern].label}</span>
                      </div>
                      {enableRetention && (
                        <div className="flex justify-between">
                          <span className="text-sm">Retention Rate:</span>
                          <span className="text-sm font-medium">{retentionRate}%</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-sm">Geo-targeting:</span>
                        <span className="text-sm font-medium">
                          {geoOptions.find(opt => opt.value === geoTargeting)?.label || 'Worldwide'}
                        </span>
                      </div>
                      <Separator />
                      <div className="flex justify-between font-medium">
                        <span>Total Price:</span>
                        <span className="text-primary">${calculatePrice()}</span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Price is calculated based on the number of views and selected options.
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Alert className="w-full">
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>Important Note</AlertTitle>
                      <AlertDescription className="text-xs">
                        Adding fake views may violate platform terms of service.
                        Use at your own risk. We recommend using natural patterns and
                        retention rates to minimize detection risk.
                      </AlertDescription>
                    </Alert>
                  </CardFooter>
                </Card>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
