import axios from 'axios';
import { AppError, createInternalServerError } from '../utils/error';
import { bhashiniApiService } from './bhashini-api.service';
import { VideoModel } from '../models';

/**
 * AI Assistant service for handling AI-related operations
 */
export class AIAssistantService {
  /**
   * Send a message to the AI Assistant
   */
  async sendMessage(data: {
    message: string;
    provider: string;
    apiKey: string;
    endpoint?: string;
    model?: string; // Added model parameter for providers that support multiple models
    conversationId?: string;
    creatorId: string;
    creatorName?: string; // Added creator name for personalized responses
    videoId?: string;
    chatHistory?: Array<{
      role: 'user' | 'assistant';
      content: string;
    }>;
  }) {
    try {
      let response;

      // Format chat history for the API request
      const formattedHistory = data.chatHistory || [];

      // Add the current message to the history
      formattedHistory.push({
        role: 'user',
        content: data.message
      });

      // Use creator name if provided, otherwise use a generic fallback
      const creatorName = data.creatorName || 'Video Creator';

      // Get video information if videoId is provided
      let videoInfo = null;
      let videoContent = '';
      if (data.videoId) {
        try {
          console.log(`Fetching video info for videoId: ${data.videoId}`);
          const video = await VideoModel.findOne({
            $or: [
              { id: data.videoId },
              { url: data.videoId }
            ]
          });

          if (video) {
            videoInfo = {
              title: video.title,
              description: video.description,
              category: video.category,
              tags: video.tags,
              captions: video.captions
            };

            // Build comprehensive video content for context
            videoContent = `Video Title: ${video.title}\n`;
            if (video.description) {
              videoContent += `Description: ${video.description}\n`;
            }
            if (video.category) {
              videoContent += `Category: ${video.category}\n`;
            }
            if (video.tags && video.tags.length > 0) {
              videoContent += `Tags: ${video.tags.join(', ')}\n`;
            }

            console.log('Found video info:', videoInfo);
            console.log('Video content for context:', videoContent);
          } else {
            console.log(`No video found with ID: ${data.videoId}`);
          }
        } catch (videoError) {
          console.error('Error fetching video info:', videoError);
        }
      }

      console.log(`Processing message with provider: ${data.provider}`);

      try {
        switch (data.provider) {
          case 'openai':
            response = await this.callOpenAI(data.apiKey, formattedHistory, data.creatorId, creatorName, videoContent);
            break;
          case 'deepseek':
            response = await this.callDeepSeek(data.apiKey, formattedHistory, data.creatorId, creatorName, videoContent);
            break;
          case 'bhashini':
            response = await this.callBhashini(data.apiKey, formattedHistory, data.creatorId, creatorName, videoContent);
            break;
          case 'openrouter':
            response = await this.callOpenRouter(data.apiKey, formattedHistory, data.creatorId, creatorName, data.model, videoContent);
            break;
          case 'custom':
            if (!data.endpoint) {
              throw new AppError('Custom endpoint URL is required for custom provider', 400);
            }
            response = await this.callCustomAPI(data.endpoint, data.apiKey, formattedHistory, data.creatorId, creatorName, videoContent);
            break;
          default:
            // Instead of throwing an error, use a fallback provider
            console.warn(`Unsupported provider: ${data.provider}, falling back to simulated response`);
            response = {
              content: `I'm sorry, but I couldn't process your request with the selected provider. Please try again or contact support.`
            };
        }
      } catch (providerError) {
        console.error(`Error with provider ${data.provider}:`, providerError);
        // Return a fallback response instead of throwing
        response = {
          content: `I apologize, but I encountered an issue while processing your request. Please try again later.`
        };
      }

      // Ensure we have a valid response format
      const responseContent = response?.content || "I'm here to help! What would you like to know?";
      const conversationId = data.conversationId || `conv_${Date.now()}`;
      const timestamp = new Date().toISOString();

      console.log('Returning AI assistant response:', {
        content: responseContent,
        conversationId,
        timestamp
      });

      return {
        response: responseContent,
        content: responseContent, // Include both response and content fields for compatibility
        conversationId: conversationId,
        timestamp: timestamp
      };
    } catch (error: any) {
      console.error('Unexpected error in chat service:', error);

      // Instead of throwing an error, return a fallback response
      const fallbackResponse = `I'm sorry, but I couldn't process your message at this time. Please try again later.`;
      const conversationId = data.conversationId || `conv_${Date.now()}`;
      const timestamp = new Date().toISOString();

      console.log('Returning fallback response due to error:', {
        content: fallbackResponse,
        conversationId,
        timestamp
      });

      return {
        response: fallbackResponse,
        content: fallbackResponse, // Include both response and content fields for compatibility
        conversationId: conversationId,
        timestamp: timestamp
      };
    }
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string,
    videoContent?: string
  ) {
    try {
      // Check if we have video content to restrict responses
      if (!videoContent || !videoContent.trim()) {
        return {
          content: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."
        };
      }

      // In a real implementation, we would call the OpenAI API with video content context
      // For now, we'll simulate a response that acknowledges video content restriction
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        content: `I'm happy to help with questions about this video! What would you like to know about the content?`
      };
    } catch (error: any) {
      console.error('Error calling OpenAI API:', error);
      throw error;
    }
  }

  /**
   * Call DeepSeek API
   */
  private async callDeepSeek(
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string,
    videoContent?: string
  ) {
    try {
      // Check if we have video content to restrict responses
      if (!videoContent || !videoContent.trim()) {
        return {
          content: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."
        };
      }

      // In a real implementation, we would call the DeepSeek API with video content context
      // For now, we'll simulate a response that acknowledges video content restriction
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        content: `Thanks for your message about this video! How can I assist you with the video content?`
      };
    } catch (error: any) {
      console.error('Error calling DeepSeek API:', error);
      throw error;
    }
  }

  /**
   * Call Bhashini API
   */
  private async callBhashini(
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string,
    videoContent?: string
  ) {
    try {
      console.log('Calling Bhashini API with messages:', JSON.stringify(messages, null, 2));

      // Check if we have video content to restrict responses
      if (!videoContent || !videoContent.trim()) {
        return {
          content: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."
        };
      }

      // Extract userId from apiKey if it's in the format "userId:ulcaApiKey"
      let userId = '';
      let ulcaApiKey = '';

      if (apiKey.includes(':')) {
        const parts = apiKey.split(':');
        userId = parts[0];
        ulcaApiKey = parts[1];
      } else {
        // If no colon, assume apiKey is the userId
        userId = apiKey;
      }

      console.log(`Using Bhashini API with userId: ${userId.substring(0, 5)}... and ulcaApiKey: ${ulcaApiKey ? ulcaApiKey.substring(0, 5) + '...' : 'none'}`);

      // Add video content context to the messages for Bhashini
      const contextualizedMessages = [...messages];
      if (contextualizedMessages.length > 0) {
        const lastMessage = contextualizedMessages[contextualizedMessages.length - 1];
        lastMessage.content = `Video Context: ${videoContent}\n\nUser Question: ${lastMessage.content}\n\nPlease only answer if the question is related to the video content above. If not related, respond with: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."`;
      }

      // Process the chat messages using Bhashini API
      const response = await bhashiniApiService.processChatMessages(
        contextualizedMessages,
        apiKey,
        userId,
        ulcaApiKey
      );

      // Format the response with creator personalization
      const personalizedResponse = `${creatorName}: ${response}`;

      return {
        content: personalizedResponse
      };
    } catch (error: any) {
      console.error('Error calling Bhashini API:', error);

      // Fall back to simulated response if API call fails
      return {
        content: `Sorry, this question is not related to the video content. I can only answer questions about this specific video.`
      };
    }
  }

  /**
   * Call OpenRouter API
   */
  private async callOpenRouter(
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string,
    model?: string,
    videoContent?: string
  ) {
    try {
      console.log('Starting OpenRouter API call with API key:', apiKey ? `${apiKey.substring(0, 5)}...${apiKey.substring(apiKey.length - 5)}` : 'none');

      // Validate API key
      if (!apiKey || apiKey.trim() === '') {
        console.error('OpenRouter API key is missing');
        // Instead of throwing an error, return a simulated response
        return {
          content: `I'm sorry, but I'm having trouble connecting to the service. Could you please try again in a moment?`
        };
      }

      // Check if API key has the correct format (should start with sk-or- or sk-proj-)
      if (!apiKey.startsWith('sk-or-') && !apiKey.startsWith('sk-proj-')) {
        console.error('Invalid API key format');
        // Instead of throwing an error, return a simulated response
        return {
          content: `I'm sorry, but there seems to be an issue with the service configuration. Please try again later.`
        };
      }

      // Format messages for OpenRouter API
      // Create a new array for OpenRouter that can include the 'system' role
      const openRouterMessages: Array<{ role: string; content: string }> = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Create system prompt with video content context and restrictions
      let systemPrompt = `You are ${creatorName}, a content creator on LawEngaxe. `;

      if (videoContent && videoContent.trim()) {
        systemPrompt += `You are responding about your specific video with the following content:

${videoContent}

IMPORTANT INSTRUCTIONS:
1. ONLY answer questions that are directly related to the content of this specific video
2. If a user asks about anything NOT related to this video's content, respond EXACTLY with: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."
3. Base your answers only on the video information provided above
4. Be helpful and knowledgeable about YOUR video content
5. Do not mention that you are an AI
6. Keep responses concise and focused on the video content`;
      } else {
        systemPrompt += `You can only answer questions about the specific video content. If no video content is available or if the user asks about anything not related to the video, respond with: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."`;
      }

      // Add system message to personalize responses with instruction for concise answers
      systemPrompt += ` Always provide concise, summary-style responses (2-3 sentences maximum).`;

      openRouterMessages.unshift({
        role: 'system',
        content: systemPrompt
      });

      console.log('Formatted messages for OpenRouter:', JSON.stringify(openRouterMessages, null, 2));

      // Call OpenRouter API
      console.log(`Calling OpenRouter API with model: ${model || 'anthropic/claude-3-haiku'}`);

      try {
        // Determine the API endpoint based on the API key format
        const apiEndpoint = apiKey.startsWith('sk-or-')
          ? 'https://openrouter.ai/api/v1/chat/completions'
          : 'https://api.openai.com/v1/chat/completions';

        // Determine the model based on the API key format
        const apiModel = apiKey.startsWith('sk-or-')
          ? (model || 'anthropic/claude-3-haiku') // OpenRouter model
          : (model || 'gpt-3.5-turbo'); // OpenAI model

        console.log(`Using API endpoint: ${apiEndpoint}`);
        console.log(`Using model: ${apiModel}`);

        const response = await axios.post(
          apiEndpoint,
          {
            model: apiModel,
            messages: openRouterMessages,
            max_tokens: 150,
            temperature: 0.7
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`,
              'HTTP-Referer': 'http://localhost:3001', // Local development URL
              'X-Title': 'LawEngaxe Creator Chat',
              'User-Agent': 'LawEngaxe/1.0'
            },
            timeout: 30000 // 30 second timeout
          }
        );

        console.log('OpenRouter API response:', response.data);

        // Extract the response content
        if (response.data && response.data.choices && response.data.choices.length > 0) {
          return {
            content: response.data.choices[0].message.content
          };
        } else {
          console.error('Invalid response format from OpenRouter API');
          // Return a fallback response instead of throwing an error
          return {
            content: `I'm sorry, but I received an unexpected response from the service. Please try again.`
          };
        }
      } catch (apiError: any) {
        console.error('Error in OpenRouter API call:', apiError.message);

        // Log more detailed error information
        if (apiError.response) {
          console.error('OpenRouter API error response data:', apiError.response.data);
          console.error('OpenRouter API error response status:', apiError.response.status);
        }

        // Return a fallback response instead of throwing an error
        return {
          content: `I'm sorry, but I encountered an issue connecting to the service. Please try again later.`
        };
      }
    } catch (error: any) {
      console.error('Unexpected error in callOpenRouter:', error);

      // Always return a response instead of throwing an error
      return {
        content: `I'm sorry for the inconvenience. There was an unexpected error. Please try again.`
      };
    }
  }

  /**
   * Call Custom API
   */
  private async callCustomAPI(
    endpoint: string,
    apiKey: string,
    messages: Array<{ role: 'user' | 'assistant'; content: string }>,
    creatorId: string,
    creatorName: string,
    videoContent?: string
  ) {
    try {
      // Check if we have video content to restrict responses
      if (!videoContent || !videoContent.trim()) {
        return {
          content: "Sorry, this question is not related to the video content. I can only answer questions about this specific video."
        };
      }

      // In a real implementation, we would call the custom API with video content context
      // For now, we'll simulate a response that acknowledges video content restriction
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        content: `I'd be happy to help answer your questions about this video. What would you like to know about the content?`
      };
    } catch (error: any) {
      console.error('Error calling Custom API:', error);
      throw error;
    }
  }

  /**
   * Helper function to generate creator responses
   */
  private simulateResponse(message: string): string {
    // Simple response generation based on message content
    if (message.toLowerCase().includes('hello') || message.toLowerCase().includes('hi')) {
      return "Hello! How can I help you today?";
    }

    if (message.toLowerCase().includes('thank')) {
      return "You're welcome! Feel free to ask if you have any other questions.";
    }

    if (message.toLowerCase().includes('how') && message.toLowerCase().includes('work')) {
      return "That's a great question. Understanding the core concepts and applying them correctly is essential for success in this area.";
    }

    if (message.toLowerCase().includes('explain')) {
      return "I'd be happy to explain that further. This topic is based on several key principles that work together to achieve the desired result.";
    }

    if (message.toLowerCase().includes('when') || message.toLowerCase().includes('time')) {
      return "The timing depends on several factors. Generally, it takes about 2-3 weeks to see results, but it can vary based on your specific situation.";
    }

    // Default response for other messages
    return "I'd be happy to help answer your questions. Could you please provide more details about what you'd like to know?";
  }
}

export const aiAssistantService = new AIAssistantService();
