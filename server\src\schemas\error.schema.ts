import { Type } from '@sinclair/typebox';

/**
 * Standard error response schema for Swagger documentation
 */
export const ErrorResponseSchema = Type.Object({
  success: Type.Boolean({ default: false }),
  error: Type.Object({
    message: Type.String(),
    code: Type.String(),
    data: Type.Optional(Type.Any()),
  }),
});

/**
 * Standard success response schema for Swagger documentation
 */
export const SuccessResponseSchema = Type.Object({
  success: Type.Boolean({ default: true }),
  message: Type.String(),
});

/**
 * Pagination response schema for Swagger documentation
 */
export const PaginationSchema = Type.Object({
  page: Type.Number(),
  limit: Type.Number(),
  total: Type.Number(),
  pages: Type.Number(),
});
