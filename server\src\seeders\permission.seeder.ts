import { PermissionModel } from '../models';

/**
 * Seed permissions data
 */
async function seedPermissions() {
  try {
    // Clear existing permissions
    await PermissionModel.deleteMany({});

    // Define permissions to seed
    const permissions = [
      // User permissions
      {
        name: 'Create User',
        description: 'Ability to create new user accounts',
        code: 'user:create',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'create',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Read User',
        description: 'Ability to view user account details',
        code: 'user:read',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'read',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Update User',
        description: 'Ability to update user account details',
        code: 'user:update',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'update',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Delete User',
        description: 'Ability to delete user accounts',
        code: 'user:delete',
        category: 'users',
        isActive: true,
        resourceType: 'user',
        action: 'delete',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },

      // Video permissions
      {
        name: 'Create Video',
        description: 'Ability to upload new videos',
        code: 'video:create',
        category: 'content',
        isActive: true,
        resourceType: 'video',
        action: 'create',
        scope: 'own',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Read Video',
        description: 'Ability to view videos',
        code: 'video:read',
        category: 'content',
        isActive: true,
        resourceType: 'video',
        action: 'read',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Update Video',
        description: 'Ability to update video details',
        code: 'video:update',
        category: 'content',
        isActive: true,
        resourceType: 'video',
        action: 'update',
        scope: 'own',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Delete Video',
        description: 'Ability to delete videos',
        code: 'video:delete',
        category: 'content',
        isActive: true,
        resourceType: 'video',
        action: 'delete',
        scope: 'own',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Moderate Video',
        description: 'Ability to moderate videos (approve/reject)',
        code: 'video:moderate',
        category: 'content',
        isActive: true,
        resourceType: 'video',
        action: 'moderate',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },

      // Message permissions
      {
        name: 'Send Message',
        description: 'Ability to send messages',
        code: 'message:create',
        category: 'messaging',
        isActive: true,
        resourceType: 'message',
        action: 'create',
        scope: 'own',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Read Message',
        description: 'Ability to read messages',
        code: 'message:read',
        category: 'messaging',
        isActive: true,
        resourceType: 'message',
        action: 'read',
        scope: 'own',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Delete Message',
        description: 'Ability to delete messages',
        code: 'message:delete',
        category: 'messaging',
        isActive: true,
        resourceType: 'message',
        action: 'delete',
        scope: 'own',
        createdBy: 'system',
        updatedBy: 'system',
      },

      // System permissions
      {
        name: 'Manage System Configuration',
        description: 'Ability to view and update system configuration',
        code: 'system:manage',
        category: 'system',
        isActive: true,
        resourceType: 'system',
        action: 'manage',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'View Analytics',
        description: 'Ability to view system analytics',
        code: 'analytics:read',
        category: 'system',
        isActive: true,
        resourceType: 'analytics',
        action: 'read',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },

      // Log permissions
      {
        name: 'Create Log',
        description: 'Ability to create log entries',
        code: 'log:create',
        category: 'system',
        isActive: true,
        resourceType: 'log',
        action: 'create',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Read Log',
        description: 'Ability to view log entries',
        code: 'log:read',
        category: 'system',
        isActive: true,
        resourceType: 'log',
        action: 'read',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
      {
        name: 'Delete Log',
        description: 'Ability to delete log entries',
        code: 'log:delete',
        category: 'system',
        isActive: true,
        resourceType: 'log',
        action: 'delete',
        scope: 'global',
        createdBy: 'system',
        updatedBy: 'system',
      },
    ];

    // Insert permissions
    await PermissionModel.insertMany(permissions);

    console.log(`✅ ${permissions.length} permissions seeded successfully`);
  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
    throw error;
  }
}

export default seedPermissions;
