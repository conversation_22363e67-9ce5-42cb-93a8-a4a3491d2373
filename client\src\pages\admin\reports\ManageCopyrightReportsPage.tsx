import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Home,
  ChevronRight,
  ArrowUpDown,
  Check,
  Eye,
  Trash2,
  Search,
  Filter,
  AlertCircle,
  Flag,
  Video,
  Calendar,
  User,
  MoreHorizontal,
  Shield,
  ShieldAlert,
  ShieldCheck,
  ShieldX,
  FileText,
  Copyright,
  Mail,
  ExternalLink,
  Send
} from 'lucide-react';
import AdminSidebar from '@/components/admin/AdminSidebar';
import AdminHeader from '@/components/admin/AdminHeader';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';

// Mock data for copyright reports
const mockReports = [
  {
    id: '1',
    claimant: 'Universal Media Group',
    email: '<EMAIL>',
    reported: '2024-August-26',
    video: 'Music Tutorial',
    videoId: 'vid-12345',
    contentType: 'Music',
    description: 'This video contains copyrighted music owned by Universal Media Group without proper licensing.',
    status: 'pending'
  },
  {
    id: '2',
    claimant: 'Educational Publishing Inc.',
    email: '<EMAIL>',
    reported: '2024-August-25',
    video: 'Advanced Mathematics Course',
    videoId: 'vid-67890',
    contentType: 'Educational Content',
    description: 'This video contains copyrighted educational material from our textbooks without permission.',
    status: 'pending'
  },
  {
    id: '3',
    claimant: 'Software Solutions Ltd',
    email: '<EMAIL>',
    reported: '2024-August-24',
    video: 'Programming Tutorial',
    videoId: 'vid-24680',
    contentType: 'Software Tutorial',
    description: 'This video demonstrates our proprietary software without authorization.',
    status: 'resolved'
  }
];

export default function ManageCopyrightReportsPage() {
  const [reports, setReports] = useState(mockReports);
  const [selectedReports, setSelectedReports] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string | null>('id');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [action, setAction] = useState('Mark Resolved');
  const [currentPage, setCurrentPage] = useState(1);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isRespondDialogOpen, setIsRespondDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState<any>(null);
  const [responseMessage, setResponseMessage] = useState('');

  // Show success alert
  const displaySuccessAlert = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessAlert(true);
    setTimeout(() => setShowSuccessAlert(false), 3000);
  };

  // Filter reports based on search term and status
  const filteredReports = reports.filter(report => {
    const matchesSearch =
      report.claimant.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.video.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      report.contentType.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      activeTab === 'all' ? true :
      activeTab === 'pending' ? report.status === 'pending' :
      activeTab === 'resolved' ? report.status === 'resolved' : true;

    return matchesSearch && matchesStatus;
  });

  // Sort reports
  const sortedReports = [...filteredReports].sort((a, b) => {
    if (!sortField) return 0;

    const fieldA = a[sortField as keyof typeof a];
    const fieldB = b[sortField as keyof typeof b];

    if (fieldA < fieldB) return sortDirection === 'asc' ? -1 : 1;
    if (fieldA > fieldB) return sortDirection === 'asc' ? 1 : -1;
    return 0;
  });

  // Handle sort
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Handle report selection
  const handleSelectReport = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedReports([...selectedReports, id]);
    } else {
      setSelectedReports(selectedReports.filter(reportId => reportId !== id));
    }
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedReports(sortedReports.map(report => report.id));
    } else {
      setSelectedReports([]);
    }
  };

  // Handle mark resolved
  const handleMarkResolved = (id: string) => {
    setReports(reports.map(report =>
      report.id === id ? { ...report, status: 'resolved' } : report
    ));
    displaySuccessAlert('Copyright claim marked as resolved successfully');
  };

  // Handle delete
  const handleDelete = (id: string) => {
    if (window.confirm('Are you sure you want to delete this copyright claim?')) {
      setReports(reports.filter(report => report.id !== id));
      setSelectedReports(selectedReports.filter(reportId => reportId !== id));
      displaySuccessAlert('Copyright claim deleted successfully');
    }
  };

  // Handle bulk action
  const handleBulkAction = () => {
    if (selectedReports.length === 0) return;

    if (action === 'Mark Resolved') {
      setReports(reports.map(report =>
        selectedReports.includes(report.id) ? { ...report, status: 'resolved' } : report
      ));
      displaySuccessAlert(`${selectedReports.length} copyright claims marked as resolved`);
    } else if (action === 'Delete') {
      if (window.confirm('Are you sure you want to delete the selected copyright claims?')) {
        setReports(reports.filter(report => !selectedReports.includes(report.id)));
        setSelectedReports([]);
        displaySuccessAlert(`${selectedReports.length} copyright claims deleted successfully`);
      }
    }
  };

  // Handle view report
  const handleViewReport = (report: any) => {
    setSelectedReport(report);
    setIsViewDialogOpen(true);
  };

  // Handle respond to claim
  const handleRespondToClaim = (report: any) => {
    setSelectedReport(report);
    setIsRespondDialogOpen(true);
  };

  // Handle send response
  const handleSendResponse = () => {
    if (!responseMessage.trim()) {
      alert('Please enter a response message');
      return;
    }

    // In a real app, this would send an email to the claimant
    console.log('Sending response to:', selectedReport.email);
    console.log('Response message:', responseMessage);

    // Mark as resolved
    handleMarkResolved(selectedReport.id);

    // Close dialog
    setIsRespondDialogOpen(false);
    setResponseMessage('');

    displaySuccessAlert('Response sent and claim marked as resolved');
  };

  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(sortedReports.length / itemsPerPage);
  const paginatedReports = sortedReports.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Pending</Badge>;
      case 'resolved':
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Resolved</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="flex h-screen bg-background overflow-hidden">
      <AdminSidebar />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader />
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-4">
              <h1 className="text-2xl font-bold">Manage Copyright Reports</h1>
            </div>

            {/* Breadcrumb */}
            <div className="flex items-center text-sm mb-6">
              <Link to="/admin" className="text-muted-foreground hover:text-foreground flex items-center">
                <Home className="h-4 w-4 mr-2" />
                Admin Panel
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <Link to="#" className="text-muted-foreground hover:text-foreground">
                Reports
              </Link>
              <span className="mx-2 text-muted-foreground"><ChevronRight className="h-4 w-4" /></span>
              <span className="text-primary">Manage Copyright Reports</span>
            </div>

            {showSuccessAlert && (
              <Alert className="mb-6 bg-green-50 text-green-800 border-green-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>
                  {successMessage}
                </AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Copyright Claims</CardTitle>
                    <CardDescription>Manage copyright claims against videos on your platform</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="all" className="w-full mb-6" value={activeTab} onValueChange={setActiveTab}>
                  <TabsList>
                    <TabsTrigger value="all" className="flex items-center gap-2">
                      <Copyright className="h-4 w-4" />
                      All Claims
                    </TabsTrigger>
                    <TabsTrigger value="pending" className="flex items-center gap-2">
                      <ShieldAlert className="h-4 w-4" />
                      Pending
                    </TabsTrigger>
                    <TabsTrigger value="resolved" className="flex items-center gap-2">
                      <ShieldCheck className="h-4 w-4" />
                      Resolved
                    </TabsTrigger>
                  </TabsList>
                </Tabs>

                <div className="flex flex-col md:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search claims by claimant, video, or content type..."
                      className="pl-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  <div className="flex gap-2">
                    <Select
                      value={action}
                      onValueChange={setAction}
                    >
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select action" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Mark Resolved">Mark Resolved</SelectItem>
                        <SelectItem value="Delete">Delete</SelectItem>
                      </SelectContent>
                    </Select>

                    <Button
                      onClick={handleBulkAction}
                      disabled={selectedReports.length === 0}
                      className="gap-2"
                    >
                      Apply
                    </Button>
                  </div>
                </div>

                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedReports.length === sortedReports.length && sortedReports.length > 0}
                            onCheckedChange={(checked) => handleSelectAll(checked as boolean)}
                          />
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('id')}>
                          <div className="flex items-center">
                            ID
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            CLAIMANT
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer" onClick={() => handleSort('reported')}>
                          <div className="flex items-center">
                            REPORTED
                            <ArrowUpDown className="ml-1 h-4 w-4 opacity-50" />
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            VIDEO
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            CONTENT TYPE
                          </div>
                        </TableHead>
                        <TableHead>
                          <div className="flex items-center">
                            STATUS
                          </div>
                        </TableHead>
                        <TableHead className="text-right">ACTION</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paginatedReports.length > 0 ? (
                        paginatedReports.map((report) => (
                          <TableRow key={report.id}>
                            <TableCell>
                              <Checkbox
                                checked={selectedReports.includes(report.id)}
                                onCheckedChange={(checked) => handleSelectReport(report.id, checked as boolean)}
                              />
                            </TableCell>
                            <TableCell className="font-medium">{report.id}</TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-muted-foreground" />
                                {report.claimant}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4 text-muted-foreground" />
                                {report.reported}
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Video className="h-4 w-4 text-muted-foreground" />
                                <span className="truncate max-w-[150px]">{report.video}</span>
                              </div>
                            </TableCell>
                            <TableCell>
                              <span className="truncate max-w-[150px]">{report.contentType}</span>
                            </TableCell>
                            <TableCell>
                              {getStatusBadge(report.status)}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" className="h-8 w-8">
                                    <MoreHorizontal className="h-4 w-4" />
                                    <span className="sr-only">Open menu</span>
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="flex items-center gap-2 cursor-pointer"
                                    onClick={() => handleViewReport(report)}
                                  >
                                    <Eye className="h-4 w-4" />
                                    View Details
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="flex items-center gap-2 cursor-pointer"
                                    onClick={() => handleRespondToClaim(report)}
                                  >
                                    <Mail className="h-4 w-4" />
                                    Respond to Claim
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="flex items-center gap-2 text-green-600 cursor-pointer"
                                    onClick={() => handleMarkResolved(report.id)}
                                  >
                                    <Check className="h-4 w-4" />
                                    Mark Resolved
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="flex items-center gap-2 text-red-600 cursor-pointer"
                                    onClick={() => handleDelete(report.id)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                            <div className="flex flex-col items-center justify-center py-4">
                              <Copyright className="h-10 w-10 text-muted-foreground mb-2 opacity-20" />
                              <p>No copyright claims found</p>
                              <p className="text-sm text-muted-foreground">Try adjusting your filters or search term</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination */}
                {sortedReports.length > 0 && (
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-sm text-muted-foreground">
                      Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, sortedReports.length)} of {sortedReports.length} entries
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(1)}
                        disabled={currentPage === 1}
                      >
                        First
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Previous
                      </Button>
                      <div className="text-sm">
                        Page <span className="font-medium">{currentPage}</span> of <span className="font-medium">{totalPages}</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                      >
                        Next
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(totalPages)}
                        disabled={currentPage === totalPages}
                      >
                        Last
                      </Button>
                    </div>
                  </div>
                )}

                {selectedReports.length > 0 && (
                  <div className="flex items-center justify-between bg-muted/30 p-3 rounded-md mt-4">
                    <div className="text-sm">
                      <span className="font-medium">{selectedReports.length}</span> {selectedReports.length === 1 ? 'claim' : 'claims'} selected
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="gap-2"
                        onClick={() => {
                          setReports(reports.map(report =>
                            selectedReports.includes(report.id) ? { ...report, status: 'resolved' } : report
                          ));
                          displaySuccessAlert(`${selectedReports.length} claims marked as resolved`);
                        }}
                      >
                        <Check className="h-4 w-4" />
                        Mark Selected Resolved
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        className="gap-2"
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete the selected claims?')) {
                            setReports(reports.filter(report => !selectedReports.includes(report.id)));
                            setSelectedReports([]);
                            displaySuccessAlert(`${selectedReports.length} claims deleted successfully`);
                          }
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                        Delete Selected
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* View Report Dialog */}
            <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Copyright Claim Details</DialogTitle>
                  <DialogDescription>
                    Detailed information about the copyright claim
                  </DialogDescription>
                </DialogHeader>
                {selectedReport && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Claim ID</h3>
                        <p>{selectedReport.id}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Status</h3>
                        <p>{getStatusBadge(selectedReport.status)}</p>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Claimant</h3>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <p>{selectedReport.claimant}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Contact Email</h3>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <p>{selectedReport.email}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Reported On</h3>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <p>{selectedReport.reported}</p>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Video</h3>
                      <div className="flex items-center gap-2">
                        <Video className="h-4 w-4 text-muted-foreground" />
                        <p>{selectedReport.video}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Video ID</h3>
                      <p>{selectedReport.videoId}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Content Type</h3>
                      <p>{selectedReport.contentType}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Claim Description</h3>
                      <p className="text-sm">{selectedReport.description}</p>
                    </div>
                  </div>
                )}
                <DialogFooter className="flex justify-between items-center">
                  <Button
                    variant="destructive"
                    onClick={() => {
                      handleDelete(selectedReport.id);
                      setIsViewDialogOpen(false);
                    }}
                    className="gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete Claim
                  </Button>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setIsViewDialogOpen(false)}
                    >
                      Close
                    </Button>
                    <Button
                      onClick={() => {
                        setIsViewDialogOpen(false);
                        handleRespondToClaim(selectedReport);
                      }}
                      className="gap-2"
                    >
                      <Mail className="h-4 w-4" />
                      Respond
                    </Button>
                  </div>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {/* Respond to Claim Dialog */}
            <Dialog open={isRespondDialogOpen} onOpenChange={setIsRespondDialogOpen}>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Respond to Copyright Claim</DialogTitle>
                  <DialogDescription>
                    Send a response to the copyright claimant
                  </DialogDescription>
                </DialogHeader>
                {selectedReport && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Claimant</h3>
                        <p>{selectedReport.claimant}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Email</h3>
                        <p>{selectedReport.email}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Video</h3>
                      <p>{selectedReport.video}</p>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Response Message</h3>
                      <Textarea
                        placeholder="Enter your response to the copyright claim..."
                        className="min-h-[150px]"
                        value={responseMessage}
                        onChange={(e) => setResponseMessage(e.target.value)}
                      />
                      <p className="text-xs text-muted-foreground">
                        This message will be sent to the claimant's email address.
                      </p>
                    </div>
                  </div>
                )}
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsRespondDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSendResponse}
                    className="gap-2"
                  >
                    <Send className="h-4 w-4" />
                    Send Response
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </main>
      </div>
    </div>
  );
}
